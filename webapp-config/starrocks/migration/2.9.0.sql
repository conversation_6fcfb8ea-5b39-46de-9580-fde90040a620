SET global time_zone = 'Asia/Shanghai' ;

SET PASSWORD = PASSWORD('Databuff@123');

CREATE DATABASE IF NOT EXISTS databuff;

use databuff;

-- event_2_8_6 表结构优化
DROP TABLE IF EXISTS databuff.`dc_event_2_8_6`;
SELECT sleep(10);
BEGIN;
CREATE TABLE IF NOT EXISTS `dc_event_2_8_6` (
  `triggerTime` bigint(20) NULL COMMENT "本次触发时间(异常点)",
  `id` varchar(50) NOT NULL COMMENT "事件ID",
  `createDt` date NOT NULL COMMENT "生命周期TTL分区键",
  `gid` varchar(32) NULL COMMENT "域ID",
  `monitorId` varchar(50) NULL COMMENT "监控规则ID",
  `value` double NULL COMMENT "值",
  `silence` tinyint(4) NULL COMMENT "是否静默",
  `level` tinyint(4) NULL COMMENT "事件最终等级",
  `creatorId` varchar(50) NULL COMMENT "规则创建者ID",
  `editorId` varchar(50) NULL COMMENT "规则修改者ID",
  `source` varchar(50) NULL COMMENT "事件来源",
  `classification` varchar(50) NULL COMMENT "分类",
  `trgTrd` varchar(500) NULL COMMENT "触发消息",
  `triggerObjType` varchar(500) NULL COMMENT "触发字段",
  `group` varchar(500) NULL COMMENT "触发分组",
  `message` varchar(5000) NULL COMMENT "事件消息",
  `metrics` array<varchar(255)> NULL COMMENT "度量标准",
  `host` array<varchar(255)> NULL COMMENT "主机列表",
  `serviceId` array<varchar(200)> NULL COMMENT "服务ID列表",
  `serviceInstance` array<varchar(255)> NULL COMMENT "服务实例列表",
  `deviceName` array<varchar(100)> NULL COMMENT "磁盘分区列表",
  `eventStatus` varchar(50) NULL COMMENT "事件状态",
  `eventMsg` varchar(500) NULL COMMENT "事件消息",
  `trigger` json NULL COMMENT "触发对象",
  `tags` json NULL COMMENT "标签",
  `query` json NULL COMMENT "规则查询参数",
  `thresholds` json NULL COMMENT "单指标阈值",
  `multithresholds` json NULL COMMENT "多指标阈值",
  `busName` array<varchar(100)> NULL COMMENT "业务系统列表",
  `ruleName` varchar(1000) NULL COMMENT "规则名称",
  `type` varchar(50) NULL COMMENT "检测方法",
  `createTime` bigint(20) NULL COMMENT "事件创建时间"
) ENGINE=OLAP
DUPLICATE KEY(`triggerTime`, `id`)
PARTITION BY RANGE(`createDt`)()
DISTRIBUTED BY HASH(`id`) BUCKETS 1
PROPERTIES (
"replication_num" = "1",
"dynamic_partition.enable" = "true",
"dynamic_partition.time_unit" = "DAY",
"dynamic_partition.time_zone" = "Asia/Shanghai",
"dynamic_partition.start" = "-30",
"dynamic_partition.end" = "1",
"dynamic_partition.prefix" = "p",
"dynamic_partition.history_partition_num" = "30",
"in_memory" = "false",
"enable_persistent_index" = "false",
"replicated_storage" = "true",
"compression" = "LZ4"
);
SELECT sleep(10);
ALTER TABLE dc_event SWAP WITH dc_event_2_8_6;
SELECT sleep(10);
COMMIT;


-- 支持starrocks数据保留时长30+
ALTER TABLE dc_span SET ("partition_live_number" = "720");
ALTER TABLE dc_rum_web_session SET ("partition_live_number" = "720");
ALTER TABLE dc_rum_web_request_uv SET ("dynamic_partition.start" = "-720");
ALTER TABLE dc_rum_web_request_span SET ("dynamic_partition.start" = "-720");
ALTER TABLE dc_rum_page_uv SET ("dynamic_partition.start" = "-720");
ALTER TABLE dc_rum_page_span SET ("dynamic_partition.start" = "-720");
ALTER TABLE dc_rum_page SET ("dynamic_partition.start" = "-720");
ALTER TABLE dc_rum_ios_session SET ("partition_live_number" = "720");
ALTER TABLE dc_rum_ios_page_span SET ("dynamic_partition.start" = "-720");
ALTER TABLE dc_rum_ios_page SET ("dynamic_partition.start" = "-720");
ALTER TABLE dc_rum_ios_lifecycle_method SET ("dynamic_partition.start" = "-720");
ALTER TABLE dc_rum_ios_launch_span SET ("dynamic_partition.start" = "-720");
ALTER TABLE dc_rum_ios_launch SET ("dynamic_partition.start" = "-720");
ALTER TABLE dc_rum_ios_device_minute SET ("dynamic_partition.start" = "-720");
ALTER TABLE dc_rum_ios_crash SET ("dynamic_partition.start" = "-720");
ALTER TABLE dc_rum_ios_anr SET ("dynamic_partition.start" = "-720");
ALTER TABLE dc_rum_ios_action_span SET ("dynamic_partition.start" = "-720");
ALTER TABLE dc_rum_ios_action SET ("dynamic_partition.start" = "-720");
ALTER TABLE dc_rum_error_logs_uv SET ("dynamic_partition.start" = "-720");
ALTER TABLE dc_rum_error_logs SET ("dynamic_partition.start" = "-720");
ALTER TABLE dc_rum_android_session SET ("partition_live_number" = "720");
ALTER TABLE dc_rum_android_page_span SET ("dynamic_partition.start" = "-720");
ALTER TABLE dc_rum_android_page SET ("dynamic_partition.start" = "-720");
ALTER TABLE dc_rum_android_lifecycle_method SET ("dynamic_partition.start" = "-720");
ALTER TABLE dc_rum_android_launch_span SET ("dynamic_partition.start" = "-720");
ALTER TABLE dc_rum_android_launch SET ("dynamic_partition.start" = "-720");
ALTER TABLE dc_rum_android_device_minute SET ("dynamic_partition.start" = "-720");
ALTER TABLE dc_rum_android_crash SET ("dynamic_partition.start" = "-720");
ALTER TABLE dc_rum_android_anr SET ("dynamic_partition.start" = "-720");
ALTER TABLE dc_rum_android_action_span SET ("dynamic_partition.start" = "-720");
ALTER TABLE dc_rum_android_action SET ("dynamic_partition.start" = "-720");
ALTER TABLE dc_rum_action_uv SET ("dynamic_partition.start" = "-720");
ALTER TABLE dc_rum_action_span SET ("dynamic_partition.start" = "-720");
ALTER TABLE dc_rum_action SET ("dynamic_partition.start" = "-720");
ALTER TABLE dc_rca SET ("dynamic_partition.start" = "-30");
ALTER TABLE dc_logs SET ("dynamic_partition.start" = "-30");
ALTER TABLE dc_data_collect SET ("partition_live_number" = "720");
ALTER TABLE dc_aggregate_result SET ("dynamic_partition.start" = "-30");

ALTER TABLE dc_alarm SET ("dynamic_partition.start" = "-30");
ALTER TABLE dc_alarm_aggregate SET ("dynamic_partition.start" = "-30");
ALTER TABLE dc_event SET ("dynamic_partition.start" = "-30");

ALTER TABLE dc_profiling_hotspot SET ("dynamic_partition.start" = "-30");
ALTER TABLE dc_profiling_stack SET ("dynamic_partition.start" = "-30");
ALTER TABLE dc_profiling_stack_base SET ("dynamic_partition.start" = "-30");