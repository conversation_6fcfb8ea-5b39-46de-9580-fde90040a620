package com.databuff.engine.metric.model;

import lombok.Data;

import java.util.HashMap;
import java.util.Map;

@Data
public class RumWebStatsPercentileAccumulator {
    private String dfApiKey;
    private long windowStartTime;
    private Integer appId;
    private String appName;
    private String processedLocationHref;
    private Map<String, PercentileData> metricsData = new HashMap<>();

    public void add(CombinedRumPageStats stats) {
        addMetric("fcp", stats.getFcp(), stats.getPv());
        addMetric("fid", stats.getFid(), stats.getPv());
        addMetric("fullLoadTime", stats.getFullLoadTime(), stats.getPv());
        addMetric("lcp", stats.getLcp(), stats.getPv());
        addMetric("tbt", stats.getTbt(), stats.getPv());
        addMetric("ttfb", stats.getTtfb(), stats.getPv());
        addMetric("tti", stats.getTti(), stats.getPv());
    }

    private void addMetric(String metricName, Long metricValue, long pv) {
        if (metricValue == null) return;
        metricsData.computeIfAbsent(metricName, k -> new PercentileData())
                .add(metricValue, pv);
    }

    public void merge(RumWebStatsPercentileAccumulator other) {
        for (Map.Entry<String, PercentileData> entry : other.metricsData.entrySet()) {
            metricsData.merge(entry.getKey(), entry.getValue(), PercentileData::merge);
        }
    }

    @Data
    public static class PercentileData {
        private long allCount = 0;
        private long sum = 0;
        private long max = Long.MIN_VALUE;
        private long min = Long.MAX_VALUE;
        private PercentileAccumulator percentile = new PercentileAccumulator();

        public void add(long value, long count) {
            allCount += count;
            sum += value * count;
            max = Math.max(max, value);
            min = Math.min(min, value);
            for (int i = 0; i < count; i++) {
                percentile.add(value);
            }
        }

        public PercentileData merge(PercentileData other) {
            allCount += other.allCount;
            sum += other.sum;
            max = Math.max(max, other.max);
            min = Math.min(min, other.min);
            percentile.merge(other.percentile);
            return this;
        }
    }
}
