package com.databuff.engine.metric.fun;

import com.alibaba.fastjson.JSON;
import com.databuff.common.model.DCSpan;
import com.databuff.common.model.DCTrace;
import com.databuff.common.utils.DCTraceUtil;
import com.databuff.common.utils.StringUtil;
import com.databuff.engine.metric.model.BusinessComponentSpan;
import com.databuff.engine.metric.model.ComponentSpan;
import com.databuff.engine.metric.model.CountSpans;
import com.databuff.engine.metric.model.CountSpansMap;
import com.databuff.engine.util.ComponentUtil;
import org.apache.flink.api.common.functions.AggregateFunction;

import java.util.ArrayList;
import java.util.List;
import java.util.Vector;

import static com.databuff.common.constants.Constant.Trace.REMOTE_ARRRESS_NAME;
import static com.databuff.common.constants.TSDBIndex.TSDB_METRIC_REMOTE_TABLE_NAME;

public class CountSpansAggregateFunction implements AggregateFunction<byte[], CountSpansMap, CountSpansMap> {

    private int totalKeyByNumber;

    public CountSpansAggregateFunction(int keyBy) {
        totalKeyByNumber = keyBy;
    }

    @Override
    public CountSpansMap createAccumulator() {
        return new CountSpansMap();
    }

    @Override
    public CountSpansMap add(byte[] totalDCTraceArr, CountSpansMap countSpansMap) {
        DCTrace dcTrace = DCTraceUtil.deserialize(totalDCTraceArr);

        countSpansMap.initKeyByValue(totalDCTraceArr, totalKeyByNumber);

        long now = System.currentTimeMillis();
        long kafkaDelay = now - dcTrace.getDtsReceiveTime();
        long dataDelay = now - dcTrace.getEndTime();
        int spanSize = dcTrace.getSpans().size();
        countSpansMap.addMetric(1, kafkaDelay, dataDelay, spanSize);

        Vector<DCSpan> spans = new Vector<>();
        dcTrace.getSpans().parallelStream().forEach(bytes ->
                spans.add(JSON.parseObject(bytes, DCSpan.class)));

        spans.forEach(span -> {
            addToServiceMap(span, countSpansMap);
            addToServiceTraceMap(span, countSpansMap);
            addToComponentMap(span, countSpansMap);
            addToBusinessMap(span, countSpansMap);
        });

        return countSpansMap;
    }

    private void addToServiceMap(DCSpan span, CountSpansMap countSpansMap) {
        if (span.getIsIn() != null && span.getIsIn() == 1) {
            String key = span.getServiceId() + "_" + span.getServiceInstance() + "_" + span.getMeta_error_type();
            CountSpans o = countSpansMap.getServiceMap().computeIfAbsent(key, k -> {
                CountSpans cs = new CountSpans();
                cs.setServiceId(span.getServiceId());
                cs.setService(span.getService());
                cs.setServiceInstance(span.getServiceInstance());
                cs.setApiKey(span.getDfApiKey());
                cs.setErrorType(span.getMeta_error_type());
                cs.setWindowStartTime(span.getLongStartTime() / 1000);
                return cs;
            });
            o.add(span);
        }
    }

    private void addToServiceTraceMap(DCSpan span, CountSpansMap countSpansMap) {
        if (span.getIs_parent() == 1) {
            String key = span.getServiceId() + "_" + span.getServiceInstance() + "_" + span.getResource()
                    + "_" + span.getMeta_http_status_code() + "_" + span.getMeta_error_type()
                    + "_" + span.getHostName() + "_" + span.getMeta_http_method() + "_" + span.getError();

            CountSpans o = countSpansMap.getServiceTraceMap().computeIfAbsent(key, k -> {
                CountSpans cs = new CountSpans();
                cs.setServiceId(span.getServiceId());
                cs.setService(span.getService());
                cs.setServiceInstance(span.getServiceInstance());
                cs.setApiKey(span.getDfApiKey());
                cs.setResource(span.getResource());
                cs.setHttpStatusCode(String.valueOf(span.getMeta_http_status_code()));
                cs.setErrorType(span.getMeta_error_type());
                cs.setHostName(span.getHostName());
                cs.setHttpMethod(span.getMeta_http_method());
                cs.setStatus(String.valueOf(span.getError()));
                cs.setWindowStartTime(span.getLongStartTime() / 1000);
                return cs;
            });
            o.add(span);
        }
    }

    private void addToComponentMap(DCSpan span, CountSpansMap countSpansMap) {
        List<BusinessComponentSpan> businessComponentSpans = new ArrayList<>();
        List<ComponentSpan> componentSpanList = ComponentUtil.parseDCSpan(span, businessComponentSpans);

        componentSpanList.forEach(componentSpan -> {
            if (componentSpan.getTags().containsKey(REMOTE_ARRRESS_NAME)) {
                String srcServiceId = componentSpan.getSrcServiceId();
                String svcId = componentSpan.getServiceId();
                String svcInstance = componentSpan.getServiceInstance();
                String value = "";
                if (StringUtil.isNotBlank(srcServiceId)) {
                    //如果来源为空的话代表这个还没有赋上远程调用的服务，需要设置为“”
                    value = StringUtil.isBlank(svcId) ? "" : svcId + "_" + svcInstance;
                }
                countSpansMap.getRemoteAddressSvcMap().put(componentSpan.getTags().get(REMOTE_ARRRESS_NAME), value);
                componentSpan.getTags().remove(REMOTE_ARRRESS_NAME);
                if (componentSpan.getMetricName().equals(TSDB_METRIC_REMOTE_TABLE_NAME) && StringUtil.isEmpty(srcServiceId)) {
                    //如果是远端调用类型，但是没有来源服务ID的话，代表这个数据有问题，不统计指标，只做redis缓存
                    return;
                }
            }
            String key = componentSpan.getServiceId() + "_" + componentSpan.getServiceInstance() + "_" + componentSpan.getSrcServiceId()
                    + "_" + componentSpan.getSrcServiceInstance() + "_" + componentSpan.getResource() + "_" + componentSpan.getIsOut()
                    + "_" + componentSpan.getIsIn() + "_" + componentSpan.getCntOnly() + "_" + componentSpan.getMetricName()
                    + "_" + componentSpan.getTags();
            CountSpans o = countSpansMap.getComponentMap().computeIfAbsent(key, k -> {
                CountSpans cs = new CountSpans();
                cs.setServiceId(componentSpan.getServiceId());
                cs.setService(componentSpan.getService());
                cs.setServiceInstance(componentSpan.getServiceInstance());
                cs.setApiKey(componentSpan.getApiKey());
                cs.setSrcService(componentSpan.getSrcService());
                cs.setSrcServiceId(componentSpan.getSrcServiceId());
                cs.setSrcServiceInstance(componentSpan.getSrcServiceInstance());
                cs.setResource(componentSpan.getResource());
                cs.setIsOut(componentSpan.getIsOut());
                cs.setIsIn(componentSpan.getIsIn());
                cs.setCntOnly(componentSpan.getCntOnly());
                cs.setMetricName(componentSpan.getMetricName());
                cs.setTags(componentSpan.getTags());
                cs.setWindowStartTime(componentSpan.getLongStartTime() / 1000);
                return cs;
            });
            o.add(componentSpan);
        });
    }

    private void addToBusinessMap(DCSpan span, CountSpansMap countSpansMap) {
        List<BusinessComponentSpan> businessComponentSpans = new ArrayList<>();
        ComponentUtil.parseDCSpan(span, businessComponentSpans);

        businessComponentSpans.forEach(businessComponentSpan -> {
            String key = businessComponentSpan.getSrcServiceId() + "_" + businessComponentSpan.getSrcServiceInstance()
                    + "_" + businessComponentSpan.getDstServiceId() + "_" + businessComponentSpan.getDstServiceInstance()
                    + "_" + businessComponentSpan.getIsOut() + "_" + businessComponentSpan.getIsIn()
                    + businessComponentSpan.getSrc_biz_id() + "_" + businessComponentSpan.getDst_biz_id()
                    + "_" + businessComponentSpan.getSrc_biz_pid() + "_" + businessComponentSpan.getDst_biz_pid()
                    + "_" + businessComponentSpan.getBiz_internal_call() + "_" + businessComponentSpan.getSub_biz_internal_call();

            CountSpans o = countSpansMap.getBussinessMap().computeIfAbsent(key, k -> {
                CountSpans cs = new CountSpans();
                cs.setServiceId(StringUtil.nullToEmpty(businessComponentSpan.getDstServiceId()));
                cs.setService(StringUtil.nullToEmpty(businessComponentSpan.getDstService()));
                cs.setServiceInstance(StringUtil.nullToEmpty(businessComponentSpan.getDstServiceInstance()));
                cs.setDstBusName(StringUtil.nullToEmpty(businessComponentSpan.getDstBusName()));
                cs.setApiKey(StringUtil.nullToEmpty(businessComponentSpan.getApiKey()));
                cs.setSrcService(StringUtil.nullToEmpty(businessComponentSpan.getSrcService()));
                cs.setSrcServiceId(StringUtil.nullToEmpty(businessComponentSpan.getSrcServiceId()));
                cs.setSrcServiceInstance(StringUtil.nullToEmpty(businessComponentSpan.getSrcServiceInstance()));
                cs.setIsOut(businessComponentSpan.getIsOut());
                cs.setIsIn(businessComponentSpan.getIsIn());
                cs.setWindowStartTime(businessComponentSpan.getLongStartTime() / 1000);
                cs.setSrc_biz_id(StringUtil.nullToEmpty(businessComponentSpan.getSrc_biz_id()));
                cs.setDst_biz_id(StringUtil.nullToEmpty(businessComponentSpan.getDst_biz_id()));
                cs.setSrc_biz_pid(StringUtil.nullToEmpty(businessComponentSpan.getSrc_biz_pid()));
                cs.setDst_biz_pid(StringUtil.nullToEmpty(businessComponentSpan.getDst_biz_pid()));
                cs.setBiz_internal_call(String.valueOf(businessComponentSpan.getBiz_internal_call()));
                cs.setSub_biz_internal_call(String.valueOf(businessComponentSpan.getSub_biz_internal_call()));
                cs.setSrcServiceType(StringUtil.nullToEmpty(businessComponentSpan.getSrcServiceType()));
                cs.setDstServiceType(StringUtil.nullToEmpty(businessComponentSpan.getDstServiceType()));
                return cs;
            });
            o.add(businessComponentSpan);
        });
    }

    @Override
    public CountSpansMap getResult(CountSpansMap countSpansMap) {
        return countSpansMap;
    }

    @Override
    public CountSpansMap merge(CountSpansMap a, CountSpansMap b) {
        return a.merge(b);
    }
}