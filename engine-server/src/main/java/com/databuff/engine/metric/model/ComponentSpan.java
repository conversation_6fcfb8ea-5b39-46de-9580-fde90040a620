package com.databuff.engine.metric.model;

import lombok.Data;

import java.util.HashMap;
import java.util.Map;

@Data
public class ComponentSpan {

    private String startTime;

    private Long longStartTime;

    private String srcService;
    private String srcServiceId;
    private String srcServiceInstance;

    private String service;
    private String serviceId;
    private String serviceInstance;
    private String resource;
    private String apiKey;

    private int isOut = 0;
    private int isIn = 0;

    private int cntOnly = 0;

    private String metricName;
    private Map<String, String> tags = new HashMap<>();
    private Map<String, Long> fields = new HashMap<>();

    private long cnt;
    private long slow;
    private long duration;
    private long error;

    // 给业务系统使用的字段 ,service指标不需要用到
    private String srcServiceType ;
    private String dstServiceType ;

}
