package com.databuff.engine.trace;

import org.apache.flink.streaming.api.windowing.triggers.Trigger;
import org.apache.flink.streaming.api.windowing.triggers.TriggerResult;
import org.apache.flink.streaming.api.windowing.windows.TimeWindow;

public class SessionTrigger extends Trigger<Object, TimeWindow> {
    private final long maxSessionDuration;

    public SessionTrigger(long maxSessionDuration) {
        this.maxSessionDuration = maxSessionDuration;
    }

    @Override
    public TriggerResult onElement(Object element, long timestamp, TimeWindow window, TriggerContext ctx) {
        // 如果窗口大小超过最大会话时长，触发窗口计算
        if (window.maxTimestamp() - window.getStart() >= maxSessionDuration) {
            return TriggerResult.FIRE_AND_PURGE;
        }
        return TriggerResult.CONTINUE;
    }

    @Override
    public TriggerResult onProcessingTime(long time, TimeWindow window, TriggerContext ctx) {
        return TriggerResult.CONTINUE;
    }

    @Override
    public TriggerResult onEventTime(long time, TimeWindow window, TriggerContext ctx) {
        // 如果窗口结束时间到达，触发窗口计算
        if (time >= window.maxTimestamp()) {
            return TriggerResult.FIRE_AND_PURGE;
        }
        return TriggerResult.CONTINUE;
    }

    @Override
    public void clear(TimeWindow window, TriggerContext ctx) {
        ctx.deleteProcessingTimeTimer(window.maxTimestamp());
    }

    @Override
    public boolean canMerge() {
        return true;
    }

    @Override
    public void onMerge(TimeWindow window, OnMergeContext ctx) {
        long triggerTime = window.maxTimestamp();
        ctx.registerEventTimeTimer(triggerTime);
    }
}
