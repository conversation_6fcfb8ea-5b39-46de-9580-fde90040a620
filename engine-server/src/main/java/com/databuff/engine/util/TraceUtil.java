package com.databuff.engine.util;

import com.databuff.common.model.DCSpan;
import com.databuff.engine.metric.model.ComponentSpan;
import com.databuff.engine.metric.model.Span;
import com.databuff.moredb.aggregator.histogram.HistogramUtil;
import lombok.extern.slf4j.Slf4j;

import java.time.Instant;
import java.util.Map;

@Slf4j
public class TraceUtil {

    public static Instant getStartTime(Span span, long now) {
        Long startTimeLong = span.getStartTime().toEpochMilli();
        if (startTimeLong == null) {
            long start = span.getStart() / 1000 / 1000;
            if (start > now) {
                start = now;
            }
            startTimeLong = (start + span.getDuration() / 1000 / 1000);
        }
        //统计开始时间，既span的结束时间
        return Instant.ofEpochMilli(startTimeLong);
    }

    public static void initServiceAndInstanceFromClient(ComponentSpan componentSpan, DCSpan span) {
        componentSpan.setService(span.getClientService());
        componentSpan.setServiceId(span.getClient_service_id());
        componentSpan.setServiceInstance(span.getClientServiceInstance());
        componentSpan.setResource(span.getResource());
    }

    public static void initServiceAndInstanceFromServer(ComponentSpan componentSpan, DCSpan span) {
        componentSpan.setService(span.getService());
        componentSpan.setServiceId(span.getServiceId());
        componentSpan.setServiceInstance(span.getServiceInstance());
        componentSpan.setResource(span.getResource());
    }

    public static void initServiceAndInstance(ComponentSpan componentSpan, DCSpan span) {
        componentSpan.setService(span.getService());
        componentSpan.setServiceId(span.getServiceId());
        componentSpan.setServiceInstance(span.getServiceInstance());
        componentSpan.setDstServiceType(span.getServiceType());
        componentSpan.setSrcService(span.getClientService() == null ? "" : span.getClientService());
        componentSpan.setSrcServiceId(span.getClient_service_id() == null ? "" : span.getClient_service_id());
        componentSpan.setSrcServiceInstance(span.getClientServiceInstance() == null ? "" : span.getClientServiceInstance());
        componentSpan.setSrcServiceType(span.getClientServiceType() == null ? "" : span.getClientServiceType());
        componentSpan.setResource(span.getResource());
    }

    public static void initOutServiceAndInstance(ComponentSpan componentSpan, DCSpan span) {
        componentSpan.setSrcService(span.getService());
        componentSpan.setSrcServiceId(span.getServiceId());
        componentSpan.setSrcServiceInstance(span.getServiceInstance());
        componentSpan.setSrcServiceType(span.getServiceType());
        componentSpan.setResource(span.getResource());
        componentSpan.setService(span.getServerService());
        componentSpan.setServiceId(span.getServer_service_id());
        componentSpan.setServiceInstance(span.getServerServiceInstance());
        componentSpan.setDstServiceType(span.getServerServiceType());
    }

    public static void addPercentileField(Map<String, Object> fields, Map<Integer, Long> percentileAggMap, long count, long max) {
        fields.put(HistogramUtil.HISTOGRAM_COUNT, count);
        fields.put(HistogramUtil.HISTOGRAM_MAX, max);
        //百分位桶
        if (percentileAggMap != null) {
            for (Map.Entry<Integer, Long> entry : percentileAggMap.entrySet()) {
                if (entry.getValue() == 0) {
                    //如果百分位桶中没有数据，则不输出
                    continue;
                }
                fields.put(HistogramUtil.HISTOGRAM_FIELD + entry.getKey(), entry.getValue());
            }
        }
    }
}
