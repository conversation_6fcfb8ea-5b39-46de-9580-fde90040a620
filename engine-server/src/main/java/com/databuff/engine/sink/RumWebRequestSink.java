package com.databuff.engine.sink;


import com.databuff.common.constants.Constant;
import com.databuff.common.constants.TSDBIndex;
import com.databuff.common.tsdb.model.TSDBPoint;
import com.databuff.common.utils.PointUtil;
import com.databuff.engine.metric.model.RumWebRequestData;
import com.databuff.entity.rum.moredb.RumWebRequest;
import org.apache.flink.api.java.utils.ParameterTool;

import java.util.HashMap;
import java.util.Map;

public class RumWebRequestSink extends AbstractTSDBSink<RumWebRequestData> {


    public RumWebRequestSink(String tsdb, String tsdbUrl, ParameterTool params) {
        super(
                tsdb,
                tsdbUrl,
                params.get("tsdb.api", Constant.MORE_DB.API),
                params.get("tsdb.user", Constant.MORE_DB.USER),
                params.get("tsdb.password", Constant.MORE_DB.PASSWORD),
                params.getInt("tsdb.queryTimeout", Constant.MORE_DB.QUERY_TIMEOUT),
                params.get("tsdb.duration", Constant.MORE_DB.DURATION),
                params.getInt("tsdb.shard", Constant.MORE_DB.SHARD),
                params.getInt("tsdb.replication", Constant.MORE_DB.REPLICATION),
                60,
                false,
                TSDBIndex.TSDB_RUM_METRIC_DATABASE_NAME
        );
    }

    @Override
    protected TSDBPoint processElement(RumWebRequestData rumWebRequestData) {
        if (rumWebRequestData == null){
            return null;
        }
        RumWebRequest request = rumWebRequestData.toRumWebRequest();
        String apikey = request.getApiKey();
        if (apikey == null) {
            return null;
        }

        String dbName = apikey + "_" + TSDBIndex.TSDB_RUM_METRIC_DATABASE_NAME;

        Map<String, String> tags = new HashMap<>();
        tags.put("appId", request.getAppId());
        tags.put("appName", request.getAppName());
        tags.put("isError", request.getIsError());
        tags.put("requestType", String.valueOf(request.getRequestType()));
        tags.put("processedHttpUrl", request.getProcessedHttpUrl());
        tags.put("domain", request.getDomain());
        tags.put("processedPath", request.getProcessedPath());
        tags.put("isp", request.getIsp());
        tags.put("statusCode", request.getStatusCode());
        tags.put("service", request.getService());

        Map<String, Object> fields = new HashMap<>();
        fields.put("requestCount", request.getRequestCount());
        fields.put("errorCount", request.getErrorCount());
        fields.put("transferSize", request.getTransferSize());
        fields.put("duration", request.getDuration());
        fields.put("serverTime", request.getServerTime());
        fields.put("networkTime", request.getNetworkTime());

        return new TSDBPoint(dbName, TSDBIndex.TSDB_METRIC_RUM_WEB_REQUEST, request.getStartTime(), tags, fields, PointUtil.DEFAULT_FIELD_TYPES);
    }
}
