package com.databuff.config;

import com.alibaba.fastjson.JSONObject;
import com.databuff.util.IDConfigManager;
import dto.DConf;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.CommandLineRunner;
import org.springframework.boot.origin.OriginTrackedValue;
import org.springframework.core.env.ConfigurableEnvironment;
import org.springframework.core.env.MapPropertySource;
import org.springframework.core.env.PropertySource;
import org.springframework.stereotype.Component;

import java.util.Map;
import java.util.Objects;

@Component
@Slf4j
public class DConfigStartupRunner implements CommandLineRunner {
    @Autowired
    protected ConfigurableEnvironment configurableEnvironment;

    @Autowired
    protected IDConfigManager<DConf> dConfigManager;

    @Autowired
    protected EnvConfig envConfig;

    @Override
    public void run(String... args) {
        if (dConfigManager == null) {
            throw new IllegalArgumentException("Environment或DConfigManager不能为空");
        }
        initConfig(false, envConfig.getPath(), envConfig.getAppProfile());
    }

    protected DConf getConfigs(String root, String key, String path, String desc) {
        return getConfigs(root, key, path, desc, "");
    }

    protected DConf getConfigs(String root, String key, String path, String desc, Object defaultValue) {
        if (root == null || key == null) {
            return null;
        }

        final String rootKey = root + "." + key;

        // 遍历所有的属性源
        if (configurableEnvironment == null) {
            return null;
        }
        JSONObject jsonObject = new JSONObject();
        for (PropertySource<?> propertySource : configurableEnvironment.getPropertySources()) {
            if (!(propertySource instanceof MapPropertySource)) {
                continue;
            }
            Map<String, Object> propertyMap = ((MapPropertySource) propertySource).getSource();
            // 遍历属性源中的所有属性
            for (Map.Entry<String, Object> entry : propertyMap.entrySet()) {
                if (entry == null) {
                    continue;
                }
                String entryKey = entry.getKey();
                if (entryKey == null || rootKey == null || !entryKey.startsWith(rootKey)) {
                    continue;
                }
                Object value = entry.getValue();
                if (value instanceof OriginTrackedValue) {
                    OriginTrackedValue originTrackedValue = (OriginTrackedValue) value;
                    value = originTrackedValue.getValue();
                }
                if (value != null) {
                    if (Objects.equals(entryKey, rootKey)) {
                        if (rootKey.lastIndexOf(".") + 1 <= entryKey.length()) {
                            entryKey = entryKey.substring(rootKey.lastIndexOf(".") + 1);
                            return DConf.builder().desc(desc).builtIn(true).path(path).key(entryKey).value(value).build();
                        }
                    } else {
                        if (rootKey.length() + 1 <= entryKey.length()) {
                            entryKey = entryKey.substring(rootKey.length() + 1);
                            jsonObject.put(entryKey, value);
                        }
                    }
                }
            }
        }
        // 如果没有找到值，则使用默认值
        return key.lastIndexOf(".") + 1 <= key.length() ? DConf.builder().desc(desc).builtIn(true).path(path).key(key.substring(key.lastIndexOf(".") + 1)).value(jsonObject.isEmpty() ? defaultValue : jsonObject).build() : null;
    }

    private DConf initConfig(Boolean overWrite, String path, String key) {
        if (dConfigManager == null) {
            throw new IllegalArgumentException("Environment或DConfigManager不能为空");
        }
        final String root = envConfig.getRoot();
        if (root == null) {
            throw new IllegalArgumentException("Root或AppName不能为空");
        }

        DConf configs = new DConf<>();
        try {
            configs = this.getConfigs(root, key, path, "初始化配置");
            dConfigManager.saveNode(path, configs, overWrite);
            log.info("配置初始化成功: path={}, key={}", path, key);
        } catch (Exception e) {
            log.error("配置初始化失败", e);
        } finally {
            return configs;
        }
    }
}