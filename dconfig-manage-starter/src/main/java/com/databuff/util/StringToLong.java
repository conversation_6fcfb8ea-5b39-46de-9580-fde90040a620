package com.databuff.util;

import com.alibaba.fastjson.JSON;
import dto.DConf;
import org.springframework.core.convert.converter.Converter;

public class StringToLong implements Converter<String, Long> {
    @Override
    public Long convert(String source) {
        if (!JSON.isValidObject(source)) {
            return Long.valueOf(source);
        }
        final DConf<Object> dConf = JSON.parseObject(source, DConf.class);
        final Object value = dConf.getValue();
        if (value == null) {
            return 0L;
        }
        if (value instanceof Number) {
            return ((Number) value).longValue();
        } else if (value instanceof String) {
            return Long.valueOf((String) value);
        } else {
            return 0L;
        }
    }
}