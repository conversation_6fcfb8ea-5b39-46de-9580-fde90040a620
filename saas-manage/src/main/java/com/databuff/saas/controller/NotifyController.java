package com.databuff.saas.controller;

import com.alibaba.fastjson.JSONObject;
import com.databuff.saas.config.common.CommonResponse;
import com.databuff.saas.model.saas.*;
import com.databuff.saas.service.NotifyService;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;


/**
 * @author:TianMing
 * @date: 2021/12/22
 * @time: 17:14
 */
@RestController
@RequestMapping(value = "/notify")
public class NotifyController {

    public static final String MAIL = "mail" ;
    public static final String SMS = "sms" ;
    public static final String WECHAT = "wechat" ;
    public static final String DINGTALK = "dingtalk" ;
    public static final String WEBHOOK = "webhook";

    @Autowired
    private NotifyService notifyService;


    @ApiOperation(value = "设置邮件配置",notes = "设置邮件配置")
    @PostMapping(value = "/saas/setEmailConfig")
    public CommonResponse<Object> setSaasEmailConfig(@RequestBody NotifyEmailConfig notifyConfig) {
        CommonResponse<Object> commonResponse = new CommonResponse<>();
        notifyConfig.setApiKey("saas-manage");
        notifyConfig.setNotifyType("mail");
        commonResponse.setData(notifyService.setEmailConfig(notifyConfig));
        return commonResponse;
    }


    @ApiOperation(value = "获取邮件配置",notes = "获取邮件配置")
    @GetMapping(value = "/saas/getEmailConfig")
    public CommonResponse<Object> getSaasEmailConfig() {
        CommonResponse<Object> commonResponse = new CommonResponse<>();
        NotifyEmailConfig ret = notifyService.getEmailConfig("saas-manage", MAIL);
        ret.setApiKey(null);
        commonResponse.setData(ret);
        return commonResponse;
    }


    @ApiOperation(value = "设置短信配置",notes = "设置短信配置")
    @PostMapping(value = "/saas/setSmsConfig")
    public CommonResponse<Object> setSaasSmsConfig(@RequestBody NotifySmsConfig notifyConfig) {
        CommonResponse<Object> commonResponse = new CommonResponse<>();
        notifyConfig.setApiKey("saas-manage");
        notifyConfig.setNotifyType("sms");
        commonResponse.setData(notifyService.setSmsConfig(notifyConfig));
        return commonResponse;
    }

    @ApiOperation(value = "设置webhook配置",notes = "设置短信配置")
    @PostMapping(value = "/saas/setWebhookConfig")
    public CommonResponse<Object> setWebhookConfig(@RequestBody WebhookConfig webhookConfig) {
        CommonResponse<Object> commonResponse = new CommonResponse<>();
        webhookConfig.setApiKey("saas-manage");
        webhookConfig.setNotifyType("webhook");
        commonResponse.setData(notifyService.setWebhookConfig(webhookConfig));

        return commonResponse;
    }


    @ApiOperation(value = "获取webhook配置", notes = "获取webhook配置")
    @PostMapping(value = "/saas/getWebhookConfig")
    public CommonResponse<Object> getWebhookConfig() {
        CommonResponse<Object> commonResponse = new CommonResponse<>();
        WebhookConfig ret = notifyService.getWebhookConfig("saas-manage", WEBHOOK);
        ret.setApiKey(null);
        commonResponse.setData(ret);
        return commonResponse;
    }


    @ApiOperation(value = "获取短信配置",notes = "获取短信配置")
    @GetMapping(value = "/saas/getSmsConfig")
    public CommonResponse<Object> getSaasSmsConfig() {
        CommonResponse<Object> commonResponse = new CommonResponse<>();
        NotifySmsConfig ret = notifyService.getSmsConfig("saas-manage",SMS);
        ret.setApiKey(null);
        commonResponse.setData(ret);
        return commonResponse;
    }


    @ApiOperation(value = "设置钉钉配置",notes = "设置钉钉配置")
    @PostMapping(value = "/saas/setDingTalkConfig")
    public CommonResponse<Object> setDingTalkConfig(@RequestBody NotifyDingTalkConfig notifyConfig) {
        CommonResponse<Object> commonResponse = new CommonResponse<>();
        notifyConfig.setApiKey("saas-manage");
        notifyConfig.setNotifyType("dingtalk");
        commonResponse.setData(notifyService.setDingTalkConfig(notifyConfig));
        return commonResponse;
    }


    @ApiOperation(value = "获取钉钉配置",notes = "获取钉钉配置")
    @GetMapping(value = "/saas/getDingTalkConfig")
    public CommonResponse<Object> getDingTalkConfig() {
        CommonResponse<Object> commonResponse = new CommonResponse<>();
        NotifyDingTalkConfig ret = notifyService.getDingTalkConfig("saas-manage",DINGTALK);
        ret.setApiKey(null);
        commonResponse.setData(ret);
        return commonResponse;
    }

    @ApiOperation(value = "设置微信配置",notes = "设置微信配置")
    @PostMapping(value = "/saas/setWeChatConfig")
    public CommonResponse<Object> setWeChatConfig(  @RequestBody NotifyWeChatConfig notifyConfig) {
        CommonResponse<Object> commonResponse = new CommonResponse<>();
        notifyConfig.setApiKey("saas-manage");
        notifyConfig.setNotifyType("wechat");
        commonResponse.setData(notifyService.setWeChatConfig(notifyConfig));
        return commonResponse;
    }


    @ApiOperation(value = "获取微信配置",notes = "获取微信配置")
    @GetMapping(value = "/saas/getWeChatConfig")
    public CommonResponse<Object> getWeChatConfig( ) {
        CommonResponse<Object> commonResponse = new CommonResponse<>();
        NotifyWeChatConfig ret = notifyService.getWeChatConfig("saas-manage",WECHAT);
        ret.setApiKey(null);
        commonResponse.setData(ret);
        return commonResponse;
    }



    @ApiOperation(value = "测试邮件发送",notes = "参数：toEmails")
    @PostMapping(value = "/saas/testEmail")
    public CommonResponse<Object> testEmail(@RequestBody JSONObject info) {
        CommonResponse<Object> commonResponse = new CommonResponse<>();
        commonResponse.setData(notifyService.testEmail("saas-manage",info));
        return commonResponse;
    }


    @ApiOperation(value = "测试短信发送",notes = "参数：phones")
    @PostMapping(value = "/saas/testSms")
    public CommonResponse<Object> testSms(@RequestBody JSONObject info) {
        CommonResponse<Object> commonResponse = new CommonResponse<>();
        commonResponse.setData(notifyService.testSms("saas-manage",info));
        return commonResponse;
    }

    @ApiOperation(value = "设置服务升级/购买地址配置",notes = "参数：enable:0,1 remark ,url")
    @PostMapping(value = "/saas/setUpgradeConfig")
    public CommonResponse<Object> setSaasUpgradeConfig(@RequestBody JSONObject info) {
        CommonResponse<Object> commonResponse = new CommonResponse<>();
        commonResponse.setData(notifyService.setUpgradeConfig(info));
        return commonResponse;
    }


    @ApiOperation(value = "获取服务升级/购买地址配置",notes = "获取服务升级/购买地址配置")
    @GetMapping(value = "/saas/getUpgradeConfig")
    public CommonResponse<Object> getSaasUpgradeConfig() {
        CommonResponse<Object> commonResponse = new CommonResponse<>();
        commonResponse.setData(notifyService.getUpgradeConfig());
        return commonResponse;
    }


}
