package com.databuff.saas.config.kaptcha;

import com.google.code.kaptcha.GimpyEngine;
import com.google.code.kaptcha.NoiseProducer;
import com.google.code.kaptcha.util.Configurable;
import com.jhlabs.image.RippleFilter;

import java.awt.*;
import java.awt.image.BufferedImage;
import java.awt.image.ImageObserver;
import java.security.NoSuchAlgorithmException;
import java.security.SecureRandom;
import java.util.Random;

public class DisKaptchaCssImpl extends Configurable implements GimpyEngine {
    private Random rand = new SecureRandom();
    public  DisKaptchaCssImpl() throws NoSuchAlgorithmException {

    }
    @Override
    public BufferedImage getDistortedImage(BufferedImage baseImage) {
        NoiseProducer noiseProducer = this.getConfig().getNoiseImpl();
        BufferedImage distortedImage = new BufferedImage(125, 45, 2);
        Graphics2D graph = (Graphics2D)distortedImage.getGraphics();

        RippleFilter rippleFilter = new RippleFilter();
        rippleFilter.setXAmplitude(7.6F);
        rippleFilter.setYAmplitude(rand.nextFloat() + 1.0F);
        rippleFilter.setEdgeAction(1);
        BufferedImage effectImage = rippleFilter.filter(baseImage, (BufferedImage)null);
        graph.drawImage(effectImage, 0, 0, (Color) null, (ImageObserver)null);
        graph.dispose();
        noiseProducer.makeNoise(distortedImage, 0.1F, 0.25F, 0.5F, 0.9F);
        noiseProducer.makeNoise(distortedImage, Math.abs(rand.nextFloat()-0.5f), 0.25F, 0.5F, 0.9F);
        return distortedImage;
    }
}