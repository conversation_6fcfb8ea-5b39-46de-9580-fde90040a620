package com.databuff.saas.service;

import com.alibaba.fastjson.JSONObject;
import com.databuff.saas.config.common.CommonResponse;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
 * <AUTHOR>
 * @date 2019/3/19 15:44
 */
public interface UserService {

    /**
     * 用户登录
     *  @param httpServletResponse
     * @param jsonObject
     * @param request
     */
    void login(HttpServletResponse httpServletResponse, JSONObject jsonObject, CommonResponse commonResponse, HttpServletRequest request);

    /**
     * 获取验证码
     */
    JSONObject getImgcapt() throws Exception;

    /**
     * 判断平台授权是否过期
     * @return
     */
    Integer isOutTime();

    /**
     * 初次激活预设平台侧管理员账户
     * @param jsonObject
     * @return
     */
    Object createAdmin(JSONObject jsonObject);
}
