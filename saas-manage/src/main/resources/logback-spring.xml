<?xml version="1.0" encoding="UTF-8"?>
<configuration>
    <property name="log-path" value="${saas.logDir}"/>

    <appender name="CONSOLE" class="ch.qos.logback.core.ConsoleAppender">
        <filter class="ch.qos.logback.classic.filter.LevelFilter">
            <level>INFO</level>
        </filter>
        <encoder>
            <pattern>%yellow(%date) %red([%thread]) %blue(%-5level) %highlight([%logger{50}]) %magenta(%file:%line) - %cyan(%msg%n)</pattern>
            <charset>UTF-8</charset>
        </encoder>
    </appender>

    <appender name="FILE_LOG" class="ch.qos.logback.core.rolling.RollingFileAppender">

        <file>${log-path}/saas-manage.log</file>

        <encoder charset="utf-8">
            <pattern>[%-5level] %d{yyyy-MM-dd HH:mm:ss} [%thread] %logger{36} - %m%n
            </pattern>
        </encoder>

        <rollingPolicy class="ch.qos.logback.core.rolling.FixedWindowRollingPolicy">
            <fileNamePattern>${log-path}/saas-manage.log.%i.gz</fileNamePattern>
            <!-- 最多存留10个文件 -->
            <minIndex>1</minIndex>
            <maxIndex>10</maxIndex>
        </rollingPolicy>

        <triggeringPolicy class="ch.qos.logback.core.rolling.SizeBasedTriggeringPolicy">
            <!-- 单文件最大100MB -->
            <maxFileSize>100MB</maxFileSize>
        </triggeringPolicy>

    </appender>
    <root level="info">
        <appender-ref ref="FILE_LOG"/>
        <appender-ref ref="CONSOLE"/>
    </root>
</configuration>