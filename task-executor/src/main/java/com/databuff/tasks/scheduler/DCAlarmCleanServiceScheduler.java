package com.databuff.tasks.scheduler;

import com.databuff.tasks.service.impl.DCAlarmCleanService;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * @package com.databuff.tasks.kafka.stream
 * @company: dacheng
 * @author: yuzhili
 * @createDate: 2024/10/17
 * @description: 告警过期清理任务
 */
@Slf4j
@Component
public class DCAlarmCleanServiceScheduler {

    @Autowired
    private DCAlarmCleanService dcAlarmCleanService;

    /**
     * 定时清理告警
     * 每分钟的第30秒执行,清理过期的告警（需要在10秒内完成）
     * 每分钟的第40秒执行,规则检测，事件生成，告警生成逻辑
     */
//    @Scheduled(cron = "30 * * * * ?")
    @XxlJob("sharding-alarmClean-task")
    public void scheduleDCAlarmCleanService() {
        try {
            dcAlarmCleanService.clean();
        } catch (Exception e) {
            log.error("定时任务执行失败", e);
        }
    }
}
