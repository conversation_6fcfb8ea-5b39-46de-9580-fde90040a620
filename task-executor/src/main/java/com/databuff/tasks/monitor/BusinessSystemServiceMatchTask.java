package com.databuff.tasks.monitor;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.databuff.common.tsdb.TSDBOperateUtil;
import com.databuff.common.tsdb.builder.QueryBuilder;
import com.databuff.common.tsdb.model.AggFun;
import com.databuff.common.tsdb.model.Aggregation;
import com.databuff.common.tsdb.model.TSDBResultSet;
import com.databuff.dao.mysql.BusinessMapper;
import com.databuff.dao.mysql.BusinessSystemRuleMapper;
import com.databuff.dao.mysql.TraceServiceMapper;
import com.databuff.dao.starrocks.K8sSrMapper;
import com.databuff.entity.*;
import com.databuff.entity.dto.KeyValue;
import com.sun.istack.NotNull;
import com.sun.istack.Nullable;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.databuff.common.constants.TSDBIndex.TSDB_APM_METRIC_DATABASE_NAME;
import static com.databuff.common.constants.TSDBIndex.TSDB_METRIC_SERVICE_INSTANCE_TABLE_NAME;

/**
 * 业务系统根据规则匹配服务
 *
 * <AUTHOR>
 * @date 2024/06/18
 */
@Component
@Slf4j
public class BusinessSystemServiceMatchTask {
    @Resource
    private BusinessSystemRuleMapper businessSystemRuleMapper;

    @Resource
    private BusinessMapper businessMapper;

    @Resource
    private K8sSrMapper k8sSrMapper;

    @Resource
    private TraceServiceMapper traceServiceMapper;

    @Resource
    private TSDBOperateUtil tsdbOperateUtil;

    @Resource
    private ServiceRuleMatcher serviceRuleMatcher;


    @Scheduled(cron = "0 * * * * ?")
    public void execute() {

        try {
            log.info("BusinessSystemServiceMatchTask start");

            // 1. 获取所有启用的业务系统规则
            List<BusinessSystemRule> rules = businessSystemRuleMapper.selectAllEnabled();
            if (rules.isEmpty()) {
                return;
            }

            //取rules 中所有apikey并去重 ,代表需要查这些系统
            Set<String> apiKeys = rules.stream().map(BusinessSystemRule::getApiKey).collect(Collectors.toSet());

            // 2. 获取所有服务与业务系统id的关系
            Map<Integer, Set<String>> bizIdToServiceIdsMap = getBizIdToServiceIdsMap();


            // 3. 查询Starrocks表dc_k8s_latest_pod
            Map<String, String> podToWorkloadMap = getPodToWorkloadMap();

            // 4. 查询MySQL表dc_databuff_service 并构建serviceRuleMap
            Map<String, ServiceRule> serviceRuleMap = fetchAndBuildServiceRuleMap();

            // 5. 查询TSDB的service.instance表
            List<ServiceRule> serviceRules = enrichServiceRulesWithTSDB(apiKeys, podToWorkloadMap, serviceRuleMap);
            if (serviceRules == null) return;

            if (serviceRules.isEmpty()) {
                log.warn("业务系统规则匹配服务,查TSDB没有匹配到任何服务");
                return;
            }

            // 6. 对于每个规则,执行匹配逻辑
            matchAndUpdateBusinessSystemServices(rules, serviceRules, bizIdToServiceIdsMap);
            log.info("BusinessSystemServiceMatchTask end");
        } catch (Exception e) {
            log.error("BusinessSystemServiceMatchTask error ", e);
        }
    }


    private void matchAndUpdateBusinessSystemServices(List<BusinessSystemRule> rules, List<ServiceRule> serviceRules, Map<Integer, Set<String>> bizIdToServiceIdsMap) {
        //  只根据业务系统服务规则监控新增删除 会有两种情况
        //
        //  1. 用户没配规则  -> 这种的话不影响原本已经配置的服务  , 也不会主动删除原本的服务
        //  2. 用户配了规则 -> 只会根据规则去匹配相应的服务 , 如果已经配置的服务不在规则匹配内 ,则会删除

        int totalMatchedCount = 0;
        int totalNewRelationsCount = 0;
        int totalRemovedRelationsCount = 0;
        Set<Integer> matchedSysIds = new HashSet<>();
        Set<Integer> sysIdsWithRemovals = new HashSet<>();

        // 按业务系统ID分组规则
        Map<Integer, List<BusinessSystemRule>> sysIdToRulesMap = rules.stream()
                .collect(Collectors.groupingBy(BusinessSystemRule::getSysId));

        for (Map.Entry<Integer, List<BusinessSystemRule>> entry : sysIdToRulesMap.entrySet()) {
            int sysId = entry.getKey();
            List<BusinessSystemRule> sysRules = entry.getValue();
            Set<String> allMatchedServiceIds = new HashSet<>();

            // 处理该业务系统的所有规则
            for (BusinessSystemRule rule : sysRules) {
                JSONObject query = JSON.parseObject(rule.getQuery());
                Set<String> matchedServiceIds = new HashSet<>();

                for (ServiceRule serviceRule : serviceRules) {
                    if (serviceRuleMatcher.match(serviceRule, query)) {
                        matchedServiceIds.add(serviceRule.getServiceId());
                    }
                }

                allMatchedServiceIds.addAll(matchedServiceIds);
                log.info("Rule id:{} name:{} - Matched: {} services for system id {} ,service ids:{}",
                        rule.getId(), rule.getRuleName(), matchedServiceIds.size(), sysId , matchedServiceIds);
            }

            Set<String> existingServiceIds = bizIdToServiceIdsMap.getOrDefault(sysId, new HashSet<>());
            Set<String> servicesToAdd = new HashSet<>(allMatchedServiceIds);
            servicesToAdd.removeAll(existingServiceIds);

            Set<String> servicesToRemove = new HashSet<>(existingServiceIds);
            servicesToRemove.removeAll(allMatchedServiceIds);

            if (!servicesToAdd.isEmpty()) {
                log.info("Adding {} new services to business system {}: service ids :{}", servicesToAdd.size(), sysId, servicesToAdd);
                JSONArray servicesToAddJSONArray = new JSONArray();
                servicesToAddJSONArray.addAll(servicesToAdd);
                businessMapper.bachAddRelation(sysId, servicesToAddJSONArray);
                totalNewRelationsCount += servicesToAdd.size();
            }

            if (!servicesToRemove.isEmpty()) {
                log.info("Removing {} services from business system {}: service ids :{}", servicesToRemove.size(), sysId, servicesToRemove);
                businessMapper.batchRemoveRelation(sysId, servicesToRemove);
                totalRemovedRelationsCount += servicesToRemove.size();
                sysIdsWithRemovals.add(sysId);
            }

            totalMatchedCount += allMatchedServiceIds.size();
            if (!allMatchedServiceIds.isEmpty()) {
                matchedSysIds.add(sysId);
            }

            log.info("System id:{} - Total rules: {}, Added: {}, Removed: {}, Total matched: {}",
                    sysId, sysRules.size(), servicesToAdd.size(), servicesToRemove.size(), allMatchedServiceIds.size());
        }

        log.info("本次规则匹配结果: 总共服务个数:{}, 启动规则个数:{}, 匹配到业务系统ID: {}, 匹配到服务个数: {}, " +
                        "实际写入数据库的新关系记录数: {}, 实际从数据库删除的关系记录数: {}, 涉及删除的业务系统ID: {}",
                serviceRules.size(), rules.size(), matchedSysIds, totalMatchedCount,
                totalNewRelationsCount, totalRemovedRelationsCount, sysIdsWithRemovals);
    }

    /**
     * 使用TSDB查询并丰富ServiceRule对象
     * @param apiKeys API密钥集合
     * @param podToWorkloadMap Pod到工作负载的映射
     * @param serviceRuleMap 服务规则映射
     * @return 丰富后的ServiceRule列表
     */
    private @Nullable List<ServiceRule> enrichServiceRulesWithTSDB(Set<String> apiKeys, Map<String, String> podToWorkloadMap, Map<String, ServiceRule> serviceRuleMap) {
        // 查询TSDB,补充Kubernetes相关字段
        for (String apiKey : apiKeys) {
            try {
                // 构建查询
                QueryBuilder queryBuilder = new QueryBuilder()
                        .setDatabaseName(apiKey, TSDB_APM_METRIC_DATABASE_NAME)
                        .setMeasurement(TSDB_METRIC_SERVICE_INSTANCE_TABLE_NAME)
                        .addAgg(Aggregation.of(AggFun.LAST, "metricsVal"))
                        .addGroupBy("hostname")
                        .addGroupBy("k8sNamespace")
                        .addGroupBy("k8sPodName")
                        .addGroupBy("serviceId");

                // 添加时间范围条件 - 最近1小时的数据
                long currentTimeMs = System.currentTimeMillis();
                long oneHourAgoMs = currentTimeMs - (60 * 60 * 1000);
                queryBuilder.addWhere(com.databuff.common.tsdb.model.Where.gte("time", oneHourAgoMs));
                queryBuilder.addWhere(com.databuff.common.tsdb.model.Where.neq("serviceId", ""));

                // 执行查询
                TSDBResultSet tsdbResult = tsdbOperateUtil.executeQuery(queryBuilder);

                // 处理查询结果
                if (tsdbResult != null && tsdbResult.getResults() != null && !tsdbResult.getResults().isEmpty()) {
                    if (tsdbResult.getResults().get(0) != null && tsdbResult.getResults().get(0).getSeries() != null) {
                        tsdbResult.getResults().get(0).getSeries().forEach(series -> {
                            Map<String, String> tags = series.getTags();
                            String podName = tags.get("k8sPodName");
                            String serviceId = tags.get("serviceId");
                            String k8sNamespace = tags.get("k8sNamespace");
                            String hostName = tags.get("hostname");

                            ServiceRule serviceRule = serviceRuleMap.get(serviceId);
                            if (serviceRule != null) {
                                serviceRule.setK8sPodName(podName);
                                serviceRule.setK8sNamespace(k8sNamespace);
                                serviceRule.setHostName(hostName);
                                serviceRule.setK8sWorkloadName(podToWorkloadMap.get(podName));
                            } else {
                                serviceRule = new ServiceRule();
                                serviceRule.setServiceId(serviceId);
                                serviceRule.setK8sPodName(podName);
                                serviceRule.setK8sNamespace(k8sNamespace);
                                serviceRule.setHostName(hostName);
                                serviceRule.setK8sWorkloadName(podToWorkloadMap.get(podName));
                                serviceRuleMap.put(serviceId, serviceRule);
                                log.warn("TSDB service.instance表中出现不在mysql的 dc_databuff_service 表的serviceId:{} ,新建ServiceRule为:{}", serviceId, serviceRule);
                            }
                        });
                    }
                }
            } catch (Exception e) {
                log.error("查询TSDB失败 ,本次规则匹配服务跳过, apiKey: " + apiKey, e);
                return null;
            }
        }
        return new ArrayList<>(serviceRuleMap.values());
    }

    /**
     * 先根据 serviceIdToServiceInfoMap 构建 ServiceRule 对象 ,因为dc_databuff_service 表统计所有的服务,包含没启动的
     * @param serviceIdToServiceInfoMap
     * @return {@link Map }<{@link String }, {@link ServiceRule }>
     */
    private static @NotNull Map<String, ServiceRule> buildServiceRuleMap(Map<String, TraceServiceEntity> serviceIdToServiceInfoMap) {
        Map<String, ServiceRule> serviceRuleMap = new HashMap<>();

        for (Map.Entry<String, TraceServiceEntity> entry : serviceIdToServiceInfoMap.entrySet()) {
            String serviceId = entry.getKey();
            TraceServiceEntity serviceInfo = entry.getValue();

            ServiceRule serviceRule = new ServiceRule();
            serviceRule.setServiceId(serviceId);
            serviceRule.setService(serviceInfo.getService());
            serviceRule.setServiceName(serviceInfo.getName());
            serviceRule.setCustomTags(serviceInfo.getCustom_tags());

            serviceRuleMap.put(serviceId, serviceRule);
        }
        return serviceRuleMap;
    }

    /**
     * 查询MySQL表dc_databuff_service 并构建serviceRuleMap
     * @return {@link Map }<{@link String }, {@link ServiceRule }>
     */
    private @NotNull Map<String, ServiceRule> fetchAndBuildServiceRuleMap() {
        Map<String, TraceServiceEntity> serviceIdToServiceInfoMap = getServiceIdToServiceInfoMap();
        return buildServiceRuleMap(serviceIdToServiceInfoMap);
    }

    /**
     * @return {@link Map }<{@link String }, {@link TraceServiceEntity }>
     */
    private @NotNull Map<String, TraceServiceEntity> getServiceIdToServiceInfoMap() {
        List<TraceServiceEntity> allService = traceServiceMapper.listAll();
        return allService.stream()
                .collect(Collectors.toMap(
                        TraceServiceEntity::getId,
                        Function.identity()
                ));
    }

    /**
     * @return {@link Map }<{@link String }, {@link String }>
     */
    private @NotNull Map<String, String> getPodToWorkloadMap() {
        List<K8sPodEntity> k8sPodEntities = k8sSrMapper.K8sPodList(new K8sParam());
        return k8sPodEntities.stream()
                .filter(pod -> pod.getName() != null)  // Filter out only null pod names
                .collect(Collectors.toMap(
                        K8sPodEntity::getName,
                        pod -> pod.getWlName() != null ? pod.getWlName() : ""  // Use empty string for null workload names
                ));
    }

    private Map<Integer, Set<String>> getBizIdToServiceIdsMap() {
        List<KeyValue> relations = businessMapper.getAllBusinessRelations();
        return relations.stream()
                .collect(Collectors.groupingBy(kv -> (Integer) kv.getValue(),
                        Collectors.mapping(KeyValue::getKey, Collectors.toSet())));
    }
}
