package com.databuff.tasks.util;

import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

public class DelayTaskUtil {

    private static final ScheduledExecutorService sch = Executors.newScheduledThreadPool(10);

    public static void add(Runnable runnable, long delay) {
        sch.schedule(runnable, delay, TimeUnit.SECONDS);
    }
}
