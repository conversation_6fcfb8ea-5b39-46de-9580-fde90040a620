package com.databuff.tasks.config;

import lombok.Data;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;
import org.springframework.validation.annotation.Validated;

import javax.validation.Valid;
import javax.validation.constraints.AssertTrue;

/**
 * 自定义功能配置类，管理功能总开关及其子组件配置
 *
 * <p>支持配置热更新(@RefreshScope)和参数校验(@Validated)，当主开关关闭时强制要求所有子功能必须处于关闭状态</p>
 */
@Validated
@Component
@RefreshScope
@Data
public class CustomFeatureConfig {
    /**
     * 功能总开关（默认关闭）
     *
     * <p>通过 custom.feature.enabled 配置项管理，未配置时默认值为false</p>
     */
    @Value("${custom.feature.enabled:false}")
    private Boolean enabled = false;

    /**
     * 子功能组件配置对象
     *
     * <p>包含各子模块的独立开关配置，使用嵌套验证(@Valid)确保配置合法性</p>
     */
    @Valid
    private Components components = new Components();

    /**
     * 主开关与子功能状态校验方法
     *
     * @return boolean 校验结果：当主开关开启(true)或所有子功能被禁用时返回true，
     * 否则校验失败并抛出异常
     */
    @AssertTrue(message = "当总开关关闭时子功能必须全部关闭")
    public boolean isComponentsDisabledWhenMainOff() {
        return enabled || components.isAllDisabled();
    }

    /**
     * 子功能组件配置类，管理各独立功能的启用状态
     *
     * <p>每个子功能都有独立配置项，支持通过all-disabled强制禁用全部子功能</p>
     */
    @Component
    @RefreshScope
    @Data
    public static class Components {
        /**
         * CMDB同步功能（默认关闭）
         *
         * <p>注意：配置默认值(true)与字段默认值(false)存在差异，实际以字段初始值为准</p>
         */
        @Value("${custom.feature.components.cmdb-sync:true}")
        private Boolean cmdbSync = false;

        /**
         * 告警转发功能（默认关闭）
         */
        @Value("${custom.feature.components.alarm-forward:false}")
        private boolean alarmForward = false;

        /**
         * 全量禁用开关（默认关闭）
         *
         * <p>当设置为true时，会覆盖所有子功能的独立配置，强制禁用全部子功能</p>
         */
        @Value("${custom.feature.components.all-disabled:false}")
        private boolean allDisabled = false;

        /**
         * 获取全量禁用状态
         *
         * @return boolean 当前是否处于强制全禁用状态
         */
        public boolean isAllDisabled() {
            return allDisabled;
        }

        /**
         * 设置全量禁用状态
         *
         * @param allDisabled 是否强制禁用所有子功能
         */
        public void setAllDisabled(boolean allDisabled) {
            this.allDisabled = allDisabled;
        }
    }
}
