package com.databuff.tasks.config;

import com.alibaba.fastjson.JSONObject;
import lombok.Data;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;

@Component
@RefreshScope
@Data
public class TaskRefreshScopeConfig {

    /**
     * 压测事件参数
     */
    @Value("${databuff.test.event.count:1}")
    private int eventStressTestCount;

    /**
     * 压测事件开关
     */
    @Value("${databuff.test.event.enabled:false}")
    private boolean eventStressTestEnabled;

    /**
     * AlarmEventForwardTask
     */
    //外发频率 0最后一次发送，1第一次发送，2每次都发
    @Value("${alarm.forward.frequency:0}")
    private String sendFrequency;

    /**
     * BizObservabilityMetric
     */
    @Value("${biz.metric.defTimeOffset:120}")
    private Long defTimeOffset;

    /**
     * RumV2Receiver
     */
    @Value("${global:{}}")
    private JSONObject global;


    /**
     * EventCacheCleanScheduler
     */
    @Value("${databuff.event.expired.time:604800}")
    private long expiredTime;

    /**
     * 默认时间偏移量, 默认120秒
     * 计算公式：默认时间偏移量 = 最延迟的指标数据时间（3分钟） - 60秒（最近1分钟）
     * 注意：这个延迟参数同时会导致告警延迟，所以需要根据实际情况调整
     */
    @Value("${event.metric.defTimeOffset:60}")
    protected int eventDefTimeOffset;

    @Value("${alarm.needMoreTag.enable:false}")
    private boolean needMoreTag;

    /**
     * MonitorUtils
     */
    @Value("${databuff.event.silence.enabled:true}")
    private boolean eventSilenceEnabled;

    /**
     * DefConvergenceTimerV2
     */
    @Value("${alarm.convergence.retry:5}")
    private int retry;
    @Value("${alarm.convergence.offset:0}")
    private int offset;
    @Value("${alarm.enabled:true}")
    private Boolean enabled;

    @Value("${taskPool.metricToMoreDBConsumer.corePoolSize}")
    private Integer corePoolSize;
    @Value("${taskPool.metricToMoreDBConsumer.maxPoolSize}")
    private Integer maxPoolSize;
    @Value("${taskPool.metricToMoreDBDataEntry.corePoolSize}")
    private Integer corePoolSize2;
    @Value("${taskPool.metricToMoreDBDataEntry.maxPoolSize}")
    private Integer maxPoolSize2;
    @Value("${taskPool.metricToMoreDBConsumer.metricConsumerGroup}")
    private String metricConsumerGroup;
    @Value("${taskPool.dropData}")
    private Boolean dropData = false;
    @Value("${root.engine.trigger.delay:3}")
    private Integer rootEngineTriggerDelay;
}
