

#### 指标列表
![](https://cdn.nlark.com/yuque/0/2025/png/40720541/1748252877532-8fb19dcc-b46a-4cc5-a125-454d1a8d3165.png)

![](https://cdn.nlark.com/yuque/0/2025/png/40720541/1748253017120-40256461-646a-4dee-9f2a-a5d61bab8b19.png)

![](https://cdn.nlark.com/yuque/0/2025/png/40720541/1748252985056-9fe54d5e-6074-4f7e-8f09-c47b6b390807.png)

#### 指标分析
![](https://cdn.nlark.com/yuque/0/2025/png/40720541/1748252947267-8244a223-16bc-4c8a-bc81-a59ecb456b5c.png)

#### 指标元数据表结构
```sql
CREATE TABLE `dc_databuff_metrics_insert` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT 'id',
  `identifier` varchar(255) NOT NULL COMMENT '唯一标识符',
  `app` varchar(255) NOT NULL COMMENT '所属插件',
  `database` varchar(255) NOT NULL COMMENT '所属的database',
  `measurement` varchar(255) NOT NULL COMMENT '所属指标名',
  `field` varchar(255) NOT NULL COMMENT '所属field',
  `percentile` int NOT NULL DEFAULT '1' COMMENT '处理百分比数据',
  `tags` varchar(255) DEFAULT NULL COMMENT '转化成的tags',
  `isOpen` tinyint DEFAULT NULL COMMENT '是否开启',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=276 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
```

```sql
CREATE TABLE `dc_databuff_metrics_core` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '指标名的id',
  `type1` varchar(255) NOT NULL COMMENT '一级分类',
  `type2` varchar(255) NOT NULL COMMENT '二级分类',
  `type3` varchar(255) NOT NULL COMMENT '三级分类',
  `app` varchar(255) NOT NULL COMMENT '所属插件',
  `database` varchar(255) NOT NULL COMMENT '指标所在的数据库名',
  `measurement` varchar(1000) NOT NULL COMMENT '指标名',
  `desc` varchar(255) DEFAULT NULL COMMENT '指标描述',
  `tagKey` varchar(5000) DEFAULT NULL COMMENT '指标的维度tags',
  `tagValue` varchar(5000) DEFAULT NULL COMMENT '指标的value的中英文映射',
  `fields` longtext NOT NULL COMMENT '指标的fields',
  `isOpen` tinyint NOT NULL COMMENT '是否启用',
  `metric_type` varchar(50) DEFAULT NULL COMMENT '指标类型，标识数据来源(原始采集/衍生计算/预测/合成等)',
  `metric_source` varchar(50) DEFAULT NULL COMMENT '指标来源，预置/自定义/第三方平台等',
  `basic_judgment` varchar(50) DEFAULT NULL COMMENT '基本判断，高好/低好等指标值的判断方向',
  `aggregation_method` varchar(255) DEFAULT NULL COMMENT '聚合方式，平均，求和，最大值，75分位等',
  `builtin` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否为内置指标：0-非内置，1-内置',
  PRIMARY KEY (`id`),
  KEY `type_index` (`type1`),
  KEY `app_index` (`app`),
  KEY `database_index` (`database`),
  KEY `measurement_index` (`measurement`(700))
) ENGINE=InnoDB AUTO_INCREMENT=563 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
```

```sql
CREATE TABLE `dc_databuff_metrics_query` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '主键',
  `type1` varchar(255) NOT NULL COMMENT '类型1',
  `type2` varchar(255) NOT NULL COMMENT '类型2',
  `type3` varchar(255) NOT NULL COMMENT '类型3',
  `database` varchar(255) NOT NULL COMMENT '数据库',
  `measurement` varchar(255) NOT NULL COMMENT '测量',
  `field` varchar(255) NOT NULL COMMENT '字段',
  `desc` varchar(255) DEFAULT NULL COMMENT '描述',
  `unit` varchar(255) DEFAULT NULL COMMENT '单位',
  `formula` varchar(1000) DEFAULT NULL COMMENT '公式',
  `filter` varchar(2000) DEFAULT NULL COMMENT '过滤器',
  `is_open` tinyint(1) DEFAULT '1' COMMENT '是否启用',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `metric_cn` varchar(255) DEFAULT NULL COMMENT '指标中文名',
  PRIMARY KEY (`id`),
  UNIQUE KEY `measurement_field` (`measurement`,`field`) COMMENT '唯一键'
) ENGINE=InnoDB AUTO_INCREMENT=94 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
```

#### 核心DTO
```java
package com.databuff.entity;

@ApiModel(value = "MetricsQuery", description = "指标查询实体")
@Data
@TableName("dc_databuff_metrics_query")
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class MetricsQuery {

    @ApiModelProperty(value = "类型1", notes = "指标的一级分类，如基础设施、应用、业务等")
    private String type1;

    @ApiModelProperty(value = "类型2", notes = "指标的二级分类，如服务器、数据库、网络等")
    private String type2;

    @ApiModelProperty(value = "类型3", notes = "指标的三级分类，更细粒度的分类")
    private String type3;

    @ApiModelProperty(value = "应用", notes = "指标所属的应用名称")
    private String app;

    @ApiModelProperty(value = "数据库", notes = "指标数据存储的数据库名称")
    private String database;

    @ApiModelProperty(value = "测量", notes = "指标在时序数据库中的测量名称")
    private String measurement;

    @ApiModelProperty(value = "描述", notes = "指标的详细描述信息")
    private String desc;

    @ApiModelProperty(value = "维度key", notes = "指标的标签键定义，用于指标数据的多维度分析")
    private JSONObject tagKey;

    @ApiModelProperty(value = "特殊维度key", notes = "指标的特殊标签键，用于特殊场景的数据分析")
    private JSONObject keys;

    @ApiModelProperty(value = "维度value", notes = "指标的标签值定义，与标签键对应")
    private JSONObject tagValue;

    @ApiModelProperty(value = "字段value", notes = "指标的字段值定义，用于存储指标的实际数值")
    private JSONObject fieldValue;

    @ApiModelProperty(value = "主键ID", notes = "指标在数据库中的唯一标识")
    private Long id;

    @ApiModelProperty(value = "字段", notes = "指标的字段名称，用于指定要查询的指标值")
    private String field;

    @ApiModelProperty(value = "聚合类型", notes = "指标数据的聚合方式，如avg、sum、max等")
    private String aggregatorType;

    @ApiModelProperty(value = "单位", notes = "指标的单位，如ms、bytes、percent等")
    private String unit;

    @ApiModelProperty(value = "单位(中文)", notes = "指标单位的中文表示，如毫秒、字节、百分比等")
    private String unitCn;

    @ApiModelProperty(value = "公式", notes = "衣生指标的计算公式，用于从原始指标计算得出衣生指标")
    private String formula;

    @ApiModelProperty(value = "过滤条件", notes = "指标查询的过滤条件，用于筛选特定条件的数据")
    private JSONObject filter;

    @ApiModelProperty(value = "是否开启", notes = "指标是否启用，用于控制指标的可用性")
    private Boolean isOpen;

    @ApiModelProperty(value = "创建时间", notes = "指标创建的时间戳")
    private Timestamp createTime;

    @ApiModelProperty(value = "更新时间", notes = "指标最后更新的时间戳")
    private Timestamp updateTime;

    @ApiModelProperty(value = "标识符", notes = "指标的唯一标识符，用于在系统中唯一标识指标")
    private String identifier;

    @ApiModelProperty(value = "指标中文名", notes = "指标的中文名称，用于在界面上展示")
    private String metricCn;

    @ApiModelProperty(value = "是否为核心指标", notes = "标记指标是否为核心指标，核心指标通常具有更高的优先级")
    private boolean core;

    @ApiModelProperty(value = "是否内置指标", notes = "内置指标不需要用户手动创建，由系统自动创建")
    public Boolean builtin;

    @ApiModelProperty(value = "为null时，自动填充", notes = "当指标值为null时，自动填充的默认值")
    private Double autoFill;

    @ApiModelProperty(value = "指标类型", notes = "标识数据来源(原始采集/衣生计算/预测/合成等)")
    private MetricTypeEnum metricType;

    @ApiModelProperty(value = "指标来源", notes = "预置/自定义/第三方平台等")
    private MetricSourceEnum metricSource;

    @ApiModelProperty(value = "基本判断", notes = "高好/低好等指标值的判断方向")
    private BasicJudgmentEnum basicJudgment;

    @ApiModelProperty(value = "聚合方式", notes = "平均，求和，最大值，75分位等")
    private Collection<AggregationMethodEnum> aggregationMethod;
}
```

```java
package com.databuff.entity;

@Data
@Builder
@TableName("dc_databuff_metrics_core")
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class MetricsCore {

    @ApiModelProperty(value = "下一个指标")
    private MetricsCore next;

    public boolean hasNext() {
        return next != null;
    }

    /**
     * 设置下一个 MetricsCore 节点。
     * 如果当前节点已有后续节点，则将新节点插入到当前节点之后。
     * 注意：此方法不允许设置自身或形成循环引用。
     */
    public void setNext(MetricsCore next) {
        if (next == null) {
            return;
        }

        // 不允许设置自己为下一个节点
        if (next == this) {
            return;
        }

        // 检查是否形成循环引用（包括间接循环）
        MetricsCore current = next;
        int depth = 0;
        final int MAX_DEPTH = 100; // 防止无限循环检测
        while (current.getNext() != null) {
            current = current.getNext();
            if (current == this || current == next) {
                // 检测到环形引用
                throw new IllegalArgumentException("不允许设置会导致循环引用的节点");
            }
            if (++depth > MAX_DEPTH) {
                throw new IllegalArgumentException("检测到疑似无限链表，请检查输入节点");
            }
        }

        if (this.next != null) {
            this.next.setNext(next);
        }

        this.next = next;
    }

    @ApiModelProperty(value = "主键ID")
    private Long id;

    @ApiModelProperty(value = "类型1")
    private String type1;

    @ApiModelProperty(value = "类型2")
    private String type2;

    @ApiModelProperty(value = "类型3")
    private String type3;

    @ApiModelProperty(value = "应用")
    private String app;

    @ApiModelProperty(value = "数据库")
    private String database;

    @ApiModelProperty(value = "测量")
    private String measurement;

    @ApiModelProperty(value = "描述")
    private String desc;

    @ApiModelProperty(value = "维度key")
    private JSONObject tagKey;

    @ApiModelProperty(value = "维度value")
    private JSONObject tagValue;

    @ApiModelProperty(value = "字段")
    private JSONObject fields;

    @ApiModelProperty(value = "是否启用")
    private Boolean isOpen;

    @ApiModelProperty(value = "指标类型", notes = "标识数据来源(原始采集/衣生计算/预测/合成等)")
    private MetricTypeEnum metricType;

    @ApiModelProperty(value = "指标来源", notes = "预置/自定义/第三方平台等")
    private MetricSourceEnum metricSource;

    @ApiModelProperty(value = "基本判断", notes = "高好/低好等指标值的判断方向")
    private BasicJudgmentEnum basicJudgment;

    @ApiModelProperty(value = "聚合方式", notes = "平均，求和，最大值，75分位等")
    private Collection<AggregationMethodEnum> aggregationMethod;

    @ApiModelProperty(value = "是否内置指标", notes = "内置指标不需要用户手动创建，由系统自动创建")
    public Boolean builtin;

    // 添加getter方法，提供默认值
    public MetricTypeEnum getMetricType() {
        return metricType != null ? metricType : MetricTypeEnum.ORIGINAL;
    }

    public MetricSourceEnum getMetricSource() {
        return metricSource != null ? metricSource : MetricSourceEnum.PRESET;
    }

    public BasicJudgmentEnum getBasicJudgment() {
        return basicJudgment != null ? basicJudgment : BasicJudgmentEnum.HIGH_GOOD;
    }


    public static MetricsCore build(String metric) {
        if (metric == null || !metric.contains(".")) {
            throw new IllegalArgumentException("无效的指标格式");
        }

        // metric 是一个字符串，格式为：database.measurement.field
        final String[] parts = metric.split("\\.");
        final String database = parts[0];
//        final String field = parts[parts.length - 1];

        // measurement 是中间所有字符串
        final String measurement = Arrays.stream(parts, 1, parts.length - 1)
                .collect(Collectors.joining("."));

        return MetricsCore.builder()
                .database(database)
                .measurement(measurement)
//                .fields(field)
                .build();
    }

    public static MetricsCore build(String metric, String type1, String type2, String type3) {
        final MetricsCore metricsCore = MetricsCore.builder()
                .type1(type1)
                .type2(type2)
                .type3(type3)
                .build();
        if (metric == null || !metric.contains(".")) {
            return metricsCore;
        } else {
            // metric 是一个字符串，格式为：database.measurement.field
            final String[] parts = metric.split("\\.");
            final String database = parts[0];

            // measurement 是中间所有字符串
            final String measurement = Arrays.stream(parts, 1, parts.length - 1)
                    .collect(Collectors.joining("."));
            metricsCore.setDatabase(database);
            metricsCore.setMeasurement(measurement);
            return metricsCore;
        }
    }

    public Map<String, MetricsQuery> toMetricsQueryMap() {
        Map<String, MetricsQuery> metricsQueryMap = new HashMap<>();
        if (this.hasNext()) {
            metricsQueryMap.putAll(this.getNext().toMetricsQueryMap());
        }
        JSONObject fields = this.getFields();
        String measurement = this.getMeasurement();
        for (Map.Entry<String, Object> objectEntry : fields.entrySet()) {
            final String field = objectEntry.getKey();
            // 处理值可能是JSONObject或Map的情况
            JSONObject fieldInfo;
            Object value = objectEntry.getValue();
            if (value instanceof JSONObject) {
                fieldInfo = (JSONObject) value;
            } else if (value instanceof Map) {
                // 如果是Map类型，转换为JSONObject
                fieldInfo = new JSONObject((Map<String, Object>) value);
            } else {
                continue; // 其他类型则跳过
            }
            if (fieldInfo == null) {
                continue;
            }
            // 安全地获取字段值，处理可能的空值
            String unit = getStringValue(fieldInfo, "unit");
            String unit_cn = getStringValue(fieldInfo, "unit_cn");
            String describe = getStringValue(fieldInfo, "describe");
            String aggregatorType = getStringValue(fieldInfo, "aggregatorType");
            String metricCn = getStringValue(fieldInfo, "metric_cn");
            Double autoFill = getDoubleValue(fieldInfo, "autoFill");

            if (aggregatorType == null) {
                aggregatorType = "avg";
            }

            final String identifier = measurement + "." + field;

            MetricsQuery metricsQuery = new MetricsQuery();
            metricsQuery.setId(this.id);
            metricsQuery.setIdentifier(identifier);
            metricsQuery.setType1(this.type1);
            metricsQuery.setType2(this.type2);
            metricsQuery.setType3(this.type3);
            metricsQuery.setUnit(unit);
            metricsQuery.setUnitCn(unit_cn);
            metricsQuery.setDesc(describe);
            metricsQuery.setApp(this.getApp());
            metricsQuery.setDatabase(this.getDatabase());
            metricsQuery.setTagKey(this.getTagKey());
            metricsQuery.setTagValue(this.getTagValue());
            // 处理fieldValue可能是JSONObject或Map的情况
            Object fieldValue = fieldInfo.get("fieldValue");
            if (fieldValue instanceof JSONObject) {
                metricsQuery.setFieldValue((JSONObject) fieldValue);
            } else if (fieldValue instanceof Map) {
                metricsQuery.setFieldValue(new JSONObject((Map<String, Object>) fieldValue));
            }
            metricsQuery.setMeasurement(measurement);
            metricsQuery.setField(field);
//            metricsQuery.setIsOpen(this.getIsOpen());
            metricsQuery.setAggregatorType(aggregatorType);
            metricsQuery.setMetricCn(metricCn);
            metricsQuery.setCore(true);
            metricsQuery.setBuiltin(this.builtin);
            metricsQuery.setAutoFill(autoFill);

            // 设置新增的属性
            metricsQuery.setMetricType(this.getMetricType());
            metricsQuery.setMetricSource(this.getMetricSource());
            metricsQuery.setBasicJudgment(this.getBasicJudgment());
            metricsQuery.setAggregationMethod(this.aggregationMethod);

            // 代码兼容
            switch (aggregatorType) {
                case "avg":
                case "gauge":
                    metricsQuery.setFormula(String.format("%s(%s)", "avg", "\"" + field + "\""));
                    break;
                default:
                    metricsQuery.setFormula(String.format("%s(%s)", aggregatorType, "\"" + field + "\""));
                    break;
            }

            metricsQueryMap.put(identifier, metricsQuery);
        }

        final JSONObject cnt = fields.getJSONObject("cnt");
        final JSONObject error = fields.getJSONObject("error");
        final JSONObject slow = fields.getJSONObject("slow");
        final JSONObject sumDuration = fields.getJSONObject("sumDuration");

        if (cnt != null) {
            if (sumDuration != null) {
                metricsQueryMap.put(measurement + ".avgDuration", createMetricsQuery("avgDuration", sumDuration.getString("unit"), sumDuration.getString("unit_cn"), "平均耗时", "sum(sumDuration)/sum(cnt)"));
            }
            if (error != null) {
                metricsQueryMap.put(measurement + ".error.pct", createMetricsQuery("error.pct", "percent", "%", "错误率", "(sum(error)/sum(cnt)) * 100"));
                metricsQueryMap.put(measurement + ".success.pct", createMetricsQuery("success.pct", "percent", "%", "成功率", "(1-sum(error)/sum(cnt)) * 100"));
            }
            if (slow != null) {
                metricsQueryMap.put(measurement + ".slow.pct", createMetricsQuery("slow.pct", "percent", "%", "慢比率", "(sum(slow)/sum(cnt)) * 100"));
            }
        }


        return metricsQueryMap;
    }

    private MetricsQuery createMetricsQuery(String identifier, String unit, String unitCn, String desc, String formula) {
        MetricsQuery metricsQuery = new MetricsQuery();
        metricsQuery.setIdentifier(measurement + "." + identifier);
        metricsQuery.setType1(this.type1);
        metricsQuery.setType2(this.type2);
        metricsQuery.setType3(this.type3);
        metricsQuery.setUnit(unit);
        metricsQuery.setUnitCn(unitCn);
        metricsQuery.setDesc(desc);
        metricsQuery.setApp(this.getApp());
        metricsQuery.setDatabase(this.getDatabase());
        metricsQuery.setTagKey(this.getTagKey());
        metricsQuery.setTagValue(this.getTagValue());
        metricsQuery.setMeasurement(measurement);
//        metricsQuery.setIsOpen(this.getIsOpen());
        metricsQuery.setFormula(formula);

        // 设置新增的属性
        metricsQuery.setMetricType(this.getMetricType());
        metricsQuery.setMetricSource(this.getMetricSource());
        metricsQuery.setBasicJudgment(this.getBasicJudgment());
        metricsQuery.setAggregationMethod(this.aggregationMethod);
        return metricsQuery;
    }

    /**
     * 安全地从 JSONObject 中获取字符串值
     *
     * @param json JSONObject对象
     * @param key 要获取的键
     * @return 字符串值，如果键不存在或值为空则返回null
     */
    private String getStringValue(JSONObject json, String key) {
        try {
            return json.getString(key);
        } catch (Exception e) {
            // 如果键不存在或值不是字符串，返回null
            Object value = json.get(key);
            return value != null ? value.toString() : null;
        }
    }

    /**
     * 安全地从 JSONObject 中获取浮点数值
     *
     * @param json JSONObject对象
     * @param key 要获取的键
     * @return 浮点数值，如果键不存在或值不是数字则返回null
     */
    private Double getDoubleValue(JSONObject json, String key) {
        try {
            return json.getDouble(key);
        } catch (Exception e) {
            // 如果键不存在或值不是数字，返回null
            Object value = json.get(key);
            if (value == null) {
                return null;
            }
            try {
                if (value instanceof Number) {
                    return ((Number) value).doubleValue();
                } else {
                    return Double.parseDouble(value.toString());
                }
            } catch (NumberFormatException nfe) {
                return null;
            }
        }
    }
}
```

```java
package com.databuff.entity;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@TableName("dc_databuff_metrics_insert")
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class MetricsInsert {

    @ApiModelProperty(value = "主键ID")
    private Long id;

    @ApiModelProperty(value = "唯一标识")
    private String identifier;

    @ApiModelProperty(value = "所属插件")
    private String app;

    @ApiModelProperty(value = "所属database")
    private String database;

    @ApiModelProperty(value = "所属指标名")
    private String measurement;

    @ApiModelProperty(value = "所属field")
    private String field;

    @ApiModelProperty(value = "维度")
    private JSONObject tags;

    @ApiModelProperty(value = "处理百分比")
    private Integer percentile;

    @ApiModelProperty(value = "是否开启")
    private Boolean isOpen;


    public MetricsQuery toMetricsQuery(MetricsCore metricsCore) {
        if (metricsCore == null) {
            return null;
        }
        final JSONObject fields = metricsCore.getFields();

        MetricsQuery metricsQuery = new MetricsQuery();
        metricsQuery.setType1(metricsCore.getType1());
        metricsQuery.setType2(metricsCore.getType2());
        metricsQuery.setType3(metricsCore.getType3());
        metricsQuery.setDesc(metricsCore.getDesc());
        metricsQuery.setTagValue(metricsCore.getTagValue());
        metricsQuery.setApp(this.getApp());
        metricsQuery.setIdentifier(this.getIdentifier());
        metricsQuery.setDatabase(this.getDatabase());
        metricsQuery.setTagKey(metricsCore.getTagKey());
        metricsQuery.setTagValue(metricsCore.getTagValue());
        if (fields != null) {
            final JSONObject fieldInfo = fields.getJSONObject(this.getField());
            if (fieldInfo != null) {
                metricsQuery.setUnit(fieldInfo.getString("unit"));
                metricsQuery.setUnitCn(fieldInfo.getString("unitCn"));
                metricsQuery.setDesc(fieldInfo.getString("desc"));
                metricsQuery.setFieldValue(fieldInfo.getJSONObject("fieldValue"));
            }
        }
//        metricsQuery.setIsOpen(this.getIsOpen());
        metricsQuery.setMeasurement(this.getMeasurement());
        metricsQuery.setField(this.getField());
        metricsQuery.setAggregatorType("avg");
        return metricsQuery;
    }

    public Integer getPercentile() {
        return null == percentile ? 1 : percentile;
    }

}
```

#### 核心服务
```java
package com.databuff.service;

import com.alibaba.fastjson.JSONObject;
import com.databuff.common.exception.ExceptionHandlingRunnable;
import com.databuff.dao.mysql.MetricsInsertMapper;
import com.databuff.entity.MetricsCore;
import com.databuff.entity.MetricsInsert;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

import static com.databuff.common.constants.Constant.METRIC_VAL;

@Service
@Slf4j
public class MetricsInsertService {

    @Autowired
    private MetricsInsertMapper metricsInsertMapper;
    @Autowired
    private MetricsCoreService metricsCoreService;

    private Map<String, MetricsInsert> openMetricsInserts = new HashMap<>();
    private ScheduledExecutorService scheduledExecutorService = Executors.newSingleThreadScheduledExecutor();

    @PostConstruct
    private void init() {
        loadByApp(null, true);
        scheduledExecutorService.scheduleAtFixedRate(new ExceptionHandlingRunnable(() -> loadByApp(null, true)), 1, 1, TimeUnit.MINUTES);
    }

    public MetricsInsert findOpenByIdentifier(String identifier) {
        return openMetricsInserts.get(identifier);
    }

    public Map<String, MetricsInsert> findOpenAll() {
        return openMetricsInserts;
    }

    public List<MetricsInsert> findAllByApp(String app) {
        return new ArrayList<>(doLoadByApp(app, null).values());
    }

    private void loadByApp(String app, Boolean open) {
        try {
            Map<String, MetricsInsert> all = doLoadByApp(app, open);
            openMetricsInserts = all;
        } catch (Throwable e) {
            log.error("loadByApp error", e);
        }
    }

    private Map<String, MetricsInsert> doLoadByApp(String app, Boolean open) {
        Map<String, MetricsInsert> all = new HashMap<>();
        List<MetricsInsert> metricsInserts = metricsInsertMapper.findByApp(app, open);
        convertMetricsInsert(all, metricsInserts);

        Map<String, MetricsCore> metricsCores = metricsCoreService.findByApp(app, open);
        convertMetricsCore(all, metricsCores);
        return all;
    }

    private void convertMetricsCore(Map<String, MetricsInsert> all, Map<String, MetricsCore> metricsCores) {
        if (metricsCores == null || metricsCores.size() == 0) {
            return;
        }
        for (Map.Entry<String, MetricsCore> entry : metricsCores.entrySet()) {
            MetricsCore metricsCore = entry.getValue();
            JSONObject fields = metricsCore.getFields();
            String measurement = metricsCore.getMeasurement();
            for (String field : fields.keySet()) {
                MetricsInsert metricsInsert1 = initMetricInsert(".", measurement, field, metricsCore.getApp(), metricsCore.getDatabase());
                metricsInsert1.setIsOpen(metricsCore.getIsOpen());
                if (all.get(metricsInsert1.getIdentifier()) != null) {
                    metricsInsert1.setPercentile(all.get(metricsInsert1.getIdentifier()).getPercentile());
                }
                all.put(metricsInsert1.getIdentifier(), metricsInsert1);

                MetricsInsert metricsInsert2 = initMetricInsert("_", measurement, field, metricsCore.getApp(), metricsCore.getDatabase());
                metricsInsert2.setIsOpen(metricsCore.getIsOpen());
                if (all.get(metricsInsert2.getIdentifier()) != null) {
                    metricsInsert2.setPercentile(all.get(metricsInsert2.getIdentifier()).getPercentile());
                }
                all.put(metricsInsert2.getIdentifier(), metricsInsert2);
            }
        }
    }

    private void convertMetricsInsert(Map<String, MetricsInsert> all, List<MetricsInsert> metricsInserts) {
        if (metricsInserts == null || metricsInserts.size() == 0) {
            return;
        }
        for (MetricsInsert metricsInsert : metricsInserts) {
            all.put(metricsInsert.getIdentifier(), metricsInsert);
        }
    }

    private MetricsInsert initMetricInsert(String split, String measurement, String field, String app, String database) {
        MetricsInsert metricsInsert = new MetricsInsert();
        String identifier;
        if (METRIC_VAL.equals(field)) {
            identifier = measurement;
        } else {
            identifier = measurement + split + field;
        }
        metricsInsert.setIdentifier(identifier);
        metricsInsert.setApp(app);
        metricsInsert.setDatabase(database);
        metricsInsert.setMeasurement(measurement);
        metricsInsert.setField(field);
        metricsInsert.setIsOpen(Boolean.TRUE);
        metricsInsert.setPercentile(1);
        return metricsInsert;
    }


}

```

```java
package com.databuff.service;

import com.alibaba.fastjson.JSONObject;
import com.databuff.common.annotation.LogExecutionTime;
import com.databuff.common.exception.ExceptionHandlingRunnable;
import com.databuff.common.tsdb.dto.detect.MultiDetectQueryRequest;
import com.databuff.common.utils.DateUtils;
import com.databuff.config.DAORefreshScopeConfig;
import com.databuff.dao.mysql.*;
import com.databuff.entity.*;
import com.databuff.entity.dto.DatabuffMonitorView;
import com.databuff.entity.page.PagedResult;
import com.databuff.metric.moredb.SQLParser;
import com.google.common.cache.Cache;
import com.google.common.cache.CacheBuilder;
import com.google.common.collect.Sets;
import com.sun.istack.NotNull;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.ibatis.annotations.Param;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.util.*;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;
import java.util.function.Predicate;
import java.util.stream.Collectors;

import static com.databuff.common.constants.Constant.DF_API_KEY_VALUE;
import static com.databuff.common.constants.Constant.HOST;
import static com.databuff.common.constants.Constant.Trace.BIZ_EVENT_NAME;
import static com.databuff.common.constants.Constant.Trace.SERVICE;
import static com.databuff.common.constants.TSDBIndex.DATABASE_NAME_DATABUFF_SYSTEM;
import static com.databuff.metric.moredb.SQLParser.RULE_NAME;

@Api(value = "MetricsQueryService", tags = "指标查询服务")
@Service
@Slf4j
public class MetricsQueryService {

    public static final String ENABLED = "enabled";
    public static final String NAME = "name";
    @Autowired
    private MetricsQueryMapper metricsQueryMapper;

    @Autowired
    private MetricsCoreService metricsCoreService;

    @Autowired
    private MonitorMapper monitorMapper;

    @Autowired
    private TagHostRelationEntityMapper tagHostRelationEntityMapper;

    @Autowired
    private HostInfoEntityMapper hostInfoEntityMapper;

    @Autowired
    private DAORefreshScopeConfig refreshScopeConfig;

    @Autowired
    private DomainManagerObjService domainManagerObjService;

    @Autowired
    private PluginMapper pluginMapper;

    // 用于存储所有的指标查询（根据key排序）
    private transient NavigableMap<String, MetricsQuery> openMetrics;

    private Set<DatabuffMetricTypeDto> openMetricTypes;

    private NavigableMap<String, MetricTagKey> openMetricTagKeys;

    private final ScheduledExecutorService scheduledExecutorService = Executors.newSingleThreadScheduledExecutor(r -> {
        Thread thread = new Thread(r);
        thread.setName("MetricsQuery-init-Thread");
        return thread;
    });

    @PostConstruct
    private void init() {
        openMetrics = loadByApp(true);
        scheduledExecutorService.scheduleAtFixedRate(
                new ExceptionHandlingRunnable(() -> {
                    openMetrics = loadByApp(true);
                }), 1, 1, TimeUnit.MINUTES);
    }

    @ApiOperation(value = "根据标识符查找开放的指标")
    @LogExecutionTime(tag = {"identifier"})
    public MetricsQuery findOpenByIdentifier(@ApiParam(value = "指标标识符") String identifier) {
        if (identifier == null) {
            return null;
        }
        return openMetrics.get(identifier);
    }

    @ApiOperation(value = "查找所有开放的指标")
    public Map<String, MetricsQuery> findOpen() {
        return openMetrics;
    }

    @ApiOperation(value = "查找所有开放的标签信息")
    public NavigableMap<String, MetricTagKey> findOpenTagKeys() {
        return openMetricTagKeys;
    }

    @ApiOperation(value = "加载指定标识符的指标")
    public MetricsQuery load(String identifier) {
        if (identifier == null || openMetrics == null) {
            return null;
        }
        return openMetrics.get(identifier);
    }

    /**
     * 根据指定的标识符删除缓存中的条目。
     *
     * @param identifier 要删除的缓存标识符。如果为null或缓存未初始化，则不执行任何操作。
     */
    public void deleteCache(String identifier) {
        if (identifier == null || openMetrics == null) {
            return;
        }
        openMetrics.remove(identifier);
    }

    /**
     * 根据提供的标识符集合批量删除缓存中的条目。
     *
     * @param identifiers 要删除的缓存标识符集合。如果集合为null，则不执行任何操作。
     */
    public void deleteCache(Collection<String> identifiers) {
        if (identifiers == null) {
            return;
        }
        for (String id : identifiers) {
            deleteCache(id);
        }
    }


    @ApiOperation(value = "加载指定标识符的指标")
    public Map<String, MetricsQuery> load(Collection<String> identifiers) {
        if (identifiers == null) {
            return new HashMap<>();
        }
        Map<String, MetricsQuery> result = new HashMap<>();
        for (String identifier : identifiers) {
            final MetricsQuery metricsQuery = openMetrics.get(identifier);
            if (metricsQuery != null) {
                result.put(identifier, metricsQuery);
            }
        }
        return result;
    }

    @ApiOperation(value = "查询指定标识符的指标")
    public Map<String, MetricsQuery> query(@ApiParam(value = "指标标识符") String identifier) {
        Map<String, MetricsQuery> result = new HashMap<>();
        if (StringUtils.isBlank(identifier)) {
            return result;
        }
        for (Map.Entry<String, MetricsQuery> entry : openMetrics.entrySet()) {
            final String key = entry.getKey();
            if (key.contains(identifier)) {
                result.put(key, entry.getValue());
            }
        }
        return result;
    }

    @ApiOperation(value = "查询指定标识符集合的指标")
    public Map<String, MetricsQuery> queryIn(@ApiParam(value = "指标标识符集合") Collection<String> identifiers) {
        Map<String, MetricsQuery> result = new HashMap<>();
        if (CollectionUtils.isEmpty(identifiers)) {
            return result;
        }
        for (String identifier : identifiers) {
            final MetricsQuery metricsQuery = openMetrics.get(identifier);
            if (metricsQuery != null) {
                result.put(identifier, metricsQuery);
            }
        }
        return result;
    }


    /**
     * 判断给定的数据库和表是否是宽表
     *
     * @return true 如果给定的数据库和表是宽表，false 否则
     */
    public boolean isDerivedMetric(String metricCode) {
        log.debug("isDerivedMetric metricCode:{}", metricCode);
        return this.findOpenByIdentifier(metricCode) != null;
    }

    @ApiOperation(value = "加载指定类型的指标")
    public Map<String, MetricsQuery> load(@ApiParam(value = "类型1") String type1,
                                          @ApiParam(value = "类型2") String type2,
                                          @ApiParam(value = "类型3") String type3) {
        Map<String, MetricsQuery> all = new HashMap<>();
        this.openMetrics.entrySet().stream().filter(entry -> {
            MetricsQuery metricsQuery = entry.getValue();
            if (metricsQuery == null) {
                return false;
            }
            final String metricsQueryType1 = metricsQuery.getType1();
            return (type1 == null || type1.equals(metricsQueryType1))
                    && (type2 == null || type2.equals(metricsQuery.getType2()))
                    && (type3 == null || type3.equals(metricsQuery.getType3()));
        }).forEach(entry -> all.put(entry.getKey(), entry.getValue()));
        return all;
    }

    public synchronized void reloadAllMetrics() {
        openMetrics = loadByApp(true);
    }

    private NavigableMap<String, MetricsQuery> loadByApp(boolean forceReload) {
        NavigableMap<String, MetricsQuery> all = new TreeMap<>();
        try {
            // 指标查询暂时不依赖 dc_databuff_metrics_insert 表的元数据。
//        Map<String, MetricsInsert> metricsInserts = metricsInsertService.findOpenAll();
//        convertMetricsInsert(all, metricsInserts);
            if (forceReload) {
                metricsCoreService.load(null, true);
            }

            Map<String, MetricsCore> metricsCores = metricsCoreService.findOpenAll();
            convertMetricsCore(all, metricsCores);

            Map<String, MetricsQuery> metricsQueries = this.findOpenAll();
            all.putAll(metricsQueries);

            // 主机标签丰富
            final List<String> hostKeys = tagHostRelationEntityMapper.selectDistinctTagKeys();
            if (CollectionUtils.isNotEmpty(hostKeys)) {
                JSONObject host = new JSONObject().fluentPut(HOST, hostKeys);
                for (Map.Entry<String, MetricsQuery> entry : all.entrySet()) {
                    final MetricsQuery metricsQuery = entry.getValue();
                    if (metricsQuery == null) {
                        return null;
                    }
                    final String type2 = metricsQuery.getType2();
                    if ("主机".equals(type2)) {
                        metricsQuery.setKeys(host);
                    }
                }
            }

        } catch (Throwable e) {
            log.error("load error", e);
        }

        openMetricTagKeys = new TreeMap<>();
        openMetricTypes = new TreeSet<>();
        for (Map.Entry<String, MetricsQuery> entry : all.entrySet()) {
            final MetricsQuery metricsQuery = entry.getValue();
            if (metricsQuery == null) {
                continue;
            }

            DatabuffMetricTypeDto databuffMetricTypeDto = new DatabuffMetricTypeDto();
            databuffMetricTypeDto.setType1(metricsQuery.getType1());
            databuffMetricTypeDto.setType2(metricsQuery.getType2());
            databuffMetricTypeDto.setType3(metricsQuery.getType3());
            databuffMetricTypeDto.setApp(metricsQuery.getApp());
            databuffMetricTypeDto.setBuiltin(metricsQuery.getBuiltin());
            openMetricTypes.add(databuffMetricTypeDto);

            final JSONObject tagKey = metricsQuery.getTagKey();
            if (tagKey != null) {
                for (Map.Entry<String, Object> tag : tagKey.entrySet()) {
                    final String key = tag.getKey();
                    final String value = (String) tag.getValue();
                    MetricTagKey tagValue = openMetricTagKeys.get(key);
                    final JSONObject metricsQueryTagValue = metricsQuery.getTagValue();
                    if (tagValue != null) {
                        tagValue.getNames().add(value);
//                        tagValue.getNames().add(value + "(" + key + ")");
//                        tagValue.getMetrics().add(metricsQuery.getIdentifier());
                    } else {
                        tagValue = MetricTagKey.builder()
                                .tagKey(key)
                                .names(new HashSet<>(Collections.singletonList(value)))
//                                .names(new HashSet<>(Collections.singletonList(value + "(" + key + ")")))
//                                .metrics(new HashSet<>(Collections.singletonList(metricsQuery.getIdentifier())))
                                .tagValue(new JSONObject())
                                .build();
                        openMetricTagKeys.put(key, tagValue);
                    }
                    if (metricsQueryTagValue != null) {
                        tagValue.getTagValue().putAll(metricsQueryTagValue);
                    }
                }
            }
        }
        log.debug("loadByApp all.size:{}", all.size());
        log.debug("loadByApp openMetricTagKeys.size:{}", openMetricTagKeys.size());
        final List<DatabuffPluginMetric> allPluginOpenMetrics = pluginMapper.getAllPluginOpenMetrics(DF_API_KEY_VALUE);
        if (allPluginOpenMetrics == null) {
            return all;
        }
        for (DatabuffPluginMetric allPluginOpenMetric : allPluginOpenMetrics) {
            if (allPluginOpenMetric == null) {
                continue;
            }
            final JSONObject openMetrics = JSONObject.parseObject(allPluginOpenMetric.getOpenMetrics());
            if (openMetrics == null) {
                continue;
            }
            for (Map.Entry<String, Object> entry : openMetrics.entrySet()) {
                if (entry == null) {
                    continue;
                }
                final Object value = entry.getValue();
                if (value instanceof Map) {
                    for (Map.Entry<String, Object> entry1 : ((Map<String, Object>) value).entrySet()) {
                        final Object metrics = entry1.getValue();
                        if (metrics instanceof Collection) {
                            for (Object metric : (Collection) metrics) {
                                final MetricsQuery metricsQuery = all.get(metric);
                                if (metricsQuery == null) {
                                    continue;
                                }
                                metricsQuery.setIsOpen(true);
                            }
                        }
                    }
                }
            }
        }
        return all;
    }

    private NavigableMap<String, MetricsQuery> findOpenAll() {
        NavigableMap<String, MetricsQuery> all = new TreeMap<>();
        convertMetricsQuery(all, metricsQueryMapper.findByTypesAndIsOpen(null, null, null, true));
        return all;
    }

    @ApiOperation(value = "根据类型查找指标")
    public Set<String> findMetricByType(@ApiParam(value = "类型1") String type1, @ApiParam(value = "类型2") String type2, @ApiParam(value = "类型3") String type3) {
        return this.load(type1, type2, type3).keySet();
    }

    @ApiOperation(value = "查找所有的指标类型和应用")
    public List<DatabuffMetricTypeDto> findAllTypesAndApp( Boolean builtin) {
        return openMetricTypes.stream()
                .filter(metricsQuery -> metricsQuery != null && (builtin == null || builtin.equals(metricsQuery.getBuiltin())) )
                .collect(Collectors.toList());
    }

    /**
     * 根据提供的参数搜索指标（分页）。
     *
     * @param为搜索参数设置参数。这包括: -类型1:指标的第一种类型。
     * - type2:指标的第二种类型。
     * - type3:指标的第三种类型。
     * - app:应用名称。
     * - database:数据库名称。
     * - measurement:测量值的名称。
     * - searchKey:搜索键。
     * - pageSize:每页结果的数量。
     * @返回一个包含搜索结果的PagedResult对象。这包括: - data:搜索结果的地图，其中键是度量标识符，值是MetricsQuery对象。
     * - lastKey:搜索结果当前页面的最后一个键。
     * - pageSize:每页结果的数量。
     **/
    /**
     * 判断指标是否符合搜索条件
     *
     * @param key 指标的键
     * @param metricsQuery 指标对象
     * @param type1 类型1
     * @param type2 类型2
     * @param type3 类型3
     * @param app 应用
     * @param database 数据库
     * @param measurement 测量
     * @param lowerSearchKey 小写的搜索关键字
     * @param tagKey 标签关键字
     * @return 如果指标符合搜索条件返回true，否则返回false
     */
    private boolean isMetricMatchSearchCriteria(String key, MetricsQuery metricsQuery,
                                             String type1, String type2, String type3,
                                             String app, String database, String measurement,
                                             String lowerSearchKey, String tagKey) {
        return metricsQuery != null
                && (type1 == null || type1.equals(metricsQuery.getType1()))
                && (type2 == null || type2.equals(metricsQuery.getType2()))
                && (type3 == null || type3.equals(metricsQuery.getType3()))
                && (app == null || app.equals(metricsQuery.getApp()))
                && (database == null || database.equals(metricsQuery.getDatabase()))
                && (measurement == null || measurement.equals(metricsQuery.getMeasurement()))
//                && Boolean.TRUE.equals(metricsQuery.getIsOpen())
                && ((lowerSearchKey == null || key.toLowerCase().contains(lowerSearchKey)
                    || (metricsQuery.getDesc() != null && metricsQuery.getDesc().toLowerCase().contains(lowerSearchKey)))
                || (tagKey == null || (metricsQuery.getTagKey() != null
                    && metricsQuery.getTagKey().toJSONString().toLowerCase().contains(lowerSearchKey))));
    }

    /**
     * 检查给定的指标查询是否符合搜索条件。
     *
     * @param typeKey       用于过滤类型的键（可选，不区分大小写）
     * @param metricKey     用于过滤指标标识符的键（可选，不区分大小写）
     * @param metricsQuery  包含查询条件的指标查询对象
     * @return true 如果查询对象满足所有条件，否则false
     */
    private boolean isMetricMatchSearchCriteria(String typeKey, String metricKey, MetricsQuery metricsQuery) {
        if (typeKey != null) {
            typeKey = typeKey.toLowerCase();
        }
        if (metricKey != null) {
            metricKey = metricKey.toLowerCase();
        }
        return metricsQuery != null
                && ((typeKey == null || metricsQuery.getType1().toLowerCase().contains(typeKey))
                || (typeKey == null || metricsQuery.getType2().toLowerCase().contains(typeKey))
                || (typeKey == null || metricsQuery.getType3().toLowerCase().contains(typeKey)))
                // 检查指标是否处于开启状态
//                && Boolean.TRUE.equals(metricsQuery.getIsOpen())
                // 检查指标标识符是否包含metricKey（若metricKey非空）
                && ((metricKey == null || metricsQuery.getIdentifier().toLowerCase().contains(metricKey)));
    }


    @ApiOperation(value = "根据提供的参数搜索指标（分页）")
    public PagedResult<Map<String, MetricsQuery>, String> searchMetrics(@NotNull @ApiParam(value = "搜索指标参数") SearchMetricsParams params) {
        String type1 = params.getType1();
        String type2 = params.getType2();
        String type3 = params.getType3();
        String app = params.getApp();
        String database = params.getDatabase();
        String measurement = params.getMeasurement();
        String searchKey = params.getSearchKey();
        String tagKey = StringUtils.defaultString(params.getTagKey(), searchKey);
        int pageSize = params.getPageSize();

        Map<String, MetricsQuery> result = new LinkedHashMap<>();
        String newLastKey = null;
        // 从上一个key开始查找
        final String lastKey = params.getLastKey();
        SortedMap<String, MetricsQuery> sortedMap = openMetrics;
        if (lastKey != null) {
            sortedMap = openMetrics.tailMap(lastKey, false);
        }

        //将searchKey转换为小写，以便进行不区分大小写的比较
        String lowerSearchKey = searchKey != null ? searchKey.toLowerCase() : null;

        for (Map.Entry<String, MetricsQuery> entry : sortedMap.entrySet()) {
            final String key = entry.getKey();
            final MetricsQuery metricsQuery = entry.getValue();

            if (isMetricMatchSearchCriteria(key, metricsQuery, type1, type2, type3, app, database, measurement, lowerSearchKey, tagKey)) {
                result.put(key, metricsQuery);
                newLastKey = key;
                if (result.size() == pageSize) {
                    break;
                }
            }
        }

        PagedResult<Map<String, MetricsQuery>, String> pagedResult = new PagedResult<>();
        pagedResult.setData(result);
        pagedResult.setLastKey(newLastKey);
        pagedResult.setPageSize(pageSize);

        if (StringUtils.isBlank(lastKey)) {
            pagedResult.setTotal(openMetrics.entrySet().stream().filter(entry ->
                isMetricMatchSearchCriteria(entry.getKey(), entry.getValue(), type1, type2, type3, app, database, measurement, lowerSearchKey, tagKey)
            ).count());
        }
        return pagedResult;
    }

    @ApiOperation(value = "根据提供的参数搜索指标总数")
    public Long getTotal(@NotNull @ApiParam(value = "搜索指标参数") SearchMetricsParamsV2 params) {
        return openMetrics.entrySet().stream().filter(entry ->
                isMetricMatchSearchCriteria(params.getTypeKey(), params.getMetricKey(), entry.getValue())
        ).count();
    }

    /**
     * 根据提供的参数搜索指标类型。
     *
     * @param params 搜索参数，包括：
     *               - typeKey: 指标的类型关键字（对应原注释中的searchKey）
     *               - metricKey: 指标名称关键字
     * @return 符合条件的指标类型列表
     */
    @ApiOperation(value = "根据提供的参数搜索指标类型")
    public List<DatabuffMetricTypeDto> searchMetricTypes(@NotNull @ApiParam(value = "搜索指标参数") SearchMetricsParamsV2 params) {
        if (params == null) {
            throw new IllegalArgumentException("params cannot be null");
        }
        // 使用Set来存储唯一的DatabuffMetricTypeDto对象
        Map<DatabuffMetricTypeDto, NavigableSet<String>> typeMetricMap = new HashMap<>();

        // 基于openMetrics进行筛选，与现有的searchMetrics方法类似
        for (Map.Entry<String, MetricsQuery> entry : openMetrics.entrySet()) {
            final MetricsQuery metricsQuery = entry.getValue();

            if (isMetricMatchSearchCriteria(params.getTypeKey(), params.getMetricKey(), metricsQuery)) {
                // 创建DatabuffMetricTypeDto对象并添加到结果集合中
                DatabuffMetricTypeDto metricTypeDto = new DatabuffMetricTypeDto();
                metricTypeDto.setType1(metricsQuery.getType1());
                metricTypeDto.setType2(metricsQuery.getType2());
                metricTypeDto.setType3(metricsQuery.getType3());
                metricTypeDto.setApp(metricsQuery.getApp());
                metricTypeDto.setBuiltin(metricsQuery.getBuiltin());
                final String identifier = metricsQuery.getIdentifier();
                if (typeMetricMap.containsKey(metricTypeDto)) {
                    typeMetricMap.get(metricTypeDto).add(identifier);
                } else {
                    typeMetricMap.put(metricTypeDto, new TreeSet<>(Collections.singleton(identifier)));
                }
            }
        }
        List<DatabuffMetricTypeDto> result = new ArrayList<>();
        for (Map.Entry<DatabuffMetricTypeDto, NavigableSet<String>> entry : typeMetricMap.entrySet()) {
            if (entry == null) {
                continue;
            }
            final DatabuffMetricTypeDto metricTypeDto = entry.getKey();
            metricTypeDto.setMetricList(entry.getValue());
            result.add(metricTypeDto);
        }

        return result;
    }

    @ApiOperation(value = "根据提供的参数搜索指标（不分页）")
    public Map<String, MetricsQuery> searchAllMetrics(@NotNull @ApiParam(value = "搜索指标参数") SearchMetricsParams params) {
        String type1 = params.getType1();
        String type2 = params.getType2();
        String type3 = params.getType3();
        String database = params.getDatabase();
        String measurement = params.getMeasurement();
        String searchKey = params.getSearchKey();
        String tagKey = StringUtils.defaultString(params.getTagKey(), searchKey);

        // 将searchKey转换为小写，以便进行不区分大小写的比较
        String lowerSearchKey = searchKey != null ? searchKey.toLowerCase() : null;

        Set<String> apps = null;
        final String app = params.getApp();
        if ("system".equals(app)) {
            apps = Sets.newHashSet("cpu", "memory", "network", "disk", "host");
        } else if (app != null) {
            apps = Sets.newHashSet(app);
        }

        Map<String, MetricsQuery> result = new LinkedHashMap<>();
        SortedMap<String, MetricsQuery> sortedMap = openMetrics;

        for (Map.Entry<String, MetricsQuery> entry : sortedMap.entrySet()) {
            final String key = entry.getKey();
            final MetricsQuery metricsQuery = entry.getValue();

            // 对于系统指标特殊处理app参数
            boolean appMatch = app == null ||
                               (apps != null && apps.contains(metricsQuery.getApp())) ||
                               (app != null && app.equals(metricsQuery.getApp()));

            if (metricsQuery != null && appMatch &&
                isMetricMatchSearchCriteria(key, metricsQuery, type1, type2, type3, null, database, measurement, lowerSearchKey, tagKey)) {
                result.put(key, metricsQuery);
            }
        }

        String host = params.getHost();
        if (host == null) return result;

        String from = DateUtils.formatDateTime(new Date(System.currentTimeMillis() - TimeUnit.DAYS.toMillis(7)));
        HostInfoEntity hostInfo = hostInfoEntityMapper.selectByName(host, from, null, params.getApiKey());
        if (hostInfo == null) {
            return result;
        }

        if ((hostInfo.getOs() != null && !hostInfo.getOs().contains("BSD"))
                || (hostInfo.getPlatform() != null && !hostInfo.getPlatform().contains("BSD"))) {
            result.remove("system.net.tcp.retrans_packs");
            result.remove("system.net.tcp.sent_packs");
            result.remove("system.net.tcp.rcv_packs");
        }

        final String features = hostInfo.getFeatures();
        if (features != null && !features.contains("conntrack")) {
            result.entrySet().removeIf(entry -> entry.getKey().startsWith("system.net.conntrack"));
        }

        return result;
    }


    private void convertMetricsCore(Map<String, MetricsQuery> all, Map<String, MetricsCore> metricsCores) {
        if (metricsCores == null || metricsCores.size() == 0) {
            return;
        }
        for (Map.Entry<String, MetricsCore> entry : metricsCores.entrySet()) {
            MetricsCore metricsCore = entry.getValue();
            if (metricsCore == null) {
                continue;
            }
            all.putAll(metricsCore.toMetricsQueryMap());
        }
    }

    private void convertMetricsQuery(Map<String, MetricsQuery> all, List<MetricsQuery> metricsQueries) {
        if (metricsQueries == null || metricsQueries.size() == 0) {
            return;
        }
        for (MetricsQuery metricsQuery : metricsQueries) {
            final String identifier = metricsQuery.getMeasurement() + "." + metricsQuery.getField();
            metricsQuery.setIdentifier(identifier);
            //目前规定所有metricsQuery指标都是内置的
            metricsQuery.setBuiltin(true);
            final MetricsCore metricsCore = metricsCoreService.findOpenOne(metricsQuery.getDatabase(), metricsQuery.getMeasurement());
            if (metricsCore != null) {
                final JSONObject fields = metricsCore.getFields();
                metricsQuery.setApp(metricsCore.getApp());
                metricsQuery.setTagKey(metricsCore.getTagKey());
                metricsQuery.setTagValue(metricsCore.getTagValue());
                if (fields != null) {
                    final JSONObject fieldInfo = fields.getJSONObject(metricsQuery.getField());
                    if (fieldInfo != null) {
//                        metricsQuery.setUnit(fieldInfo.getString("unit"));
//                        metricsQuery.setUnitCn(fieldInfo.getString("unit_cn"));
//                        metricsQuery.setDesc(fieldInfo.getString("describe"));
                        metricsQuery.setFieldValue(fieldInfo.getJSONObject("fieldValue"));
                    }
                }
            }
            all.put(identifier, metricsQuery);
        }
    }


    private void convertMetricsInsert(Map<String, MetricsQuery> all, Map<String, MetricsInsert> metricsInserts) {
        if (metricsInserts == null || metricsInserts.size() == 0) {
            return;
        }
        for (MetricsInsert metricsInsert : metricsInserts.values()) {
            if (metricsInsert == null) {
                continue;
            }
            final String database = metricsInsert.getDatabase();
            final String measurement = metricsInsert.getMeasurement();
            MetricsQuery metricsQuery = metricsInsert.toMetricsQuery(metricsCoreService.findOpenOne(database, measurement));
            all.put(metricsInsert.getIdentifier(), metricsQuery);
        }
    }


    public String getAggFunc(String metricCode, String agg) {
        if (agg == null) {
            agg = "none";
        }
        if (metricCode == null || agg == null) {
            return null;
        }
        final MetricsQuery metricsQuery = this.load(metricCode);
        if (metricsQuery == null) {
            return null;
        }
        final String field = metricsQuery.getField();
        if (field != null) {
            return SQLParser.getAggsBuilder(agg, field, metricCode);
        }
        final String formula = metricsQuery.getFormula();
        if (formula != null) {
            return SQLParser.getAggsBuilder(formula, metricCode);
        }
        return null;
    }


    /**
     * 指标过滤
     *
     * @param predicate 过滤条件
     * @return
     */
    public List<MetricsQuery> filterMetrics(Predicate<MetricsQuery> predicate) {
        if (predicate == null) {
            return new ArrayList<>();
        }
        return openMetrics.values().stream().filter(predicate).collect(Collectors.toList());
    }

    public String encodedDatabaseName(String apikey, String metricCode) {
        log.debug("encodedDatabaseName apikey:{}, metricCode:{}", apikey, metricCode);
        MetricsQuery metricsQuery = this.load(metricCode);
        if (metricsQuery == null) {
            return null;
        }
        final String db = metricsQuery.getDatabase();
        if (!DATABASE_NAME_DATABUFF_SYSTEM.equals(db) && db != null) {
            return apikey + "_" + db;
        }
        return db;
    }

    public String encodedDatabaseName(String metricCode) {
        log.debug("encodedDatabaseName metricCode:{}", metricCode);
        MetricsQuery metricDicDbTable = this.load(metricCode);
        return null != metricDicDbTable ? metricDicDbTable.getDatabase() : null;
    }

    public String encodedTableName(String metricCode) {
        log.debug("encodedTableName metricCode:{}", metricCode);
        MetricsQuery metricQuery = this.load(metricCode);
        return metricQuery != null ? metricQuery.getMeasurement() : null;
    }


    public List<DatabuffMonitorView> listAllEnabledQuery(@Param("apiKey") String apiKey) {
        List<DatabuffMonitorView> result = new ArrayList<>();
        if (apiKey == null) {
            return result;
        }
        final boolean allEntityPermission = domainManagerObjService.hasAllEntityPermission();
        final Collection<String> gids = domainManagerObjService.getGidFromThread();
        return monitorMapper.listAllEnabledQuery(apiKey, allEntityPermission, gids);
    }

    /**
     * 该方法用于获取所有启用的组标签。
     * 首先检查apiKey是否为空，如果为空则返回空结果。
     * 如果apiKey不为null，则使用listAllEnabledTags方法获取所有启用的标签。
     * 对于启用标签中的每个tagKey，它从openMetricTagKeys映射中获取相应的MetricTagKey并将其设置为启用。
     * 最后，它返回 openMetricTagKeys 映射。
     *
     * @param apiKey 用于获取启用标签的 API 密钥。它不应该为空。
     * @return 标签键到 MetricTagKey 对象的映射。如果 apiKey 为 null，则返回一个空映射。
     */
    public Map<String, JSONObject> listAllEnabledGroupTag(String apiKey) {
        final Boolean listAllGroupTagEnabled = refreshScopeConfig.getListAllGroupTagEnabled();
        if (listAllGroupTagEnabled) {
            openMetricTagKeys.values().forEach(metricTagKey -> metricTagKey.setEnabled(true));
        } else {
            Map<String, JSONObject> result = new HashMap<>();
            if (apiKey == null) {
                return result;
            }
            for (String tagKey : listAllEnabledTags(apiKey)) {
                final MetricTagKey metricTagKey = openMetricTagKeys.get(tagKey);
                metricTagKey.setEnabled(true);
            }
        }
        //处理匹配的tagValue
        NavigableMap<String, JSONObject> tagKeys = new TreeMap<>();
        for (Map.Entry<String, MetricTagKey> entry : openMetricTagKeys.entrySet()) {
            MetricTagKey metricTagKey = entry.getValue();
            JSONObject tagValues = metricTagKey.getTagValue();
            JSONObject tagKeyValue = new JSONObject();
            tagKeyValue.put(ENABLED, metricTagKey.isEnabled());
            tagKeyValue.put(NAME, metricTagKey.getName());
            if (tagValues!=null && tagValues.containsKey(entry.getKey())){
                JSONObject obj = tagValues.getJSONObject(entry.getKey());
                tagKeyValue.put("tagValue", obj);
            }
            tagKeys.put(entry.getKey(), tagKeyValue);
        }
        // 2.7.10 增加k8sNamespace tag丰富
        if (tagKeys.containsKey(SERVICE)) {
            tagKeys.put("k8sNamespace", new JSONObject().fluentPut(ENABLED, true).fluentPut(NAME, "k8s 命名空间"));
            tagKeys.put("busName", new JSONObject().fluentPut(ENABLED, true).fluentPut(NAME, "业务系统"));
        }

        tagKeys.put(BIZ_EVENT_NAME, new JSONObject().fluentPut(ENABLED, true).fluentPut(NAME, "业务事件名称"));
        return tagKeys;
    }


    // 创建一个Guava Cache对象
    private final Cache<String, Set<String>> cache = CacheBuilder.newBuilder()
            .maximumSize(20)  // 设置缓存的最大容量
            .expireAfterWrite(1, TimeUnit.MINUTES)  // 设置缓存过期时间
            .build();

    @NotNull
    protected Set<String> listAllEnabledTags(String apiKey) {
        // 从缓存中获取数据
        Set<String> result = cache.getIfPresent(apiKey);
        if (result != null) {
            // 如果缓存中存在数据，直接返回
            return result;
        }
        // 如果缓存中不存在数据，从数据库中查询
        result = new HashSet<>();
        if (apiKey == null) {
            return result;
        }
        for (DatabuffMonitorView monitorView : listAllEnabledQuery(apiKey)) {
            if (monitorView == null) {
                continue;
            }
            final MultiDetectQueryRequest query = monitorView.getQuery();
            if (query == null) {
                continue;
            }
            result.addAll(query.findBy());
        }
        // 添加默认的标签
        result.add(RULE_NAME);
        // 将查询结果存入缓存
        cache.put(apiKey, result);
        return result;
    }

}
```

```java
package com.databuff.service;

import com.alibaba.fastjson.JSONObject;
import com.databuff.common.audit.AuditEntity;
import com.databuff.common.exception.ExceptionHandlingRunnable;
import com.databuff.dao.mysql.MetricsCoreMapper;
import com.databuff.dao.mysql.PluginMapper;
import com.databuff.entity.DatabuffPluginMetric;
import com.databuff.entity.MetricsCore;
import com.databuff.entity.MetricsQuery;
import com.databuff.util.MetricsTransformUtil;
import com.databuff.util.MetricsUtil;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.audit4j.core.annotation.DatabuffAudit;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.locks.Lock;
import java.util.concurrent.locks.ReentrantLock;
import java.util.stream.Collectors;

import static com.databuff.common.constants.Constant.DF_API_KEY_VALUE;

@Service
@Slf4j
public class MetricsCoreService {

    @Autowired
    private MetricsCoreMapper metricsCoreMapper;

    @Autowired
    private MetricsQueryService metricsQueryService;

    @Autowired
    private PluginMapper pluginMapper;

    private final Lock loadLock = new ReentrantLock();
    private volatile Map<String, MetricsCore> openMetricsCores = new ConcurrentHashMap<>();
    private ScheduledExecutorService scheduledExecutorService = Executors.newSingleThreadScheduledExecutor();
    private volatile Map<String, String> databases = new ConcurrentHashMap<>();

    @PostConstruct
    private void init() {
        load(null, true);
        scheduledExecutorService.scheduleAtFixedRate(new ExceptionHandlingRunnable(() -> load(null, true)), 1, 1, TimeUnit.MINUTES);
    }

    /**
     * 获取指定数据库和度量名称的指标核心配置
     *
     * 此方法在多线程环境下安全地访问共享状态
     *
     * @param database 数据库名称
     * @param measurement 度量名称
     * @return 匹配的指标核心配置，如果不存在则返回null
     */
    public MetricsCore findOpenOne(String database, String measurement) {
        String metricKey = MetricsUtil.generateMetricKey(database, measurement);
        if (metricKey == null) {
            return null;
        }

        return openMetricsCores.get(metricKey);
    }

    public Map<String, MetricsCore> findByApp(String app, Boolean open) {
        return doLoad(app, open);
    }

    /**
     * 获取所有已启用的指标核心配置
     *
     * 此方法直接返回内部Map，依赖ConcurrentHashMap的线程安全性
     *
     * @return 包含所有已启用指标核心配置的Map
     */
    public Map<String, MetricsCore> findOpenAll() {
        return openMetricsCores;
    }

    /**
     * 获取所有数据库信息
     *
     * 此方法直接返回内部Map，依赖ConcurrentHashMap的线程安全性
     *
     * @return 包含所有数据库信息的Map
     */
    public Map<String, String> findDatabases() {
        return databases;
    }


    public void load(String app, Boolean open) {
        Map<String, MetricsCore> localMetricsCoresMap;
        Map<String, String> localDatabaseMap;

        try {
            // 在锁外执行数据库查询，减少锁持有时间
            localMetricsCoresMap = doLoad(app, open);
            localDatabaseMap = new ConcurrentHashMap<>();
            for (Map.Entry<String, MetricsCore> entry : localMetricsCoresMap.entrySet()) {
                MetricsCore metricsCore = entry.getValue();
                localDatabaseMap.put(metricsCore.getDatabase(), metricsCore.getType1());
            }
        } catch (Throwable e) {
            log.error("加载指标数据失败", e);
            return;
        }

        // 只在更新共享状态时加锁，最小化锁的范围
        loadLock.lock();
        try {
            openMetricsCores = localMetricsCoresMap;
            databases = localDatabaseMap;
        } finally {
            loadLock.unlock();
        }
    }

    /**
     * 加载指定应用的指标核心配置
     *
     * 此方法从数据库中查询与给定应用相关的指标核心配置，并将它们映射到一个Map中
     * 如果找到了指标核心配置，它们将根据数据库和度量名称生成的键存储在Map中
     * 如果指标核心配置是内置的，则不会被覆盖
     *
     * 此方法已优化为线程安全，通过以下方式：
     * 1. 使用ConcurrentHashMap作为返回结果，提高并发访问性能
     * 2. 对每个MetricsCore对象进行防御性复制，避免多线程修改同一对象
     * 3. 在合并对象时使用线程安全的方式处理
     *
     * @param app 应用名称，用于查询指标核心配置
     * @param open 指示是否只加载启用的指标核心配置的布尔值
     * @return 返回一个Map，键是指标的唯一键（基于数据库和度量名称），值是MetricsCore对象
     */
    private Map<String, MetricsCore> doLoad(String app, Boolean open) {
        // 根据应用名称和启用状态查询指标核心配置列表
        List<MetricsCore> metricsCoresList = metricsCoreMapper.findByConditions(null, null, null, app, null, null, open, null, null, null, null);
        // 初始化ConcurrentHashMap，用于存储查询到的指标核心配置，提高并发性能
        Map<String, MetricsCore> metricsCoresMap = new ConcurrentHashMap<>(metricsCoresList == null ? 16 : metricsCoresList.size());

        // 如果查询结果为空，则直接返回空Map
        if (metricsCoresList == null) {
            return metricsCoresMap;
        }

        // 遍历查询到的指标核心配置列表
        for (MetricsCore metricsCore : metricsCoresList) {
            // 如果当前指标核心配置为空，则跳过
            if (metricsCore == null) {
                continue;
            }

            // 获取当前指标核心配置的数据库和度量名称
            String database = metricsCore.getDatabase();
            String measurement = metricsCore.getMeasurement();

            // 如果数据库或度量名称为空，则跳过当前指标核心配置
            if (database == null || database.isEmpty() || measurement == null || measurement.isEmpty()) {
                // 跳过数据库或度量为空的情况
                continue;
            }

            // 根据数据库和度量名称生成指标的唯一键
            String metricKey = MetricsUtil.generateMetricKey(database, measurement);
            // 如果无法生成指标的唯一键，则跳过当前指标核心配置
            if (metricKey == null) {
                // 跳过无法生成 metricKey 的情况
                continue;
            }

            // 检查Map中是否已存在当前指标的唯一键对应的指标核心配置
            MetricsCore existingMetricCore = metricsCoresMap.get(metricKey);

            // 如果Map中已存在当前指标的唯一键对应的指标核心配置
            if (existingMetricCore != null) {
                // 合并指标核心配置
                existingMetricCore.setNext(metricsCore);
//                mergeMetricsCore(existingMetricCore, metricsCore);
            } else {
                // 如果Map中不存在当前指标的唯一键对应的指标核心配置，则将其添加到Map中
                metricsCoresMap.put(metricKey, metricsCore);
            }
        }

        // 返回存储指标核心配置的Map
        return metricsCoresMap;
    }

    /**
     * 合并两个 MetricsCore 对象
     *
     * 此方法已优化以减少对象创建，提高性能：
     * 1. 直接在现有JSONObject上进行操作，避免不必要的复制
     * 2. 只在必要时创建新对象
     * 3. 内置指标（builtin为true的对象）优先，无论是目标还是源对象
     *
     * @param target 目标MetricsCore对象，将被更新
     * @param source 源MetricsCore对象，其属性将被合并到目标对象
     */
    private void mergeMetricsCore(MetricsCore target, MetricsCore source) {
        // 判断哪个对象是内置指标，内置指标优先
        boolean targetIsBuiltin = Boolean.TRUE.equals(target.getBuiltin());
        boolean sourceIsBuiltin = Boolean.TRUE.equals(source.getBuiltin());

        // 如果源对象是内置指标而目标对象不是，则交换合并方向
        if (sourceIsBuiltin && !targetIsBuiltin) {
            // 将非内置指标的目标对象合并到内置指标的源对象中
            mergeNonBuiltinToBuiltin(source, target);
            // 将合并后的源对象属性复制到目标对象
            copyProperties(source, target);
            return;
        }

        // 如果两个对象都是内置指标或都不是内置指标，或者目标对象是内置指标而源对象不是
        // 则按正常方式合并，将源对象属性合并到目标对象
        mergeProperties(target, source);
    }

    /**
     * 将非内置指标对象的属性合并到内置指标对象中
     * 内置指标对象的属性优先，只有内置指标对象中不存在的属性才会被合并
     *
     * @param builtinMetrics 内置指标对象
     * @param nonBuiltinMetrics 非内置指标对象
     */
    private void mergeNonBuiltinToBuiltin(MetricsCore builtinMetrics, MetricsCore nonBuiltinMetrics) {
        // 合并fields属性，内置指标对象的属性优先
        JSONObject builtinFields = builtinMetrics.getFields();
        JSONObject nonBuiltinFields = nonBuiltinMetrics.getFields();
        if (nonBuiltinFields != null && !nonBuiltinFields.isEmpty()) {
            if (builtinFields == null) {
                builtinMetrics.setFields(new JSONObject(nonBuiltinFields));
            } else {
                // 只添加内置指标对象中不存在的属性
                for (String key : nonBuiltinFields.keySet()) {
                    if (!builtinFields.containsKey(key)) {
                        builtinFields.put(key, nonBuiltinFields.get(key));
                    }
                }
            }
        }

        // 合并tagKey属性，内置指标对象的属性优先
        JSONObject builtinTagKey = builtinMetrics.getTagKey();
        JSONObject nonBuiltinTagKey = nonBuiltinMetrics.getTagKey();
        if (nonBuiltinTagKey != null && !nonBuiltinTagKey.isEmpty()) {
            if (builtinTagKey == null) {
                builtinMetrics.setTagKey(new JSONObject(nonBuiltinTagKey));
            } else {
                // 只添加内置指标对象中不存在的属性
                for (String key : nonBuiltinTagKey.keySet()) {
                    if (!builtinTagKey.containsKey(key)) {
                        builtinTagKey.put(key, nonBuiltinTagKey.get(key));
                    }
                }
            }
        }

        // 合并tagValue属性，内置指标对象的属性优先
        JSONObject builtinTagValue = builtinMetrics.getTagValue();
        JSONObject nonBuiltinTagValue = nonBuiltinMetrics.getTagValue();
        if (nonBuiltinTagValue != null && !nonBuiltinTagValue.isEmpty()) {
            if (builtinTagValue == null) {
                builtinMetrics.setTagValue(new JSONObject(nonBuiltinTagValue));
            } else {
                // 只添加内置指标对象中不存在的属性
                for (String key : nonBuiltinTagValue.keySet()) {
                    if (!builtinTagValue.containsKey(key)) {
                        builtinTagValue.put(key, nonBuiltinTagValue.get(key));
                    }
                }
            }
        }
    }

    /**
     * 将源对象的属性合并到目标对象中
     *
     * @param target 目标对象
     * @param source 源对象
     */
    private void mergeProperties(MetricsCore target, MetricsCore source) {
        // 合并fields属性
        JSONObject sourceFields = source.getFields();
        if (sourceFields != null && !sourceFields.isEmpty()) {
            JSONObject targetFields = target.getFields();
            if (targetFields == null) {
                // 只在目标字段为空时创建新对象
                target.setFields(new JSONObject(sourceFields));
            } else {
                // 直接在现有对象上添加属性，避免创建新对象
                for (String key : sourceFields.keySet()) {
                    targetFields.put(key, sourceFields.get(key));
                }
            }
        }

        // 合并tagKey属性
        JSONObject sourceTagKey = source.getTagKey();
        if (sourceTagKey != null && !sourceTagKey.isEmpty()) {
            JSONObject targetTagKey = target.getTagKey();
            if (targetTagKey == null) {
                // 只在目标字段为空时创建新对象
                target.setTagKey(new JSONObject(sourceTagKey));
            } else {
                // 直接在现有对象上添加属性
                for (String key : sourceTagKey.keySet()) {
                    targetTagKey.put(key, sourceTagKey.get(key));
                }
            }
        }

        // 合并tagValue属性
        JSONObject sourceTagValue = source.getTagValue();
        if (sourceTagValue != null && !sourceTagValue.isEmpty()) {
            JSONObject targetTagValue = target.getTagValue();
            if (targetTagValue == null) {
                // 只在目标字段为空时创建新对象
                target.setTagValue(new JSONObject(sourceTagValue));
            } else {
                // 直接在现有对象上添加属性
                for (String key : sourceTagValue.keySet()) {
                    targetTagValue.put(key, sourceTagValue.get(key));
                }
            }
        }
    }

    /**
     * 将源对象的属性复制到目标对象
     *
     * @param source 源对象
     * @param target 目标对象
     */
    private void copyProperties(MetricsCore source, MetricsCore target) {
        target.setFields(source.getFields() != null ? new JSONObject(source.getFields()) : null);
        target.setTagKey(source.getTagKey() != null ? new JSONObject(source.getTagKey()) : null);
        target.setTagValue(source.getTagValue() != null ? new JSONObject(source.getTagValue()) : null);
        // 保留目标对象的ID和其他必要属性
        // 不复制ID和其他唯一标识属性
    }

    /**
     * 根据条件查询指标核心数据
     *
     * @param type1 类型1
     * @param type2 类型2
     * @param type3 类型3
     * @param app 应用
     * @param database 数据库
     * @param measurement 度量
     * @param isOpen 是否开放
     * @param metricType 指标类型
     * @param metricSource 指标来源
     * @param basicJudgment 基本判断
     * @return 指标核心数据列表
     */
    public List<MetricsCore> findByConditions(String type1, String type2, String type3, String app,
                                              String database, String measurement, Boolean isOpen,
                                              String metricType, String metricSource, String basicJudgment) {
        return metricsCoreMapper.findByConditions(type1, type2, type3, app, database, measurement,
                isOpen, metricType, metricSource, basicJudgment, null);
    }

    /**
     * 根据ID获取指标核心数据
     *
     * @param id 指标ID
     * @return 指标核心数据
     */
    public MetricsCore findById(Integer id) {
        return metricsCoreMapper.findById(id);
    }

    /**
     * 添加指标核心数据
     *
     * @param metricsCore 指标核心数据
     * @return 添加结果信息
     */
    @DatabuffAudit(action = "添加", entityType = "指标核心数据")
    public void addMetricsCore(MetricsCore metricsCore) {
        validateAndProcessMetricsCore(metricsCore, false);
    }

    /**
     * 更新指标核心数据
     *
     * @param metricsCore 指标核心数据
     * @return 更新结果信息
     */
    @DatabuffAudit(action = "更新", entityType = "指标核心数据")
    public void updateMetricsCore(MetricsCore metricsCore) {
        if (metricsCore.getId() == null) {
            throw new RuntimeException("ID不能为空");
        }
        validateAndProcessMetricsCore(metricsCore, true);
    }

    /**
     * 验证并处理指标核心数据（添加或更新）
     *
     * @param metricsCore 指标核心数据
     * @param isUpdate 是否为更新操作
     */
    private void validateAndProcessMetricsCore(MetricsCore metricsCore, boolean isUpdate) {
        String operationType = isUpdate ? "更新" : "创建";

        // 参数校验
        if (metricsCore == null) {
            throw new IllegalArgumentException(operationType + "失败: 指标核心数据不能为空");
        }
        final String curentDatabase = metricsCore.getDatabase();
        if (curentDatabase == null || metricsCore.getMeasurement() == null) {
            throw new IllegalArgumentException(operationType + "失败: database 和 measurement 不能为空");
        }
        // 设置默认值
        metricsCore.setIsOpen(true);

        // 检查指标标识符是否重复
        checkDuplicateIdentifiers(metricsCore, operationType, isUpdate);

        // 验证指标核心数据
        validateMetricsCore(metricsCore, isUpdate, operationType, curentDatabase);

        // 保存指标核心数据
        saveMetricsCore(metricsCore, isUpdate);

        // 重新加载指标数据
        try {
            metricsQueryService.reloadAllMetrics();
        } catch (Exception e) {
            // 避免 reload 失败导致事务回滚，可以选择记录日志后继续提交
            log.error("重新加载指标数据失败", e);
        }
    }


    /**
     * 删除指标核心数据
     *
     * @param identifier 指标标识符
     * @return 删除结果信息
     */
    @DatabuffAudit(action = "删除", entityType = "指标核心数据")
    public void deleteMetricsCore(String identifier) {
        final MetricsQuery openByIdentifier = metricsQueryService.findOpenByIdentifier(identifier);
        if (openByIdentifier == null) {
            throw new RuntimeException("删除失败: 指标不存在");
        }
        final Boolean builtin = openByIdentifier.getBuiltin();
        if (builtin != null && builtin) {
            throw new RuntimeException("删除失败: 内置指标不允许删除");
        }
        final Long id = openByIdentifier.getId();
        if (id == null || id.intValue() != id.longValue()) {
            throw new RuntimeException("删除失败: ID不能为空");
        }
        final MetricsCore byId = metricsCoreMapper.findById(id.intValue());
        if (byId == null) {
            throw new RuntimeException("删除失败: 指标不存在");
        }
        final String measurement = byId.getMeasurement();
        final JSONObject fields = byId.getFields();
        if (fields == null) {
            throw new RuntimeException("删除失败: 指标不存在");
        }
        if (!identifier.startsWith(measurement + ".")) {
            throw new RuntimeException("删除失败: 指标不存在");
        }
        String fieldKey = identifier.substring(measurement.length() + 1);
        fields.remove(fieldKey);
        if (fields.isEmpty()) {
            metricsCoreMapper.delete(id.intValue());
        } else {
            metricsCoreMapper.update(byId);
        }

        this.disableMetric(identifier);

        // 重新加载指标数据
        try {
            metricsQueryService.reloadAllMetrics();
        } catch (Exception e) {
            // 避免 reload 失败导致事务回滚，可以选择记录日志后继续提交
            log.error("重新加载指标数据失败", e);
        }
    }

    /**
     * 统一更新指标目录名称
     * 根据传入的参数组合执行不同的更新操作
     * <p>
     * 处理逻辑：
     * 1. 若type3不等于null执行update dc_databuff_metrics_core set type3=xxx where type1=xxx and type2=xxx
     * 2. 若type3为null且type2不等于null执行update dc_databuff_metrics_core set type2=xxx where type1=xxx
     * 3. 若type3为null且type2为null且type1不等于null执行update dc_databuff_metrics_core set type1=xxx
     * 4. 其他情况不执行提示参数错误
     *
     * @param oldType1 旧的一级目录名称（查询条件）
     * @param oldType2 旧的二级目录名称（查询条件）
     * @param newType1 新的一级目录名称（要更新的值）
     * @param newType2 新的二级目录名称（要更新的值）
     * @param newType3 新的三级目录名称（要更新的值）
     * @return 更新结果信息
     */
    @DatabuffAudit(action = "更新", entityType = "指标目录")
    public void updateTypeName(String oldType1, String oldType2, String newType1, String newType2, String newType3) {
        int count = 0;
        if (newType3 != null) {
            count = metricsCoreMapper.updateType3(oldType1, oldType2, newType3);
        } else if (newType2 != null) {
            count = metricsCoreMapper.updateType2(oldType1, newType2);
        } else if (newType1 != null) {
            count = metricsCoreMapper.updateType1(oldType1, newType1);
        } else {
            throw new RuntimeException("参数错误，没有指定要更新的值");
        }

        if (count == 0) {
            throw new RuntimeException("未找到匹配的目录或没有记录被更新");
        }
        // 重新加载指标数据
        try {
            metricsQueryService.reloadAllMetrics();
        } catch (Exception e) {
            // 避免 reload 失败导致事务回滚，可以选择记录日志后继续提交
            log.error("重新加载指标数据失败", e);
        }
    }

    /**
     * 根据type1、type2、type3级目录删除关联指标
     *
     * 处理逻辑：
     * 1. 若type3不等于null，则删除type1=xxx and type2=xxx and type3=xxx的指标
     * 2. 若type3为null且type2不等于null，则删除type1=xxx and type2=xxx的指标
     * 3. 若type3为null且type2为null且type1不等于null，则删除type1=xxx的指标
     * 4. 其他情况不执行提示参数错误
     *
     * @param type1 一级目录名称
     * @param type2 二级目录名称
     * @param type3 三级目录名称
     * @return 删除结果信息
     */
    @DatabuffAudit(action = "删除", entityType = "指标目录")
    public void deleteMetricsByType(String type1, String type2, String type3) {
        try {
            // 参数验证
            if (type1 == null && type2 == null && type3 == null) {
                throw new RuntimeException("参数错误，至少需要指定一个目录级别");
            }

            // 查询符合条件的指标
            List<MetricsCore> metricsCores = metricsCoreMapper.findByConditions(type1, type2, type3, null, null, null, null, null, null, null, false);
            if (metricsCores == null || metricsCores.isEmpty()) {
                throw new RuntimeException("未找到匹配的指标");
            }

            // 收集所有指标的identifier
            List<String> identifiers = new ArrayList<>();
            for (MetricsCore metricsCore : metricsCores) {
                // 获取指标的measurement和fields
                String measurement = metricsCore.getMeasurement();
                JSONObject fields = metricsCore.getFields();
                if (fields != null) {
                    // 遍历fields，生成identifier
                    for (String fieldKey : fields.keySet()) {
                        String identifier = measurement + "." + fieldKey;
                        identifiers.add(identifier);
                    }
                }

                // 删除指标数据
                metricsCoreMapper.delete(metricsCore.getId().intValue());
            }

            // 清除缓存
            for (String identifier : identifiers) {
                try {
                    metricsQueryService.deleteCache(identifier);
                } catch (Exception e) {
                    log.warn("清除指标缓存失败: " + identifier, e);
                }
            }

            // 添加审计日志
            String result = "删除成功: 已删除" + metricsCores.size() + "条指标记录，" + identifiers.size() + "个指标标识符";
            AuditEntity.builder()
                    .action("删除指标")
                    .id(String.join("/", Arrays.asList(type1, type2, type3)))
                    .desc(result)
                    .add();
        } catch (Exception e) {
            log.error("删除指标失败", e);
            AuditEntity.builder()
                    .action("删除指标")
                    .outcome("失败")
                    .errorMessage(e.getMessage())
                    .add();
            throw new RuntimeException("删除失败: " + e.getMessage(), e);
        }

        // 重新加载指标数据
        try {
            metricsQueryService.reloadAllMetrics();
        } catch (Exception e) {
            // 避免 reload 失败导致事务回滚，可以选择记录日志后继续提交
            log.error("重新加载指标数据失败", e);
        }
    }

    /**
     * 启用指标
     *
     * @param identifier 指标标识符
     * @return 启用结果信息
     */
    @DatabuffAudit(action = "启用", entityType = "指标元数据")
    public void enableMetric(String identifier) {
        try {
            String result = updateMetricStatus(identifier, true);
            AuditEntity.builder()
                    .action("启用指标")
                    .id(identifier)
                    .desc(result)
                    .add();
        } catch (Exception e) {
            log.error("启用指标失败", e);
            AuditEntity.builder()
                    .action("启用指标")
                    .outcome("失败")
                    .errorMessage(e.getMessage())
                    .add();
            throw e;
        }
    }

    /**
     * 停用指标
     *
     * @param identifier 指标标识符
     * @return 停用结果信息
     */
    @DatabuffAudit(action = "停用", entityType = "指标元数据")
    public String disableMetric(String identifier) {
        try {
            String result = updateMetricStatus(identifier, false);
            AuditEntity.builder()
                    .action("停用指标")
                    .id(identifier)
                    .desc(result)
                    .add();
            return result;
        } catch (Exception e) {
            log.error("停用指标失败", e);
            AuditEntity.builder()
                    .action("停用指标")
                    .outcome("失败")
                    .errorMessage(e.getMessage())
                    .add();
            return "停用失败: " + e.getMessage();
        }
    }

    /**
     * 更新指标状态（启用/停用）
     *
     * @param identifier 指标标识符
     * @param isOpen 是否启用
     * @return 更新结果信息
     */
    private String updateMetricStatus(String identifier, boolean isOpen) {
        final MetricsQuery openByIdentifier = metricsQueryService.findOpenByIdentifier(identifier);
        if (openByIdentifier == null) {
            return (isOpen ? "启用" : "停用") + "失败: 指标不存在";
        }

        final String app = openByIdentifier.getApp();
        DatabuffPluginMetric pluginOpenMetrics = pluginMapper.getPluginOpenMetrics(app, DF_API_KEY_VALUE);
        if (pluginOpenMetrics == null) {
            // 若根据app和DF_API_KEY_VALUE查不到dc_databuff_plugin_metric，则创建并insert dc_databuff_plugin_metric 记录
            log.info("未找到app=[{}]的插件指标记录，将创建新记录", app);

            // 创建新的DatabuffPluginMetric对象
            pluginOpenMetrics = new DatabuffPluginMetric();
            pluginOpenMetrics.setApiKey(DF_API_KEY_VALUE);
            pluginOpenMetrics.setApp(app);
            pluginOpenMetrics.setVersion(1); // 初始版本号为1
            pluginOpenMetrics.setUpdateTime(new Date());

            // 创建初始的openMetrics JSON结构
            JSONObject initialOpenMetrics = new JSONObject();
            JSONObject categoryObj = new JSONObject();
            categoryObj.put("", Lists.newArrayList(identifier)); // 创建空的category数组
            initialOpenMetrics.put(app, categoryObj); // 使用app名称作为key

            // 设置openMetrics
            pluginOpenMetrics.setOpenMetrics(initialOpenMetrics.toJSONString());
        }

        final JSONObject jsonObject = JSONObject.parseObject(pluginOpenMetrics.getOpenMetrics());
        final ArrayList<MetricsQuery> metricsInserts = Lists.newArrayList(openByIdentifier);
        if (isOpen) {
            MetricsTransformUtil.query2Insert(metricsInserts, jsonObject);
        } else {
            MetricsTransformUtil.query2remove(metricsInserts, jsonObject);
        }
        pluginOpenMetrics.setOpenMetrics(jsonObject.toJSONString());

        pluginOpenMetrics.setApp(app);
        pluginOpenMetrics.setVersion(pluginOpenMetrics.getVersion() + 1);
        pluginOpenMetrics.setUpdateTime(new Date());

        try {
            // 保存到数据库
            pluginMapper.savePluginOpenMetrics(pluginOpenMetrics);
            log.info("成功创建app=[{}]的插件指标记录", app);
        } catch (Exception e) {
            log.error("创建app=[{}]的插件指标记录失败", app, e);
            return "更新指标状态失败: 无法创建插件指标记录";
        }

        // 更新指标状态
        openByIdentifier.setIsOpen(isOpen);

        return "指标" + (isOpen ? "启用" : "停用") + "成功";
    }

    /**
     * 检查指标标识符是否重复
     *
     * @param metricsCore 指标核心数据
     * @param operationType 操作类型（创建或更新）
     * @param isUpdate 是否为更新操作
     * @throws IllegalArgumentException 如果指标标识符重复，抛出异常
     */
    private void checkDuplicateIdentifiers(MetricsCore metricsCore, String operationType, boolean isUpdate) {
        if (metricsCore == null) {
            throw new IllegalArgumentException(operationType + "失败: 指标核心数据不能为空");
        }
        // 获取指标查询映射
        final Map<String, MetricsQuery> metricsQueryMap = metricsCore.toMetricsQueryMap();
        if (metricsQueryMap == null || metricsQueryMap.isEmpty()) {
            throw new IllegalArgumentException(operationType + "失败: 指标字段信息不能为空");
        }

        // 遍历每个指标查询，检查标识符是否重复
        for (Map.Entry<String, MetricsQuery> entry : metricsQueryMap.entrySet()) {
            final String identifier = entry.getValue().getIdentifier();
            final MetricsQuery openByIdentifier = metricsQueryService.findOpenByIdentifier(identifier);

            // 如果找到已存在的指标
            if (openByIdentifier != null) {
                // 如果是更新操作，需要判断是否是同一个指标
                if (isUpdate
                        && metricsCore.getId() != null
                        && openByIdentifier.getId() != null
                        && Objects.equals(openByIdentifier.getId(), metricsCore.getId())) {
                    // 如果是同一个指标（ID相同），则允许更新
                    continue;
                }

                // 如果不是同一个指标或不是更新操作，抛出异常
                throw new IllegalArgumentException(buildDuplicateIdentifierErrorMessage(identifier, openByIdentifier, operationType));
            }
        }
    }


    /**
     * 验证指标核心数据中type1和database的关系
     *
     * @param metricsCore 指标核心数据
     * @param isUpdate 是否为更新操作
     * @param operationType 操作类型（创建或更新）
     * @param currentDatabase 当前数据库名称
     * @throws RuntimeException 如果验证失败，抛出异常
     */
    private void validateMetricsCore(MetricsCore metricsCore, boolean isUpdate, String operationType, String currentDatabase) {
        // 查询同一type1的所有记录
        List<MetricsCore> existingRecords = metricsCoreMapper.findByConditions(
                metricsCore.getType1(), null, null, null,
                null, null,
                null, null, null, null, null);

        if (existingRecords != null && !existingRecords.isEmpty()) {
            // 根据是否为更新操作，过滤当前记录并收集数据库名称
            final Set<String> databaseSet;
            if (isUpdate) {
                databaseSet = existingRecords.stream()
                        .filter(record -> !record.getId().equals(metricsCore.getId()))
                        .map(i -> i.getDatabase()).collect(Collectors.toSet());
            } else {
                databaseSet = existingRecords.stream()
                        .map(i -> i.getDatabase()).collect(Collectors.toSet());
            }

            // 因为业务逻辑规定，type1对应的database只能创建一个（一一对应）
            if (!(databaseSet.isEmpty() || databaseSet.contains(currentDatabase))) {
                // 之前创建过 type1，但是不是同一个database，则不允许创建/更新
                throw new RuntimeException(String.format(operationType + "失败: 一级分类[%s]对应的数据库名称：%s ,建议修改对应数据库名称",
                        metricsCore.getType1(), databaseSet));
            }
            // 如果验证通过，方法结束，不抛出异常
        }
        // 没有找到同一type1的记录或验证通过，方法结束
    }

    /**
     * 保存指标核心数据
     *
     * @param metricsCore 指标核心数据
     * @param isUpdate 是否为更新操作
     */
    private void saveMetricsCore(MetricsCore metricsCore, boolean isUpdate) {
        if (isUpdate) {
            metricsCoreMapper.update(metricsCore);
        } else {
            metricsCoreMapper.insert(metricsCore);
        }
    }

    /**
     * 构建标识符重复的错误信息
     *
     * @param identifier 重复的标识符
     * @param existingMetric 已存在的指标
     * @param operationType 操作类型（创建或更新）
     * @return 格式化的错误信息
     */
    private String buildDuplicateIdentifierErrorMessage(String identifier, MetricsQuery existingMetric, String operationType) {
        StringBuilder errorMsg = new StringBuilder(operationType + "失败: 标识符 '" + identifier + "' 已存在");

        // 添加分类信息
        String type1 = existingMetric.getType1();
        String type2 = existingMetric.getType2();
        String type3 = existingMetric.getType3();
        errorMsg.append("\n已存在指标的分类: ");
        errorMsg.append(type1 != null ? type1 : "");
        if (type2 != null) {
            errorMsg.append(" > ").append(type2);
        }
        if (type3 != null) {
            errorMsg.append(" > ").append(type3);
        }

        // 添加数据库和度量信息
        String database = existingMetric.getDatabase();
        String measurement = existingMetric.getMeasurement();
        if (database != null) {
            errorMsg.append("\n数据库: ").append(database);
        }
        if (measurement != null) {
            errorMsg.append("\n度量名称: ").append(measurement);
        }

        // 添加指标中文名和描述信息
        String metricCn = existingMetric.getMetricCn();
        String desc = existingMetric.getDesc();
        if (metricCn != null) {
            errorMsg.append("\n指标名称: ").append(metricCn);
        }
        if (desc != null) {
            errorMsg.append("\n描述: ").append(desc);
        }

        // 添加建议信息
        errorMsg.append("\n\n建议: 请修改指标标识符或字段名后再保存");

        return errorMsg.toString();
    }

    /**
     * 根据type1、type2、type3查询不重复的app、database和measurement
     *
     * @param type1 类型1
     * @param type2 类型2
     * @param type3 类型3
     * @return 包含app集合、database集合和measurement集合的Map
     */
    public Map<String, Set<String>> findDistinctAppDatabaseMeasurementByTypes(String type1, String type2, String type3) {
        // 查询数据库获取结果
        List<Map<String, Object>> results = metricsCoreMapper.findDistinctAppDatabaseMeasurementByTypes(type1, type2, type3);

        // 创建三个集合用于存储不重复的值
        Set<String> appSet = new HashSet<>();
        Set<String> databaseSet = new HashSet<>();
        Set<String> measurementSet = new HashSet<>();

        // 处理查询结果，将值添加到对应的集合中
        if (results != null && !results.isEmpty()) {
            for (Map<String, Object> result : results) {
                if (result.get("app") != null) {
                    appSet.add(result.get("app").toString());
                }
                if (result.get("database") != null) {
                    databaseSet.add(result.get("database").toString());
                }
                if (result.get("measurement") != null) {
                    measurementSet.add(result.get("measurement").toString());
                }
            }
        }

        // 创建返回结果Map
        Map<String, Set<String>> resultMap = new HashMap<>();
        resultMap.put("app", appSet);
        resultMap.put("database", databaseSet);
        resultMap.put("measurement", measurementSet);

        return resultMap;
    }
}
```



#### 指标元数据批量导入
执行 mpd.sql 批量导入指标数据(dc_databuff_metrics_insert,dc_databuff_metrics_core)



#### 指标加载流程图
![画板](https://cdn.nlark.com/yuque/0/2025/jpeg/40720541/1748337319452-13795f79-ca01-4835-997e-6bdfb78e7e16.jpeg)

#### 创建/更新指标流程图
![画板](https://cdn.nlark.com/yuque/0/2025/jpeg/40720541/1748337516806-68a61666-d0ba-4a1b-a411-c27868d016b4.jpeg)

#### 指标元数据接口
指标缓存机制：系统使用内存缓存来提高指标查询性能:

+ 使用Guava Cache缓存标签值，设置1分钟过期时间
+ 指标元数据加载到内存中，通过`reloadAllMetrics()`方法刷新
+ 指标操作(增删改)后自动刷新缓存

```java
private NavigableMap<String, MetricsQuery> loadByApp(boolean forceReload) {
    NavigableMap<String, MetricsQuery> all = new TreeMap<>();
    try {
        if (forceReload) {
            metricsCoreService.load(null, true);
        }

        Map<String, MetricsCore> metricsCores = metricsCoreService.findOpenAll();
        convertMetricsCore(all, metricsCores);

        Map<String, MetricsQuery> metricsQueries = this.findOpenAll();
        all.putAll(metricsQueries);

        // 主机标签丰富
        final List<String> hostKeys = tagHostRelationEntityMapper.selectDistinctTagKeys();
        if (CollectionUtils.isNotEmpty(hostKeys)) {
            JSONObject host = new JSONObject().fluentPut(HOST, hostKeys);
            for (Map.Entry<String, MetricsQuery> entry : all.entrySet()) {
                final MetricsQuery metricsQuery = entry.getValue();
                if (metricsQuery == null) {
                    return null;
                }
                final String type2 = metricsQuery.getType2();
                if ("主机".equals(type2)) {
                    metricsQuery.setKeys(host);
                }
            }
        }

    } catch (Throwable e) {
        log.error("load error", e);
    }

    openMetricTagKeys = new TreeMap<>();
    openMetricTypes = new TreeSet<>();
    for (Map.Entry<String, MetricsQuery> entry : all.entrySet()) {
        final MetricsQuery metricsQuery = entry.getValue();
        if (metricsQuery == null) {
            continue;
        }

        DatabuffMetricTypeDto databuffMetricTypeDto = new DatabuffMetricTypeDto();
        databuffMetricTypeDto.setType1(metricsQuery.getType1());
        databuffMetricTypeDto.setType2(metricsQuery.getType2());
        databuffMetricTypeDto.setType3(metricsQuery.getType3());
        databuffMetricTypeDto.setApp(metricsQuery.getApp());
        databuffMetricTypeDto.setBuiltin(metricsQuery.getBuiltin());
        openMetricTypes.add(databuffMetricTypeDto);

        final JSONObject tagKey = metricsQuery.getTagKey();
        if (tagKey != null) {
            for (Map.Entry<String, Object> tag : tagKey.entrySet()) {
                final String key = tag.getKey();
                final String value = (String) tag.getValue();
                MetricTagKey tagValue = openMetricTagKeys.get(key);
                final JSONObject metricsQueryTagValue = metricsQuery.getTagValue();
                if (tagValue != null) {
                    tagValue.getNames().add(value);
                } else {
                    tagValue = MetricTagKey.builder()
                            .tagKey(key)
                            .names(new HashSet<>(Collections.singletonList(value)))
                            .tagValue(new JSONObject())
                            .build();
                    openMetricTagKeys.put(key, tagValue);
                }
                if (metricsQueryTagValue != null) {
                    tagValue.getTagValue().putAll(metricsQueryTagValue);
                }
            }
        }
    }
    log.debug("loadByApp all.size:{}", all.size());
    log.debug("loadByApp openMetricTagKeys.size:{}", openMetricTagKeys.size());
    final List<DatabuffPluginMetric> allPluginOpenMetrics = pluginMapper.getAllPluginOpenMetrics(DF_API_KEY_VALUE);
    if (allPluginOpenMetrics == null) {
        return all;
    }
    for (DatabuffPluginMetric allPluginOpenMetric : allPluginOpenMetrics) {
        if (allPluginOpenMetric == null) {
            continue;
        }
        final JSONObject openMetrics = JSONObject.parseObject(allPluginOpenMetric.getOpenMetrics());
        if (openMetrics == null) {
            continue;
        }
        for (Map.Entry<String, Object> entry : openMetrics.entrySet()) {
            if (entry == null) {
                continue;
            }
            final Object value = entry.getValue();
            if (value instanceof Map) {
                for (Map.Entry<String, Object> entry1 : ((Map<String, Object>) value).entrySet()) {
                    final Object metrics = entry1.getValue();
                    if (metrics instanceof Collection) {
                        for (Object metric : (Collection) metrics) {
                            final MetricsQuery metricsQuery = all.get(metric);
                            if (metricsQuery == null) {
                                continue;
                            }
                            metricsQuery.setIsOpen(true);
                        }
                    }
                }
            }
        }
    }
    return all;
}
```

```java
public synchronized void reloadAllMetrics() {
    openMetrics = loadByApp(true);
}
```





#### 待优化
指标增删改查接口建议重构：**<font style="color:#080808;background-color:#ffffff;">MetricsCoreController</font>**

**<font style="color:#080808;background-color:#ffffff;">目前问题</font>**<font style="color:#080808;background-color:#ffffff;">：</font>

1. 自定义指标元数据和核心指标元数据都存储在dc_databuff_metrics_core表中，这导致初始化脚本执行逻辑复杂。
2. dc_databuff_metrics_core的表结构不适合产品设定的指标元数据存储格式。

**改进建议**：

1. 新增一个自定义指标元数据表来存储用户的指标元数据，可参考dc_databuff_metrics_query表。
2. 使用简单的增删改查接口来替换现在复杂的修改core表逻辑。
3. 参考**加载元数据**逻辑将自定义的元数据加载到缓存中。

