

#### 背景：
前端数据格式到TSDB的封装层，为了满足检测规则，指标预览等需求抽象出来的层，数据格式和TSDB层有较大的差异，所以不能完全去除。



#### 指标查询最基本流程图
![画板](https://cdn.nlark.com/yuque/0/2025/jpeg/40720541/1748326615568-64379677-0589-4d9f-8a4f-76fd13e65d15.jpeg)

#### 核心DTO
##### 脑图
![画板](https://cdn.nlark.com/yuque/0/2025/jpeg/40720541/1748326104428-49239c66-d53c-4014-b8ba-465731dbc1f4.jpeg)

##### 基础
QueryRequest包含FilterCondition

QueryRequest对象可以被转换成TSDB层的**<font style="color:#080808;background-color:#ffffff;">QueryBuilder</font>**

```java
package com.databuff.common.tsdb.dto.explore;

import com.databuff.common.tsdb.builder.QueryBuilderX;
import com.databuff.common.tsdb.dto.CompositeCondition;
import com.databuff.common.tsdb.dto.preview.OrderDTO;
import com.databuff.common.tsdb.model.Aggregation;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.*;

/**
 * 表示查询请求的DTO对象，包含查询条件、表达式、时间范围及间隔等参数。
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class QueryRequest {

    @ApiModelProperty(
            value = "时间戳偏移量（单位：毫秒），用于调整查询时间窗口",
            example = "3600000"
    )
    private int timeOffset;

    @ApiModelProperty(
            value = "当前统计时间戳（单位：毫秒），用于相对时间计算基准",
            example = "1717027200000"
    )
    private long currentStatTime;

    /**
     * npm 专用
     */
    private String name;
    /**
     * npm 专用
     */
    private String source;
    /**
     * npm 专用
     */
    private String target;

    /**
     * 排序参数
     */
    private OrderDTO order;

    @ApiModelProperty(value = "聚合函数")
    private List<Aggregation> aggregations;

    /**
     * 将指定的聚合对象添加到查询请求的聚合集合中，并返回当前实例以支持链式调用。
     *
     * @param aggregation 需要添加的聚合对象
     * @return 当前QueryRequest实例，用于链式调用
     */
    public QueryRequest addAgg(Aggregation aggregation) {
        if (aggregation == null) {
            return this;
        }
        ensureAggregationsInitialized();
        this.aggregations.add(aggregation);
        return this;
    }


    /**
     * 将给定的聚合列表全部添加到当前查询请求中
     *
     * @param aggregations 需要添加的聚合操作列表
     * @return 当前查询请求对象（支持链式调用）
     */
    public QueryRequest addAllAgg(List<Aggregation> aggregations) {
        if (aggregations == null) {
            return this;
        }
        ensureAggregationsInitialized();
        for (Aggregation agg : aggregations) {
            if (agg != null) {
                this.aggregations.add(agg);
            }
        }
        return this;
    }


    private synchronized void ensureAggregationsInitialized() {
        if (this.aggregations == null) {
            this.aggregations = new ArrayList<>();
        }
    }

    @ApiModelProperty(value = "标签白名单", example = "[\"error\", \"warning\"]")
    private Collection<String> whiteKeys;

    @ApiModelProperty(
            value = "指标名称（单指标查询专用），如CPU使用率",
            example = "cpu_usage",
            notes = "当metrics字段存在时，该字段会被忽略"
    )
    private String metric;

    /**
     * 获取指标列表
     *
     * @return
     */
    public String getMetric() {
        if (metric != null) {
            return metric;
        }
        if (metrics != null && metrics.size() > 0) {
            return metrics.iterator().next();
        }
        return null;
    }

    @ApiModelProperty(value = "指标名称，如CPU使用率", example = "cpu_usage")
    private Collection<String> metrics = new HashSet<>();

    /**
     * 获取指标列表
     *
     * @return
     */
    public Collection<String> getMetrics() {
        return metrics;
    }

    /**
     * 添加单个指标到集合
     *
     * @param metric 需要添加的指标
     */
    public synchronized void addMetric(String metric) {
        if (metric != null) {
            if (metrics.isEmpty()) {
                metrics = new HashSet<>();
            }
            metrics.add(metric);
        }
    }

    @ApiModelProperty(value = "数据库", example = "databuff")
    private String db;

    @ApiModelProperty(value = "表名", example = "cpu")
    private String tb;

    @ApiModelProperty(value = "聚合函数类型，如平均值/最大值", example = "mean")
    private String aggs;

    @ApiModelProperty(value = "分组字段列表", example = "[\"host\", \"service\"]")
    private Collection<String> by;

    @ApiModelProperty(value = "类型过滤条件", example = "[\"error\", \"warning\"]")
    private List<String> types;

    @ApiModelProperty(value = "复合过滤条件列表", example = "[{\"type\":\"composite\", \"connector\":\"AND\"}]")
    private Collection<CompositeCondition> from;

    @ApiModelProperty(value = "扩展查询条件列表", example = "[{\"type\":\"composite\", \"connector\":\"AND\"}]")
    private Collection<CompositeCondition> fromExt;

    public QueryRequest addFrom(CompositeCondition compositeCondition) {
        if (compositeCondition == null) {
            throw new NullPointerException("compositeCondition must not be null");
        }
        ensureFromInitialized();
        this.from.add(compositeCondition);
        return this;
    }

    public Collection<CompositeCondition> getFrom() {
        ensureFromInitialized();
        return Collections.unmodifiableCollection(from);
    }

    private void ensureFromInitialized() {
        if (from == null) {
            from = new ArrayList<>();
        }
    }

    @ApiModelProperty(value = "分页下拉关键字", example = "system.core.usage")
    private String lastKey;

    @ApiModelProperty(value = "分页页码", example = "1")
    private int pageNum;

    @ApiModelProperty(value = "每页记录数", example = "20")
    private int pageSize;

    @ApiModelProperty(value = "是否禁用分页", example = "false")
    private boolean noPage;

    @ApiModelProperty(value = "查询的开始时间戳，单位为毫秒", example = "1609459200000")
    private Long start;

    @ApiModelProperty(value = "查询的结束时间戳，单位为毫秒", example = "1609462800000")
    private Long end;

    @ApiModelProperty(value = "最近时间区间，单位为秒", example = "3600")
    private Integer period;

    @ApiModelProperty(value = "查询时间间隔，单位为秒", example = "60")
    private Integer interval;

    @ApiModelProperty(value = "API密钥，用于身份认证", example = "your_api_key_123")
    private String apiKey;

    @ApiModelProperty(value = "是否包含注释信息，默认为false", example = "true")
    private boolean hasAnnotate;

    @ApiModelProperty(value = "是否拥有所有权限", example = "true")
    private Boolean allPermission;

    @ApiModelProperty(value = "是否开启领域管理状态", example = "false")
    private Boolean domainManagerStatusOpen;

    @ApiModelProperty(value = "组ID集合，用于过滤查询条件", example = "[\"group1\", \"group2\"]")
    private Collection<String> gids;

    public QueryBuilderX convert() {
        QueryBuilderX builder = new QueryBuilderX();

        // 基础参数映射
        builder.setMetric(this.getMetric());

        // 时间偏移量映射
        final int timeOffsetMS = this.getTimeOffset() * 1000;

        // 时间范围映射 - 拆分start和end分别添加到builder
        Long start = this.getStart();
        Long end = this.getEnd();

        // 时间范围映射
        final Integer period = getPeriod();
        if (start == null && end == null && period != null && period > 0) {
            final long nowMs = System.currentTimeMillis();
            builder.start(nowMs - period * 1000 - timeOffsetMS);
            builder.end(nowMs - timeOffsetMS);
        } else {
            if (start != null) {
                builder.start(start - timeOffsetMS);
            }
            if (end != null) {
                builder.end(end - timeOffsetMS);
            }
        }

        // 处理Interval字段的空值风险
        Integer interval = this.getInterval();
        if (interval != null) {
            builder.setInterval(interval.intValue());
        }

        // 分页参数映射（启用逻辑，假设QueryBuilderX支持相关接口）
        builder.setPage(this.getPageSize(), this.getPageNum());

        return builder;
    }
}
```

```java
package com.databuff.common.tsdb.dto;

import com.databuff.common.tsdb.model.LogicalOperator;
import com.databuff.common.tsdb.model.WhereOp;
import com.fasterxml.jackson.annotation.JsonInclude;

/**
 * 定义过滤条件的接口，包含左操作数、操作符和右操作数。
 * @param <X> 左操作数的类型
 * @param <Y> 右操作数的类型
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
public interface FilterCondition<X, Y> {
    /**
     * 获取过滤条件的类型标识。
     * @return 条件类型标识字符串（默认为空）
     */
    default String getType() {
        return "";
    }

    /**
     * 获取过滤条件的左操作数。
     * @return 左操作数的值
     */
    X getLeft();

    /**
     * 获取过滤条件的操作符。
     * @return 用于比较的运算符字符串（如"="、">"等）
     */
    WhereOp getOperatorEnum();

    /**
     * 获取过滤条件的大小写不敏感标识符。
     * @return 指示是否启用大小写不敏感的标识符字符串（如"CASE_INSENSITIVE"）
     */
    Boolean getCaseInsensitive();

    /**
     * 获取连接当前条件与其他条件的逻辑运算符。
     * @return 逻辑连接符字符串（如"AND"或"OR"）
     */
    LogicalOperator getConnector();

    /**
     * 获取过滤条件的右操作数。
     * @return 右操作数的值
     */
    Y getRight();
}
```

```java
package com.databuff.common.tsdb.dto;

import com.databuff.common.tsdb.model.LogicalOperator;
import com.databuff.common.tsdb.model.WhereOp;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 表示一个复合条件，通过指定的连接符（如AND或OR）将左右两侧的条件列表组合起来。
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class CompositeCondition implements FilterCondition {

    @ApiModelProperty(value = "条件类型，固定为composite", allowableValues = "composite")
    private String type = "composite";

    @ApiModelProperty(value = "逻辑操作符，支持AND/OR", example = "AND", allowableValues = "AND,OR")
    private LogicalOperator connector;

    @ApiModelProperty(value = "左侧条件表达式（可以是简单条件或复合条件）", example = "{\"field\":\"host\", \"operator\":\"=\", \"value\":\"server1\"}")
    private Object left;

    @ApiModelProperty(value = "右侧条件表达式（可以是简单条件或复合条件）", example = "{\"field\":\"service\", \"operator\":\"!=\", \"value\":\"web\"}")
    private Object right;

    @ApiModelProperty(value = "是否启用大小写不敏感匹配", example = "false")
    private Boolean caseInsensitive;

    @ApiModelProperty(value = "操作符符号（如=、!=、>等）", example = ">", allowableValues = "==,!=,>,<,>=,<=,IN,NOT IN")
    private String operator;

    @ApiModelProperty(hidden = true)
    public WhereOp getOperatorEnum() {
        return WhereOp.fromSymbol(this.operator);
    }
}
```

##### 检测类
多指标（最多支持5个），包含DetectQueryRequest对象

```java
package com.databuff.common.tsdb.dto.detect;

@Data
@JsonDeserialize(using = MultiDetectQueryRequestDeserializer.class) // 启用反序列化器
@JsonSerialize(using = MultiDetectQueryRequestSerializer.class)    // 启用序列化器
@JsonInclude(JsonInclude.Include.NON_NULL)
public class MultiDetectQueryRequest {

    public String getJsonField() {
        return JSONObject.toJSONString(this, SerializerFeature.DisableCircularReferenceDetect);
    }

    @JsonProperty("1")
    @JSONField(name = "1")
    private DetectQueryRequest A;

    @JsonProperty("2")
    @JSONField(name = "2")
    private DetectQueryRequest B;

    @JsonProperty("3")
    @JSONField(name = "3")
    private DetectQueryRequest C;

    @JsonProperty("4")
    @JSONField(name = "4")
    private DetectQueryRequest D;

    @JsonProperty("5")
    @JSONField(name = "5")
    private DetectQueryRequest E;

    private LogicalOperator critical;
    private LogicalOperator warning;
    private LogicalOperator noData;

    private Boolean hasAnnotate;

    public void setHasAnnotate(Boolean hasAnnotate) {
        this.hasAnnotate = hasAnnotate;
        if (hasAnnotate == null) {
            return;
        }
        // 同步起始值到所有非空查询请求
        for (DetectQueryRequest nonNullQuery : this.buildNonNullQueries()) {
            nonNullQuery.setHasAnnotate(hasAnnotate);
        }
    }

    private String apiKey;

    public void setApiKey(String apiKey) {
        this.apiKey = apiKey;
        if (apiKey == null) {
            return;
        }
        // 同步起始值到所有非空查询请求
        for (DetectQueryRequest nonNullQuery : this.buildNonNullQueries()) {
            nonNullQuery.setApiKey(apiKey);
        }
    }

    @ApiModelProperty(value = "起始时间戳，单位为毫秒", example = "1609459200000")
    private Long start;

    /**
     * 设置起始值，并同步到所有非空的查询请求中。
     *
     * @param start 要设置的起始值
     */
    public void setStart(Long start) {
        this.start = start;
        if (start == null) {
            return;
        }
        // 同步起始值到所有非空查询请求
        for (DetectQueryRequest nonNullQuery : this.buildNonNullQueries()) {
            nonNullQuery.setStart(start);
        }
    }

    @ApiModelProperty(value = "结束时间戳，单位为毫秒", example = "1609462800000")
    private Long end;

    /**
     * 设置结束时间并同步更新所有非空查询请求的结束时间。
     *
     * @param end 新的结束时间值（可能为null）
     */
    public void setEnd(Long end) {
        this.end = end;
        if (end == null) {
            return;
        }
        // 同步更新所有非空查询请求的结束时间
        for (DetectQueryRequest nonNullQuery : this.buildNonNullQueries()) {
            nonNullQuery.setEnd(end);
        }
    }

    @ApiModelProperty(value = "查询数据的时间间隔，单位为秒", example = "60")
    private Integer interval;

    /**
     * 设置查询时间间隔（单位为秒），并同步到所有非空的查询请求中。
     *
     * @param interval 新的时间间隔值（单位为秒，可能为null）
     */
    public void setInterval(Integer interval) {
        this.interval = interval;
        if (interval == null) {
            return;
        }
        // 同步时间间隔到所有非空查询请求（转换为Long类型）
        for (DetectQueryRequest nonNullQuery : this.buildNonNullQueries()) {
            nonNullQuery.setInterval(interval);
        }
    }

    /**
     * 获取所有非空的 A-E 查询对象列表。
     */
    @JsonIgnore
    @JSONField(serialize = false, deserialize = false) // Fastjson 的注解
    public Collection<DetectQueryRequest> buildNonNullQueries() {
        return Arrays.stream(new DetectQueryRequest[]{A, B, C, D, E})
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }

    /**
     * 获取包含非空DetectQueryRequest对象的映射表。
     * 该方法遍历A到E五个DetectQueryRequest对象，将非空的对象按指定键("1"至"5")存入Map中。
     * 每个键对应如下：
     * "1" -> A
     * "2" -> B
     * "3" -> C
     * "4" -> D
     * "5" -> E
     *
     * @return 包含非空请求对象的Map，键为字符串编号，值为对应的DetectQueryRequest实例
     */
    @JsonIgnore
    @JSONField(serialize = false, deserialize = false) // Fastjson 的注解
    public Map<String, DetectQueryRequest> buildNonNullQueriesMap() {
        Map<String, DetectQueryRequest> map = new HashMap<>();
        if (A != null) map.put("1", A);
        if (B != null) map.put("2", B);
        if (C != null) map.put("3", C);
        if (D != null) map.put("4", D);
        if (E != null) map.put("5", E);
        return map;
    }


    /**
     * 收集所有非空查询请求中的查询条件并返回结果集合。
     *
     * @return 包含所有查询条件字符串的集合
     */
    public Collection<String> findBy() {
        Collection<String> result = new HashSet<>();

        // 遍历所有非空的DetectQueryRequest对象
        for (DetectQueryRequest detectQueryRequest : this.buildNonNullQueries()) {
            if (detectQueryRequest == null) {
                continue;
            }

            // 遍历MultiQueryDTO中的非空QueryRequest对象
            for (QueryRequest queryRequest : detectQueryRequest.getNonNullQueries()) {
                if (queryRequest == null) {
                    continue;
                }

                // 将当前QueryRequest的查询条件添加到结果集合
                result.addAll(queryRequest.getBy());
            }
        }
        return result;
    }

    public Collection<String> findWay() {
        return buildNonNullQueries().stream()
                .map(DetectQueryRequest::getWay)
                .filter(Objects::nonNull)
                .collect(Collectors.toSet());
    }


    /**
     * 收集所有检测查询请求中定义的指标名称。
     * <p>
     * 该方法遍历所有非空的检测查询请求及其嵌套的查询请求，提取每个查询请求中的指标名称，
     * 并返回所有唯一指标名称的集合。
     *
     * @return 包含所有唯一指标名称的集合
     */
    public Collection<String> findMetrics() {
        Collection<String> result = new HashSet<>();
        // 遍历所有非空的检测查询请求
        for (DetectQueryRequest detectQueryRequest : this.buildNonNullQueries()) {
            if (detectQueryRequest == null) {
                continue;
            }
            // 新增对getNonNullQueries()返回值的空指针检查
            Collection<QueryRequest> queries = detectQueryRequest.getNonNullQueries();
            if (queries == null) {
                continue;
            }
            for (QueryRequest queryRequest : queries) {
                if (queryRequest == null) {
                    continue;
                }
                result.add(queryRequest.getMetric());
            }
        }
        return result;
    }

    /**
     * 设置所有查询请求的时间偏移值。
     * @param timeOffset 需要设置的时间偏移量（单位由具体实现定义）
     * @return 无返回值
     */
    public void setTimeOffset(int timeOffset) {
        // 遍历所有非空的DetectQueryRequest对象
        for (DetectQueryRequest detectQueryRequest : this.buildNonNullQueries()) {
            if (detectQueryRequest == null) {
                continue;
            }
            // 遍历当前DetectQueryRequest中的每个非空QueryRequest并设置时间偏移
            for (QueryRequest queryRequest : detectQueryRequest.getNonNullQueries()) {
                if (queryRequest == null) {
                    continue;
                }
                queryRequest.setTimeOffset(timeOffset);
            }
        }
    }

    /**
     * 通用方法，用于在所有非空的DetectQueryRequest对象上设置指定属性值。
     * 该方法使用反射机制，根据属性名查找并调用对应的setter方法。
     *
     * @param propertyName 要设置的属性名称
     * @param value 要设置的属性值
     */
    @JsonIgnore
    @JSONField(serialize = false, deserialize = false)
    public void setPropertyOnAllDetectQueries(String propertyName, Object value) {
        if (propertyName == null || value == null) {
            return;
        }

        // 构造setter方法名
        String setterMethodName = "set" + propertyName.substring(0, 1).toUpperCase() + propertyName.substring(1);

        try {
            // 获取DetectQueryRequest类中对应属性类型的setter方法
            Method setterMethod = null;
            for (Method method : DetectQueryRequest.class.getMethods()) {
                if (method.getName().equals(setterMethodName) && method.getParameterCount() == 1) {
                    setterMethod = method;
                    break;
                }
            }

            if (setterMethod != null) {
                // 在所有非空查询请求上调用setter方法
                for (DetectQueryRequest detectQuery : this.buildNonNullQueries()) {
                    if (detectQuery != null) {
                        setterMethod.invoke(detectQuery, value);
                    }
                }
            } else {
                // 如果没有找到对应的setter方法，抛出异常
                throw new RuntimeException("Property '" + propertyName + "' not found in DetectQueryRequest class");
            }
        } catch (Exception e) {
            // 记录异常信息或者抛出自定义异常
            throw new RuntimeException("Failed to set property " + propertyName + " on detect queries", e);
        }
    }

    /**
     * 通用方法，用于在所有非空的DetectQueryRequest对象的所有非空QueryRequest对象上设置指定属性值。
     * 该方法使用反射机制，根据属性名查找并调用对应的setter方法。
     *
     * @param propertyName 要设置的属性名称
     * @param value 要设置的属性值
     */
    @JsonIgnore
    @JSONField(serialize = false, deserialize = false)
    public void setPropertyOnAllQueries(String propertyName, Object value) {
        if (propertyName == null || value == null) {
            return;
        }

        // 构造setter方法名
        String setterMethodName = "set" + propertyName.substring(0, 1).toUpperCase() + propertyName.substring(1);

        try {
            // 获取QueryRequest类中对应属性类型的setter方法
            Method setterMethod = null;
            for (Method method : QueryRequest.class.getMethods()) {
                if (method.getName().equals(setterMethodName) && method.getParameterCount() == 1) {
                    setterMethod = method;
                    break;
                }
            }

            if (setterMethod != null) {
                // 遍历所有非空的DetectQueryRequest对象
                for (DetectQueryRequest detectQuery : this.buildNonNullQueries()) {
                    if (detectQuery == null) {
                        continue;
                    }
                    // 遍历当前DetectQueryRequest中的每个非空QueryRequest并设置属性
                    for (QueryRequest query : detectQuery.getNonNullQueries()) {
                        if (query != null) {
                            setterMethod.invoke(query, value);
                        }
                    }
                }
            } else {
                // 如果没有找到对应的setter方法，抛出异常
                throw new RuntimeException("Property '" + propertyName + "' not found in QueryRequest class");
            }
        } catch (Exception e) {
            // 记录异常信息或者抛出自定义异常
            throw new RuntimeException("Failed to set property " + propertyName + " on queries", e);
        }
    }

    /**
     * 设置所有非空查询请求的时间聚合器。
     *
     * @param timeAggregator 要设置的时间聚合器值
     */
    public void setTimeAggregator(String timeAggregator) {
        if (timeAggregator == null) {
            return;
        }
        // 同步时间聚合器到所有非空查询请求
        for (DetectQueryRequest nonNullQuery : this.buildNonNullQueries()) {
            nonNullQuery.setTimeAggregator(timeAggregator);
        }
    }

    /**
     * 设置所有非空查询请求的比较运算符。
     *
     * @param comparison 要设置的比较运算符值
     */
    public void setComparison(String comparison) {
        if (comparison == null) {
            return;
        }
        // 同步比较运算符到所有非空查询请求
        for (DetectQueryRequest nonNullQuery : this.buildNonNullQueries()) {
            nonNullQuery.setComparison(comparison);
        }
    }

    /**
     * 设置所有非空查询请求的单位。
     *
     * @param unit 要设置的单位值
     */
    public void setUnit(String unit) {
        if (unit == null) {
            return;
        }
        // 同步单位到所有非空查询请求
        for (DetectQueryRequest nonNullQuery : this.buildNonNullQueries()) {
            nonNullQuery.setUnit(unit);
        }
    }

    /**
     * 设置所有非空查询请求的显示单位。
     *
     * @param viewUnit 要设置的显示单位值
     */
    public void setViewUnit(String viewUnit) {
        if (viewUnit == null) {
            return;
        }
        // 同步显示单位到所有非空查询请求
        for (DetectQueryRequest nonNullQuery : this.buildNonNullQueries()) {
            nonNullQuery.setViewUnit(viewUnit);
        }
    }

    /**
     * 设置所有非空查询请求的连续检测标志。
     *
     * @param continuous 要设置的连续检测标志值
     */
    public void setContinuous(Boolean continuous) {
        if (continuous == null) {
            return;
        }
        // 同步连续检测标志到所有非空查询请求
        for (DetectQueryRequest nonNullQuery : this.buildNonNullQueries()) {
            nonNullQuery.setContinuous(continuous);
        }
    }

    /**
     * 设置所有非空查询请求的完整窗口要求标志。
     *
     * @param requireFullWindow 要设置的完整窗口要求标志值
     */
    public void setRequireFullWindow(Boolean requireFullWindow) {
        if (requireFullWindow == null) {
            return;
        }
        // 同步完整窗口要求标志到所有非空查询请求
        for (DetectQueryRequest nonNullQuery : this.buildNonNullQueries()) {
            nonNullQuery.setRequireFullWindow(requireFullWindow);
        }
    }

    /**
     * 收集所有非空查询请求中的时间聚合器并返回结果集合。
     *
     * @return 包含所有时间聚合器的集合
     */
    public Collection<String> findTimeAggregators() {
        return buildNonNullQueries().stream()
                .map(DetectQueryRequest::getTimeAggregator)
                .filter(Objects::nonNull)
                .collect(Collectors.toSet());
    }

    /**
     * 收集所有非空查询请求中的比较运算符并返回结果集合。
     *
     * @return 包含所有比较运算符的集合
     */
    public Collection<String> findComparisons() {
        return buildNonNullQueries().stream()
                .map(DetectQueryRequest::getComparison)
                .filter(Objects::nonNull)
                .collect(Collectors.toSet());
    }

    /**
     * 收集所有非空查询请求中的单位并返回结果集合。
     *
     * @return 包含所有单位的集合
     */
    public Collection<String> findUnits() {
        return buildNonNullQueries().stream()
                .map(DetectQueryRequest::getUnit)
                .filter(Objects::nonNull)
                .collect(Collectors.toSet());
    }

    /**
     * 收集所有非空查询请求中的显示单位并返回结果集合。
     *
     * @return 包含所有显示单位的集合
     */
    public Collection<String> findViewUnits() {
        return buildNonNullQueries().stream()
                .map(DetectQueryRequest::getViewUnit)
                .filter(Objects::nonNull)
                .collect(Collectors.toSet());
    }

    /**
     * 收集所有非空查询请求中的连续检测标志并返回结果集合。
     *
     * @return 包含所有连续检测标志的集合
     */
    public Collection<Boolean> findContinuous() {
        return buildNonNullQueries().stream()
                .map(DetectQueryRequest::getContinuous)
                .filter(Objects::nonNull)
                .collect(Collectors.toSet());
    }

    /**
     * 收集所有非空查询请求中的完整窗口要求标志并返回结果集合。
     *
     * @return 包含所有完整窗口要求标志的集合
     */
    public Collection<Boolean> findRequireFullWindow() {
        return buildNonNullQueries().stream()
                .map(DetectQueryRequest::getRequireFullWindow)
                .filter(Objects::nonNull)
                .collect(Collectors.toSet());
    }
}
```

单指标，包含表达式，支持A到Z的复合表达式，包含QueryRequest对象

```java
package com.databuff.common.tsdb.dto.detect;

import com.alibaba.fastjson.annotation.JSONField;
import com.databuff.common.tsdb.dto.explore.QueryRequest;
import com.databuff.common.tsdb.dto.preview.ThresholdsDTO;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 查询请求数据传输对象，包含查询条件、时间范围及间隔信息。
 */
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class DetectQueryRequest {

    @ApiModelProperty(value = "是否拥有所有权限", example = "true")
    private Boolean allPermission;

    /**
     * 设置是否拥有所有权限，并同步到所有非空的查询请求中。
     *
     * @param allPermission 新的权限状态（可能为null）
     */
    public void setAllPermission(Boolean allPermission) {
        this.allPermission = allPermission;
        // 同步权限状态到所有非空查询请求
        for (QueryRequest query : this.getNonNullQueries()) {
            query.setAllPermission(allPermission);
        }
    }

    @ApiModelProperty(value = "是否开启领域管理状态", example = "false")
    private Boolean domainManagerStatusOpen;

    /**
     * 设置是否开启领域管理状态，并同步到所有非空的查询请求中。
     *
     * @param domainManagerStatusOpen 新的领域管理状态（可能为null）
     */
    public void setDomainManagerStatusOpen(Boolean domainManagerStatusOpen) {
        this.domainManagerStatusOpen = domainManagerStatusOpen;
        // 同步领域管理状态到所有非空查询请求
        for (QueryRequest query : this.getNonNullQueries()) {
            query.setDomainManagerStatusOpen(domainManagerStatusOpen);
        }
    }

    @ApiModelProperty(value = "组ID集合，用于过滤查询条件", example = "[\"group1\", \"group2\"]")
    private Collection<String> gids;

    /**
     * 设置组ID集合，并同步到所有非空的查询请求中。
     *
     * @param gids 新的组ID集合（可能为null）
     */
    public void setGids(Collection<String> gids) {
        this.gids = gids;
        // 同步组ID集合到所有非空查询请求
        for (QueryRequest query : this.getNonNullQueries()) {
            query.setGids(gids);
        }
    }

    @ApiModelProperty(value = "是否包含注释信息，默认为false", example = "true")
    private boolean hasAnnotate;

    /**
     * 设置是否包含注释信息，并同步到所有非空的查询请求中。
     *
     * @param hasAnnotate 新的注释状态
     */
    public void setHasAnnotate(boolean hasAnnotate) {
        this.hasAnnotate = hasAnnotate;
        // 同步注释状态到所有非空查询请求
        for (QueryRequest query : this.getNonNullQueries()) {
            query.setHasAnnotate(hasAnnotate);
        }
    }

    @ApiModelProperty(value = "API密钥，用于身份认证", example = "your_api_key_123")
    private String apiKey;

    /**
     * 设置API密钥，并同步到所有非空的查询请求中。
     *
     * @param apiKey 新的API密钥（可能为null）
     */
    public void setApiKey(String apiKey) {
        this.apiKey = apiKey;
        // 同步API密钥到所有非空查询请求
        for (QueryRequest query : this.getNonNullQueries()) {
            query.setApiKey(apiKey);
        }
    }

    @ApiModelProperty(value = "起始时间戳，单位为毫秒", example = "1609459200000")
    private Long start;

    /**
     * 设置起始值，并同步到所有非空的查询请求中。
     *
     * @param start 要设置的起始值
     */
    public void setStart(Long start) {
        this.start = start;
        if (start == null) {
            return;
        }
        // 同步起始值到所有非空查询请求
        for (QueryRequest nonNullQuery : this.getNonNullQueries()) {
            nonNullQuery.setStart(start);
        }
    }

    @ApiModelProperty(value = "结束时间戳，单位为毫秒", example = "1609462800000")
    private Long end;

    /**
     * 设置结束时间并同步更新所有非空查询请求的结束时间。
     * @param end 新的结束时间值（可能为null）
     */
    public void setEnd(Long end) {
        this.end = end;
        if (end == null) {
            return;
        }
        // 同步更新所有非空查询请求的结束时间
        for (QueryRequest nonNullQuery : this.getNonNullQueries()) {
            nonNullQuery.setEnd(end);
        }
    }

    @ApiModelProperty(value = "查询数据的时间间隔，单位为秒", example = "60")
    private Integer interval;

    /**
     * 设置查询时间间隔（单位为秒），并同步到所有非空的查询请求中。
     *
     * @param interval 新的时间间隔值（单位为秒，可能为null）
     */
    public void setInterval(Integer interval) {
        this.interval = interval;
        if (interval == null) {
            return;
        }
        // 同步时间间隔到所有非空查询请求（转换为Long类型）
        for (QueryRequest query : this.getNonNullQueries()) {
            query.setInterval(interval);
        }
    }

    @ApiModelProperty(value = "最近时间区间，单位为秒", example = "3600")
    private Integer period;
    /**
     * 设置最近时间区间（单位为秒），并同步到所有非空的查询请求中。
     *
     * @param period 新的时间区间值（可能为null）
     */
    public void setPeriod(Integer period) {
        this.period = period;
        // 同步最近时间区间到所有非空查询请求
        for (QueryRequest query : this.getNonNullQueries()) {
            query.setPeriod(period);
        }
    }

    @ApiModelProperty(value = "检测方式", example = "threshold/baseline...")
    private String way;


    @ApiModelProperty(value = "动态基线", example = "1")
    @JsonProperty("baselineScope")
    @JSONField(name = "baselineScope")
    private double baselineScope;

    @ApiModelProperty(value = "任务触发时间(毫秒)", example = "60")
    private long triggerTime;

    @ApiModelProperty(value = "波动检测专用，同比于n分钟前", example = "60")
    @JsonProperty("comparePeriod")
    @JSONField(name = "comparePeriod")
    private Integer comparePeriod;

    @ApiModelProperty(value = "波动检测专用，同比于n分钟前，数值增加量", example = "数值增加量/数值减少量")
    private String fluctuate;

    @ApiModelProperty(value = "无数据时间阈值（分钟）", example = "12")
    @JsonProperty("no_data_timeframe")
    @JSONField(name = "no_data_timeframe")
    private Integer noDataTimeframe;

    /**
     * 获取无数据时间范围的值。如果内部存储的noDataTimeframe为null，则返回默认值0，否则返回其实际存储值。
     *
     * @return 无数据时间范围值（null时返回0）
     */
    public Integer getNoDataTimeframe() {
        if (noDataTimeframe == null) {
            return 0;
        }
        return noDataTimeframe;
    }


    @ApiModelProperty(value = "比较符（>、<、>=、<=）", example = ">")
    private String comparison;

    @ApiModelProperty(value = "时间聚合方式（avg/sum/max/min）", example = "avg")
    @JsonProperty("time_aggregator")
    @JSONField(name = "time_aggregator")
    private String timeAggregator;

    @ApiModelProperty(value = "延迟评估时间（秒）", example = "0")
    @JsonProperty("evaluation_delay")
    @JSONField(name = "evaluation_delay")
    private Integer evaluationDelay;

    /**
     * 获取评估延迟时间，若未设置则返回默认值0。
     *
     * @return 评估延迟时间（若未设置则返回0）
     */
    public Integer getEvaluationDelay() {
        // 如果评估延迟未设置，则返回默认值0
        if (evaluationDelay == null) {
            return 0;
        }
        return evaluationDelay;
    }


    @ApiModelProperty(value = "连续周期数", example = "3")
    @JsonProperty("continuous_n")
    @JSONField(name = "continuous_n")
    private Integer continuousN;

    @ApiModelProperty(value = "底层单位（如percent表示百分比）", example = "percent")
    private String unit;

    @ApiModelProperty(value = "显示单位", example = "%")
    @JsonProperty("view_unit")
    @JSONField(name = "view_unit")
    private String viewUnit;

    @JsonProperty("unit")
    @JSONField(name = "unit")
    public void setUnit(String unit) {
        this.unit = unit;
        if (thresholds == null) {
            return;
        }
        thresholds.setUnit(unit);
    }

    @ApiModelProperty(value = "阈值配置", required = true)
    private ThresholdsDTO thresholds;

    @ApiModelProperty(value = "突变检测，指标数据低于30%时，生成跳过事件", example = "true")
    @JsonProperty("less_data_timeframe")
    @JSONField(name = "less_data_timeframe")
    private Boolean lessDataTimeframe;

    @ApiModelProperty(value = "单位换算比例（前端用）", example = "0.01")
    @JsonProperty("_scale")
    @JSONField(name = "_scale")
    private Double scale;

    @ApiModelProperty(value = "是否启用连续检测", example = "true")
    private Boolean continuous;

    @ApiModelProperty(value = "是否需要完整窗口数据", example = "false")
    @JsonProperty("require_full_window")
    @JSONField(name = "require_full_window")
    private Boolean requireFullWindow;

    @ApiModelProperty(value = "查询表达式对象A", example = "{\"metric\":\"cpu_usage\"}")
    @JsonProperty("A")
    @JSONField(name = "A")
    private QueryRequest A;

    @ApiModelProperty(value = "查询表达式对象B", example = "{\"metric\":\"memory_usage\"}")
    @JsonProperty("B")
    @JSONField(name = "B")
    private QueryRequest B;

    @ApiModelProperty(value = "查询表达式对象C", example = "{\"metric\":\"disk_io\"}")
    @JsonProperty("C")
    @JSONField(name = "C")
    private QueryRequest C;

    @ApiModelProperty(value = "查询表达式对象D", example = "{\"metric\":\"network_traffic\"}")
    @JsonProperty("D")
    @JSONField(name = "D")
    private QueryRequest D;

    @ApiModelProperty(value = "查询表达式对象E", example = "{\"metric\":\"system_load\"}")
    @JsonProperty("E")
    @JSONField(name = "E")
    private QueryRequest E;

    @ApiModelProperty(value = "查询表达式对象F", example = "{\"metric\":\"custom_metric_f\"}")
    @JsonProperty("F")
    @JSONField(name = "F")
    private QueryRequest F;

    @ApiModelProperty(value = "查询表达式对象G", example = "{\"metric\":\"custom_metric_g\"}")
    @JsonProperty("G")
    @JSONField(name = "G")
    private QueryRequest G;

    @ApiModelProperty(value = "查询表达式对象H", example = "{\"metric\":\"custom_metric_h\"}")
    @JsonProperty("H")
    @JSONField(name = "H")
    private QueryRequest H;

    @ApiModelProperty(value = "查询表达式对象I", example = "{\"metric\":\"custom_metric_i\"}")
    @JsonProperty("I")
    @JSONField(name = "I")
    private QueryRequest I;

    @ApiModelProperty(value = "查询表达式对象J", example = "{\"metric\":\"custom_metric_j\"}")
    @JsonProperty("J")
    @JSONField(name = "J")
    private QueryRequest J;

    @ApiModelProperty(value = "查询表达式对象K", example = "{\"metric\":\"custom_metric_k\"}")
    @JsonProperty("K")
    @JSONField(name = "K")
    private QueryRequest K;

    @ApiModelProperty(value = "查询表达式对象L", example = "{\"metric\":\"custom_metric_l\"}")
    @JsonProperty("L")
    @JSONField(name = "L")
    private QueryRequest L;

    @ApiModelProperty(value = "查询表达式对象M", example = "{\"metric\":\"custom_metric_m\"}")
    @JsonProperty("M")
    @JSONField(name = "M")
    private QueryRequest M;

    @ApiModelProperty(value = "查询表达式对象N", example = "{\"metric\":\"custom_metric_n\"}")
    @JsonProperty("N")
    @JSONField(name = "N")
    private QueryRequest N;

    @ApiModelProperty(value = "查询表达式对象O", example = "{\"metric\":\"custom_metric_o\"}")
    @JsonProperty("O")
    @JSONField(name = "O")
    private QueryRequest O;

    @ApiModelProperty(value = "查询表达式对象P", example = "{\"metric\":\"custom_metric_p\"}")
    @JsonProperty("P")
    @JSONField(name = "P")
    private QueryRequest P;

    @ApiModelProperty(value = "查询表达式对象Q", example = "{\"metric\":\"custom_metric_q\"}")
    @JsonProperty("Q")
    @JSONField(name = "Q")
    private QueryRequest Q;

    @ApiModelProperty(value = "查询表达式对象R", example = "{\"metric\":\"custom_metric_r\"}")
    @JsonProperty("R")
    @JSONField(name = "R")
    private QueryRequest R;

    @ApiModelProperty(value = "查询表达式对象S", example = "{\"metric\":\"custom_metric_s\"}")
    @JsonProperty("S")
    @JSONField(name = "S")
    private QueryRequest S;

    @ApiModelProperty(value = "查询表达式对象T", example = "{\"metric\":\"custom_metric_t\"}")
    @JsonProperty("T")
    @JSONField(name = "T")
    private QueryRequest T;

    @ApiModelProperty(value = "查询表达式对象U", example = "{\"metric\":\"custom_metric_u\"}")
    @JsonProperty("U")
    @JSONField(name = "U")
    private QueryRequest U;

    @ApiModelProperty(value = "查询表达式对象V", example = "{\"metric\":\"custom_metric_v\"}")
    @JsonProperty("V")
    @JSONField(name = "V")
    private QueryRequest V;

    @ApiModelProperty(value = "查询表达式对象W", example = "{\"metric\":\"custom_metric_w\"}")
    @JsonProperty("W")
    @JSONField(name = "W")
    private QueryRequest W;

    @ApiModelProperty(value = "查询表达式对象X", example = "{\"metric\":\"custom_metric_x\"}")
    @JsonProperty("X")
    @JSONField(name = "X")
    private QueryRequest X;

    @ApiModelProperty(value = "查询表达式对象Y", example = "{\"metric\":\"custom_metric_y\"}")
    @JsonProperty("Y")
    @JSONField(name = "Y")
    private QueryRequest Y;

    @ApiModelProperty(value = "查询表达式对象Z", example = "{\"metric\":\"custom_metric_z\"}")
    @JsonProperty("Z")
    @JSONField(name = "Z")
    private QueryRequest Z;

    @ApiModelProperty(value = "查询表达式字符串（如PromQL语法）", example = "a + b + c")
    private String expr;

    @ApiModelProperty(value = "表达式名称", example = "自定义指标名")
    private String exprName;

    /**
     * 获取所有非空的 A-Z 查询对象列表。
     */
    @JsonIgnore
    @JSONField(serialize = false, deserialize = false)
    public Collection<QueryRequest> getNonNullQueries() {
        Collection<QueryRequest> nonNullList = new ArrayList<>();
        if (A != null) nonNullList.add(A);
        if (B != null) nonNullList.add(B);
        if (C != null) nonNullList.add(C);
        if (D != null) nonNullList.add(D);
        if (E != null) nonNullList.add(E);
        if (F != null) nonNullList.add(F);
        if (G != null) nonNullList.add(G);
        if (H != null) nonNullList.add(H);
        if (I != null) nonNullList.add(I);
        if (J != null) nonNullList.add(J);
        if (K != null) nonNullList.add(K);
        if (L != null) nonNullList.add(L);
        if (M != null) nonNullList.add(M);
        if (N != null) nonNullList.add(N);
        if (O != null) nonNullList.add(O);
        if (P != null) nonNullList.add(P);
        if (Q != null) nonNullList.add(Q);
        if (R != null) nonNullList.add(R);
        if (S != null) nonNullList.add(S);
        if (T != null) nonNullList.add(T);
        if (U != null) nonNullList.add(U);
        if (V != null) nonNullList.add(V);
        if (W != null) nonNullList.add(W);
        if (X != null) nonNullList.add(X);
        if (Y != null) nonNullList.add(Y);
        if (Z != null) nonNullList.add(Z);
        return nonNullList;
    }

    /**
     * 获取包含非空DetectQueryRequest对象的映射表。
     * 该方法遍历A到Z的DetectQueryRequest对象，将非空的对象按字母键存入Map中。
     * 每个键对应如下：
     * "A" -> A
     * "B" -> B
     * ...
     * "Z" -> Z
     *
     * @return 包含非空请求对象的Map，键为字母字符串，值为对应的QueryRequest实例
     */
    @JsonIgnore
    @JSONField(serialize = false, deserialize = false)
    public Map<String, QueryRequest> getNonNullQueriesMap() {
        Map<String, QueryRequest> map = new HashMap<>();
        if (A != null) map.put("A", A);
        if (B != null) map.put("B", B);
        if (C != null) map.put("C", C);
        if (D != null) map.put("D", D);
        if (E != null) map.put("E", E);
        if (F != null) map.put("F", F);
        if (G != null) map.put("G", G);
        if (H != null) map.put("H", H);
        if (I != null) map.put("I", I);
        if (J != null) map.put("J", J);
        if (K != null) map.put("K", K);
        if (L != null) map.put("L", L);
        if (M != null) map.put("M", M);
        if (N != null) map.put("N", N);
        if (O != null) map.put("O", O);
        if (P != null) map.put("P", P);
        if (Q != null) map.put("Q", Q);
        if (R != null) map.put("R", R);
        if (S != null) map.put("S", S);
        if (T != null) map.put("T", T);
        if (U != null) map.put("U", U);
        if (V != null) map.put("V", V);
        if (W != null) map.put("W", W);
        if (X != null) map.put("X", X);
        if (Y != null) map.put("Y", Y);
        if (Z != null) map.put("Z", Z);
        return map;
    }

    /**
     * 收集所有非空查询请求中的查询条件并返回结果集合。
     *
     * @return 包含所有查询条件字符串的集合
     */
    public Collection<String> findBy() {
        Collection<String> result = new HashSet<>();
        for (QueryRequest queryRequest : this.getNonNullQueries()) {
            result.addAll(queryRequest.getBy());
        }
        return result;
    }

    /**
     * 收集所有检测查询请求中定义的指标名称。
     * <p>
     * 该方法遍历所有非空的检测查询请求及其嵌套的查询请求，提取每个查询请求中的指标名称，
     * 并返回所有唯一指标名称的集合。
     *
     * @return 包含所有唯一指标名称的集合
     */
    public Collection<String> findMetrics() {
        Collection<QueryRequest> queries = this.getNonNullQueries();
        if (queries == null) {
            return new HashSet<>();
        }
        return queries.stream()
                .filter(Objects::nonNull)
                .map(QueryRequest::getMetric)
                .filter(Objects::nonNull)
                .collect(Collectors.toCollection(HashSet::new));
    }

    public String findFirstMetric() {
        Collection<QueryRequest> queries = this.getNonNullQueries();
        if (queries == null) {
            return null;
        }
        return queries.stream()
                .filter(Objects::nonNull)
                .map(QueryRequest::getMetric)
                .filter(Objects::nonNull)
                .findFirst()
                .orElse(null);
    }

    /**
     * 查询表达式字符串，用于高级查询条件（如PromQL语法）。
     * 比如：a+b+c+d+e
     */
    public String getExpr() {
        return this.expr;
    }

    public void setExpr(String expr) {
        this.expr = expr;
    }
}

```

##### 预览类
```java
package com.databuff.common.tsdb.dto.preview;

import com.databuff.common.tsdb.dto.detect.DetectQueryRequest;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel("指标数据预览请求参数")
@JsonInclude(JsonInclude.Include.NON_NULL)
public class PreviewQueryRequest {

    @ApiModelProperty(value = "规则参数")
    private RuleDTO rule;

    @ApiModelProperty(value = "查询请求参数对象，包含查询条件和权限信息", example = "包含完整的查询条件配置")
    private DetectQueryRequest query;

    public PreviewQueryRequest() {
        ensureQueryInitialized();
    }

    private synchronized void ensureQueryInitialized() {
        if (query == null) {
            query = new DetectQueryRequest();
        }
    }

    /**
     * 设置是否包含注解。
     * 如果{@link #query}为null，会自动创建一个新的{@link DetectQueryRequest}实例。
     */
    public void setHasAnnotate(boolean annotate) {
        ensureQueryInitialized();
        query.setHasAnnotate(annotate);
    }

    /**
     * 设置API密钥。
     * 如果{@link #query}为null，会自动创建一个新的{@link DetectQueryRequest}实例。
     */
    public void setApiKey(String apiKey) {
        ensureQueryInitialized();
        query.setApiKey(apiKey);
    }

    public DetectQueryRequest getQuery() {
        return this.query;
    }

    public Integer getPeriod() {
        ensureQueryInitialized();
        return this.query.getPeriod();
    }

    public Long getStart() {
        ensureQueryInitialized();
        return this.query.getStart();
    }

    public Long getEnd() {
        ensureQueryInitialized();
        return this.query.getEnd();
    }

    public Integer getInterval() {
        ensureQueryInitialized();
        return this.query.getInterval();
    }

    public void setQuery(DetectQueryRequest query) {
        this.query = query;
    }

    public void setPeriod(Integer period) {
        ensureQueryInitialized();
        this.query.setPeriod(period);
    }

    public void setStart(Long start) {
        ensureQueryInitialized();
        this.query.setStart(start);
    }

    public void setEnd(Long end) {
        ensureQueryInitialized();
        this.query.setEnd(end);
    }

    public void setInterval(Integer interval) {
        ensureQueryInitialized();
        this.query.setInterval(interval);
    }
}
```

```java
package com.databuff.common.tsdb.dto.preview;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@ApiModel("阈值配置DTO")
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class ThresholdsDTO {
    @ApiModelProperty(value = "严重告警阈值", example = "5")
    private Number critical;

    @ApiModelProperty(value = "警告告警阈值", example = "1")
    private Number warning;

    @ApiModelProperty(value = "严重阈值状态", example = "98")
    @JsonProperty("critical_state")
    @JSONField(name = "critical_state")
    private Number criticalState;

    @ApiModelProperty(value = "严重阈值状态", example = "98")
    @JsonProperty("warning_state")
    @JSONField(name = "warning_state")
    private Number warningState;

    private String comparison;
    private String unit;
    private String checkName;
    private String mName;
    private String msg;
    private String trgTrd;
    private String trgValue;
    private Number value;
}
```

```java
package com.databuff.common.tsdb.dto.preview;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import lombok.Data;

@ApiModel("排序参数")
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class OrderDTO {
    private String code;
    private String func;
    private Integer limit;
}
```

```java
package com.databuff.common.tsdb.dto.preview;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@ApiModel("规则配置DTO")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class RuleDTO {
    @ApiModelProperty(value = "检测方式", example = "changePoint")
    private String way;

    @ApiModelProperty(value = "动态基线", example = "1")
    private double baselineScope;

    @ApiModelProperty(value = "比较符（>、<、>=、<=）", example = ">")
    private String comparison;

    @ApiModelProperty(value = "阈值配置", required = true)
    private ThresholdsDTO thresholds;
}
```



#### 自定义的序列化/反序列化
```java
package com.databuff.common.tsdb.dto.detect;

import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.databind.JsonSerializer;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializerProvider;
import com.fasterxml.jackson.databind.node.ObjectNode;

import java.io.IOException;

public class MultiDetectQueryRequestSerializer extends JsonSerializer<MultiDetectQueryRequest> {

    // 复用单个ObjectMapper实例
    private final ObjectMapper objectMapper = new ObjectMapper();

    @Override
    public void serialize(
            MultiDetectQueryRequest value,
            JsonGenerator gen,
            SerializerProvider serializers)
            throws IOException {
        ObjectNode root = objectMapper.createObjectNode();

        // 序列化 "1" -> A
        serializeField(root, "1", value.getA(), serializers);
        // 序列化 "2" -> B
        serializeField(root, "2", value.getB(), serializers);
        serializeField(root, "3", value.getC(), serializers);
        serializeField(root, "4", value.getD(), serializers);
        serializeField(root, "5", value.getE(), serializers);

        // 序列化其他字段（如 critical、warning）
        root.put("critical", value.getCritical().name());
        root.put("warning", value.getWarning().name());
        root.put("noData", value.getNoData().name());

        // 新增三个数值字段的序列化
        root.put("start", value.getStart());
        root.put("end", value.getEnd());
        root.put("interval", value.getInterval());

        gen.writeTree(root);
    }

    private void serializeField(
            ObjectNode root,
            String key,
            DetectQueryRequest request,
            SerializerProvider serializers) {
        if (request != null) {
            // 移除循环引用的错误逻辑
            ObjectNode nestedNode = objectMapper.valueToTree(request);
            root.set(key, nestedNode); // 直接设置到目标键下
        }
    }
}
```

```java
package com.databuff.common.tsdb.dto.detect;

import com.databuff.common.tsdb.model.LogicalOperator;
import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.core.ObjectCodec;
import com.fasterxml.jackson.databind.DeserializationContext;
import com.fasterxml.jackson.databind.JsonDeserializer;
import com.fasterxml.jackson.databind.JsonNode;
import lombok.extern.slf4j.Slf4j;

import java.io.IOException;
import java.util.function.Consumer;

@Slf4j
public class MultiDetectQueryRequestDeserializer extends JsonDeserializer<MultiDetectQueryRequest> {

    /**
     * 反序列化JSON数据到MultiDetectQueryRequest对象
     *
     * @param p    JSON解析器，用于读取原始JSON数据流
     * @param ctxt 反序列化上下文，提供反序列化相关配置和工具
     * @return 解析后的MultiDetectQueryRequest请求对象
     * @throws IOException 当JSON解析过程中发生I/O错误时抛出
     */
    @Override
    public MultiDetectQueryRequest deserialize(JsonParser p, DeserializationContext ctxt) throws IOException {
        JsonNode node = p.getCodec().readTree(p);
        MultiDetectQueryRequest request = new MultiDetectQueryRequest();

        // 处理数字字段映射
        setDetectQueryRequest(request::setA, node, "1", p);
        setDetectQueryRequest(request::setB, node, "2", p);
        setDetectQueryRequest(request::setC, node, "3", p);
        setDetectQueryRequest(request::setD, node, "4", p);
        setDetectQueryRequest(request::setE, node, "5", p);

        // 统一处理逻辑运算符字段
        handleLogicalOperator(node, "critical", request::setCritical);
        handleLogicalOperator(node, "warning", request::setWarning);
        handleLogicalOperator(node, "noData", request::setNoData);

        request.setStart(getLongField(node, "start"));
        request.setEnd(getLongField(node, "end"));
        request.setInterval(getIntField(node, "interval"));
        return request;
    }

    private Long getLongField(JsonNode node, String fieldName) throws IOException {
        JsonNode fieldNode = node.get(fieldName);
        if (fieldNode == null) {
            return null;
        }
        if (!fieldNode.isNumber()) {
            return null;
        }
        return fieldNode.asLong();
    }

    private Integer getIntField(JsonNode node, String fieldName) throws IOException {
        JsonNode fieldNode = node.get(fieldName);
        if (fieldNode == null) {
            return null;
        }
        if (!fieldNode.isIntegralNumber()) {
            return null;
        }
        return fieldNode.asInt();
    }


    /**
     * 处理JSON节点中的逻辑运算符字段，并将其转换为对应的枚举类型
     *
     * @param node      JSON数据节点，用于提取字段值
     * @param fieldName 需要处理的逻辑运算符字段名称
     * @param setter    消费者函数，用于接收解析成功的LogicalOperator枚举值
     *                  <p>
     *                  函数逻辑：
     *                  从JSON节点中获取指定字段
     *                  验证字段是否为有效文本类型（非空且可解析为字符串）
     *                  将文本值转换为大写进行匹配，支持"AND"/"OR"的任意大小写形式
     *                  通过setter回调将匹配的枚举值传递给调用方
     *                  默认忽略未定义值（可根据需求扩展错误处理）
     */
    private void handleLogicalOperator(JsonNode node, String fieldName, Consumer<LogicalOperator> setter) {
        JsonNode fieldNode = node.get(fieldName);
        if (fieldNode != null && !fieldNode.isNull() && fieldNode.isTextual()) {
            String value = fieldNode.asText().trim();
            if ("AND".equalsIgnoreCase(value)) {
                setter.accept(LogicalOperator.AND);
            } else if ("OR".equalsIgnoreCase(value)) {
                setter.accept(LogicalOperator.OR);
            }
        }
    }

    private void setDetectQueryRequest(
            Consumer<DetectQueryRequest> setter,
            JsonNode node,
            String key,
            JsonParser p) {
        if (node.has(key) && !node.get(key).isNull()) {
            try {
                final JsonNode nested = node.get(key);
                final ObjectCodec codec = p.getCodec();
                if (codec != null) {
                    DetectQueryRequest request = codec.treeToValue(nested, DetectQueryRequest.class);
                    if (request != null) {
                        setter.accept(request);
                    }
                }
            } catch (IOException e) {
                log.error("DetectQueryRequest deserialization failed for key {}: {}", key, e.getMessage());
            }
        }
    }
}
```



#### 自定义的Mybatis <font style="color:#080808;background-color:#ffffff;">TypeHandler</font>
```java
package com.databuff.handler;

import com.alibaba.fastjson.JSON;
import com.databuff.common.tsdb.dto.detect.MultiDetectQueryRequest;
import org.apache.ibatis.type.BaseTypeHandler;
import org.apache.ibatis.type.JdbcType;

import java.sql.CallableStatement;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;

public class MultiDetectQueryRequestHandler extends BaseTypeHandler<MultiDetectQueryRequest> {
    @Override
    public void setNonNullParameter(PreparedStatement ps, int i, MultiDetectQueryRequest parameter, JdbcType jdbcType)
            throws SQLException {
        ps.setString(i, JSON.toJSONString(parameter));
    }

    @Override
    public MultiDetectQueryRequest getNullableResult(ResultSet rs, String columnName)
            throws SQLException {
        String json = rs.getString(columnName);
        return json == null ? null : JSON.parseObject(json, MultiDetectQueryRequest.class);
    }

    @Override
    public MultiDetectQueryRequest getNullableResult(ResultSet rs, int columnIndex) throws SQLException {
        String json = rs.getString(columnIndex);
        return json == null ? null : JSON.parseObject(json, MultiDetectQueryRequest.class);
    }

    @Override
    public MultiDetectQueryRequest getNullableResult(CallableStatement cs, int columnIndex) throws SQLException {
        String json = cs.getString(columnIndex);
        return json == null ? null : JSON.parseObject(json, MultiDetectQueryRequest.class);
    }
}
```

<font style="color:#080808;background-color:#ffffff;"></font>

#### 核心接口
```java
package com.databuff.metric;

import com.alibaba.fastjson.JSONObject;
import com.databuff.common.metric.WrapData;
import com.databuff.common.tsdb.builder.QueryBuilderX;
import com.databuff.common.tsdb.dto.explore.QueryRequest;
import com.databuff.common.tsdb.dto.preview.RuleDTO;
import com.databuff.common.tsdb.model.AggFun;
import com.databuff.common.tsdb.model.TSDBResultSet;
import com.databuff.common.tsdb.model.TSDBSeries;
import com.databuff.common.tsdb.model.Where;
import com.databuff.entity.EventEntity;
import com.databuff.entity.dto.DatabuffMonitorView;
import com.databuff.metric.dto.MetricDTO;
import lombok.SneakyThrows;

import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 指标聚合器
 *
 * <AUTHOR>
 * @version 1.1
 * @since 2.7.0.a
 */
public interface MetricAggregator {

    /**
     * 指标查询聚合器
     *
     * @param expr     聚合表达式: A+(B-C); A*(B-C)
     * @param queries  查询表达式
     * @return 聚合以后的结果
     */
    Map<Map, Map<Object, Double>> aggResult(String expr, List<JSONObject> queries, Long interval);

    Map<Map, Map<Object, Double>> aggResult(String expr, Map<String, QueryRequest> queries);

    List<WrapData> aggResultView(String expr, EventEntity.DetectionType way, RuleDTO rule, Map<String, QueryRequest> queries);

    /**
     * 生成序列结果
     *
     * @param query    查询参数，一个JSON对象，包含指标、分组、过滤、排序等信息 样例：{"A":{"aggs":"mean","metric":"system.cpu.usage","by":["host"],"from":[{"left":"host","right":"databuff193","operator":"="}]}}
     * @param interval 间隔值，一个长整型，表示时间分组的间隔，单位是毫秒
     * @return 一个Map对象，其中键是分组字段的映射，值是指标值和单位的映射
     */
    Map<Map<String, String>, TSDBSeries> seriesResult(JSONObject query, Long interval);

    Map<Map<String, String>, TSDBSeries> seriesResult(QueryRequest queryRequest);

    /**
     * 获取指定指标的标签值结果
     *
     * @param apiKey     API密钥（可选，若为空则使用默认值）
     * @param metricCode 指标代码（必填）
     * @param wheres     查询过滤条件集合
     * @return 返回标签键到其对应值集合的映射，若指标不存在则返回空Map
     */
    Map<String, Set<String>> showTagValuesResult(String apiKey, String metricCode, List<Where> wheres);

    /**
     * 获取指定指标的标签值结果
     *
     * @param apiKey     API密钥（可选，若为空则使用默认值）
     * @param metricCode 指标代码（必填）
     * @param wheres     查询过滤条件集合
     * @param keys       需要获取的标签键列表
     * @return 返回标签键到其对应值集合的映射，若指标不存在则返回空Map
     */
    Map<String, Set<String>> showTagValuesResult(String apiKey, String metricCode, List<Where> wheres, Collection<String> keys);

    /**
     * 获取指定指标的标签值结果
     *
     * @param apiKey     API密钥（可选，若为空则使用默认值）
     * @param metricCode 指标代码（必填）
     * @param wheres     查询过滤条件集合
     * @param keys       需要获取的标签键列表
     * @param start      查询起始时间戳（单位：毫秒）
     * @param end        查询结束时间戳（单位：毫秒）
     * @return 返回标签键到其对应值集合的映射，若指标不存在则返回空Map
     */
    Map<String, Set<String>> showTagValuesResult(String apiKey, String metricCode, List<Where> wheres, Collection<String> keys, Long start, Long end);

    Map<String, Set<String>> showTagValuesResult(String apiKey, String metricCode, List<Where> wheres, Collection<String> keys, Integer period);

    Map<Map<String, String>, TSDBSeries> seriesResult(QueryBuilderX queryBuilder);

    Map<String, Map<Map<String, String>, TSDBSeries>> seriesResult(Map<String, QueryRequest> queryRequestMap);

    Map<Map<String, String>, JSONObject> baselineResult(QueryRequest query, String comparison, Double baselineScope);

    /**
     * 生成列表结果
     *
     * @param query 查询参数，一个JSON对象，包含指标、分组、过滤、排序等信息 样例：{"A":{"aggs":"mean","metric":"system.cpu.usage","by":["host"],"from":[{"left":"host","right":"databuff193","operator":"="}]}}
     * @return 一个Map对象，其中键是分组字段的映射，值是指标值和单位的映射
     */
    Map<Map<String, String>, Collection<MetricDTO>> listResult(QueryRequest query);

    Map<Map<String, String>, Collection<MetricDTO>> listResult(QueryBuilderX queryBuilder);

    TSDBResultSet executeQuery(QueryBuilderX builder, AggFun tsAgg, AggFun valAgg, AggFun topAgg, JSONObject otherParam);

    Set<Map<String, String>> noDataResult(DatabuffMonitorView monitor, Collection<QueryRequest> queries);

    @SneakyThrows
    Set<Map<String, String>> delayResult(DatabuffMonitorView monitor, Collection<QueryRequest> queries, Integer evaluationDelay);
}
```

```java
package com.databuff.metric;

import com.databuff.common.metric.WrapData;
import com.databuff.common.tsdb.dto.explore.QueryRequest;
import com.databuff.common.tsdb.dto.preview.RuleDTO;

import java.util.Collection;
import java.util.List;

public interface MetricPreviewer {

    List<WrapData> preview(List<WrapData> wrapData, RuleDTO rule, Collection<QueryRequest> requestCollection);
}
```



#### 该废弃的接口/类 2.9.0及以后可删除
<font style="color:#080808;background-color:#ffffff;">TSDBMetricMMT</font>

<font style="color:#080808;background-color:#ffffff;">TSDBBaseMetricMMT</font>

<font style="color:#080808;background-color:#ffffff;">TSDBMetricOperator</font>

<font style="color:#080808;background-color:#ffffff;">MetricDTO</font>

<font style="color:#080808;background-color:#ffffff;">MetricByGroupGraph</font>

<font style="color:#080808;background-color:#ffffff;">MetricInstanceOperator</font>

<font style="color:#080808;background-color:#ffffff;">MetricPreviewer</font>

<font style="color:#080808;background-color:#ffffff;">MetricSQLBuilder</font>

