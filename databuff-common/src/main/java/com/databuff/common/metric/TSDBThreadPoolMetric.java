package com.databuff.common.metric;

import com.databuff.common.tsdb.model.TSDBPoint;
import com.databuff.common.utils.PointUtil;
import com.databuff.common.utils.TSDBPointUtil;
import com.databuff.moredb.proto.Common;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ThreadPoolExecutor;

public class TSDBThreadPoolMetric {

    private static final String METRIC_NAME = "thread.pool";
    private static TSDBThreadPoolMetric instance = new TSDBThreadPoolMetric();

    public static TSDBThreadPoolMetric getInstance() {
        return instance;
    }

    private Map<String, ThreadPoolExecutor> executors = new ConcurrentHashMap<>();

    public void addThreadPoolExecutor(String poolName, ThreadPoolExecutor executor) {
        if (poolName != null && executor != null) {
            executors.put(poolName, executor);
        }
    }

    public List<TSDBPoint> collectorMetric(String serviceName, long currentTime) {
        List<TSDBPoint> pointList = new ArrayList<>();
        for (Map.Entry<String, ThreadPoolExecutor> entry : executors.entrySet()) {
            String poolName = entry.getKey();
            Map<String, String> tags = new HashMap<>();
            tags.put("poolName", poolName);
            ThreadPoolExecutor executor = entry.getValue();
            int poolSize = executor.getPoolSize();
            int poolActiveCount = executor.getActiveCount();
            int queueRemainingCapacity = executor.getQueue().remainingCapacity();
            pointList.add(TSDBPointUtil.buildGaugePoint(serviceName, METRIC_NAME, currentTime, "poolSize", poolSize, tags));
            pointList.add(TSDBPointUtil.buildGaugePoint(serviceName, METRIC_NAME, currentTime, "poolActiveCount", poolActiveCount, tags));
            pointList.add(TSDBPointUtil.buildGaugePoint(serviceName, METRIC_NAME, currentTime, "queueRemainingCapacity", queueRemainingCapacity, tags));
        }
        return pointList;
    }
}
