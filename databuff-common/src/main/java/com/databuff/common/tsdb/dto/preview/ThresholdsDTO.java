package com.databuff.common.tsdb.dto.preview;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@ApiModel("阈值配置DTO")
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class ThresholdsDTO {
    @ApiModelProperty(value = "严重告警阈值", example = "5")
    private Number critical;

    @ApiModelProperty(value = "警告告警阈值", example = "1")
    private Number warning;

    @ApiModelProperty(value = "严重阈值状态", example = "98")
    @JsonProperty("critical_state")
    @JSONField(name = "critical_state")
    private Number criticalState;

    @ApiModelProperty(value = "严重阈值状态", example = "98")
    @JsonProperty("warning_state")
    @JSONField(name = "warning_state")
    private Number warningState;

    private String comparison;
    private String unit;
    private String checkName;
    private String mName;
    private String msg;
    private String trgTrd;
    private String trgValue;
    private Number value;
}