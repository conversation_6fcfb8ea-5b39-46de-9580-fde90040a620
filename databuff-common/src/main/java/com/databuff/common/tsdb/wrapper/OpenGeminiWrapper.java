package com.databuff.common.tsdb.wrapper;

import com.databuff.common.metric.AutoFillThreadLocal;
import com.databuff.common.metric.dto.Query;
import com.databuff.common.tsdb.builder.QueryBuilder;
import com.databuff.common.tsdb.model.*;
import com.databuff.common.tsdb.util.OpenGeminiRetConvertUtil;
import com.databuff.common.utils.OtelMetricUtil;
import com.databuff.moredb.model.util.StringUtils;
import lombok.extern.slf4j.Slf4j;
import okhttp3.OkHttpClient;
import org.influxdb.BatchOptions;
import org.influxdb.InfluxDB;
import org.influxdb.InfluxDBFactory;
import org.influxdb.dto.BatchPoints;
import org.influxdb.dto.Point;
import org.influxdb.dto.Pong;
import org.influxdb.dto.QueryResult;

import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.stream.Collectors;

@Slf4j
public class OpenGeminiWrapper implements DatabaseWrapper {

    private final Map<String, Object> config;
    private final DatabaseType databaseType;
    private final AtomicBoolean closed = new AtomicBoolean(false);
    private InfluxDB influxDB;
    private boolean batchEnabled = false;

    public OpenGeminiWrapper(Map<String, Object> config, DatabaseType databaseType) {
        this.databaseType = databaseType;
        this.config = config;

        // 创建连接
        createConnection();

        // 启用批处理
        if (this.influxDB != null) {
            enableBatchProcessing("initialization");
            checkConnection();
        }
    }

    /**
     * 创建数据库连接
     */
    private void createConnection() {
        String serverUrl = (String) config.get("url");
        String username = (String) config.get("user");
        String password = (String) config.get("password");

        // 创建 OkHttpClient 并设置超时时间
        OkHttpClient.Builder okHttpClient = new OkHttpClient.Builder();
        // 默认超时配置
        int connectTimeout = 10;
        int readTimeout = 120;
        int writeTimeout = 60;

        // 尝试从配置读取自定义超时
        if (config.containsKey("queryTimeout")) {
            try {
                Object timeoutObj = config.get("queryTimeout");
                int customTimeoutMs = Integer.parseInt(timeoutObj.toString());
                if (customTimeoutMs > 0) {
                    readTimeout = customTimeoutMs / 1000;
                    writeTimeout = customTimeoutMs / 1000;
                    log.info("使用自定义查询超时设置: {}s", readTimeout);
                } else {
                    log.warn("无效的queryTimeout值: {}，使用默认配置", timeoutObj);
                }
            } catch (NumberFormatException e) {
                log.error("queryTimeout配置格式错误: {}", config.get("queryTimeout"), e);
            }
        }

        // 应用最终超时配置
        okHttpClient.connectTimeout(connectTimeout, TimeUnit.SECONDS)
                .readTimeout(readTimeout, TimeUnit.SECONDS)
                .writeTimeout(writeTimeout, TimeUnit.SECONDS);

        // 使用InfluxDB客户端连接OpenGemini，因为OpenGemini与InfluxDB兼容
        String url = "http://" + serverUrl;
        try {
            if (username != null && password != null) {
                this.influxDB = InfluxDBFactory.connect(url, username, password, okHttpClient);
            } else {
                this.influxDB = InfluxDBFactory.connect(url);
            }

            // 记录实际生效的超时配置
            log.info("OkHttpClient配置: connectTimeout={}s, readTimeout={}s, writeTimeout={}s",
                    connectTimeout, readTimeout, writeTimeout);

        } catch (Exception e) {
            log.error("初始化InfluxDB连接失败: {}", e.getMessage(), e);
            this.influxDB = null;
        }

        // 检查influxDB连接是否成功创建
        if (this.influxDB == null) {
            log.error("初始化InfluxDB连接失败，批处理将不会启用");
        }
    }

    /**
     * 检查数据库连接状态
     */
    private void checkConnection() {
        if (this.influxDB == null) {
            return;
        }

        String url = "http://" + config.get("url");
        try {
            Pong pong = this.influxDB.ping();
            if (pong.getVersion() == null || pong.getVersion().isEmpty()) {
                log.warn("连接到OpenGemini失败，地址: {}", url);
            } else {
                log.info("已成功连接到OpenGemini，地址: {}，版本: {}", url, pong.getVersion());
            }
        } catch (Exception e) {
            log.error("连接OpenGemini时出错，地址: {}", url, e);
        }
    }

    @Override
    public Map<String, Object> getConfigs() {
        return config;
    }

    @Override
    public DatabaseType getDatabaseType() {
        return databaseType;
    }

    @Override
    public void close() {
        if (closed.compareAndSet(false, true)) {
            try {
                log.info("正在关闭OpenGeminiWrapper连接");
                closeConnection();
                log.info("OpenGeminiWrapper连接已成功关闭");
            } catch (Exception e) {
                log.error("关闭OpenGeminiWrapper连接时出错: {}", e.getMessage(), e);
            }
        }
    }

    /**
     * 执行查询并返回结果
     *
     * @param query    查询对象
     * @param timeUnit 时间单位
     * @param builder  查询构建器
     * @return 查询结果集
     */
    public TSDBResultSet query(Query query, TimeUnit timeUnit, QueryBuilder builder) {
        // 创建一个新的TSDBResult对象用于结果转换
        TSDBResult tsdbResult = new TSDBResult();

        // 格式化SQL并执行查询
        QueryResult queryResult = executeQuery(query, tsdbResult);

        // 转换查询结果
        String timeUnitStr = TimeUnit.SECONDS.equals(timeUnit) ? "s" : "ms";
        AtomicBoolean hasCountFunction = new AtomicBoolean(false);
        if (query.getCommand() != null && query.getCommand().toLowerCase().contains("count(")) {
            hasCountFunction.set(true);
        }

        final TSDBResultSet resultSet = OpenGeminiRetConvertUtil.formatResult(
                tsdbResult,
                queryResult,
                timeUnitStr,
                hasCountFunction,
                builder == null ? null : builder.getQueryType()
        );

        // 处理自动填充
        processAutoFill(tsdbResult, resultSet);

        return resultSet;
    }

    /**
     * 执行查询并设置查询类型
     *
     * @param query 查询对象
     * @param tsdbResult 结果对象
     * @return 原始查询结果
     */
    private QueryResult executeQuery(Query query, TSDBResult tsdbResult) {
        // 格式化SQL，处理特殊情况
//        AtomicBoolean hasCountFunction = new AtomicBoolean(false);
        final String command = query.getCommand();
//        String formattedSql = OpenGeminiRetConvertUtil.formatInfluxSql(command, hasCountFunction);
        String formattedSql = command;

        // 执行查询
        QueryResult queryResult = influxDB.query(new org.influxdb.dto.Query(formattedSql, query.getDatabase()));

        // 根据原始SQL命令设置查询类型
        setQueryType(command, tsdbResult);

        return queryResult;
    }

    /**
     * 根据命令设置查询类型
     *
     * @param command SQL命令
     * @param tsdbResult 结果对象
     */
    private void setQueryType(String command, TSDBResult tsdbResult) {
        if (command != null) {
            String lowerCommand = command.toLowerCase().trim();
            if (lowerCommand.startsWith("show tag values")) {
                tsdbResult.setQueryType("SHOW TAG VALUES");
            } else if (lowerCommand.startsWith("show tag keys")) {
                tsdbResult.setQueryType("SHOW TAG KEYS");
            } else if (lowerCommand.startsWith("show measurements")) {
                tsdbResult.setQueryType("SHOW MEASUREMENTS");
            } else if (lowerCommand.startsWith("show field keys")) {
                tsdbResult.setQueryType("SHOW FIELD KEYS");
            } else {
                tsdbResult.setQueryType("QUERY");
            }
        }
    }

    /**
     * 处理自动填充空值
     *
     * @param tsdbResult 原始结果
     * @param resultSet 转换后的结果集
     */
    private void processAutoFill(TSDBResult tsdbResult, TSDBResultSet resultSet) {
        final Double fill = AutoFillThreadLocal.get();
        if (fill != null && "QUERY".equals(tsdbResult.getQueryType())) {
            final List<TSDBResult> results = resultSet.getResults();
            if (results != null) {
                for (TSDBResult result : results) {
                    List<TSDBSeries> series = result.getSeries();
                    if (series == null) {
                        continue;
                    }
                    for (TSDBSeries s : series) {
                        List<List<Object>> values = s.getValues();
                        if (values == null || values.isEmpty()) {
                            continue;
                        }
                        List<String> columns = s.getColumns();
                        if (columns == null || columns.isEmpty()) {
                            continue;
                        }
                        int timeIndex = columns.indexOf("time");
                        if (timeIndex < 0) {
                            continue;
                        }
                        for (List<Object> value : values) {
                            for (int i = 0; i < value.size(); i++) {
                                if (i != timeIndex && value.get(i) == null) {
                                    value.set(i, fill);
                                }
                            }
                        }
                    }
                }
            }
        }
    }


    /**
     * 检查数据库是否存在
     *
     * @param database 数据库名称
     * @return 是否存在
     */
    public boolean databaseExists(String database) {
        try {
            QueryResult result = influxDB.query(new org.influxdb.dto.Query("SHOW DATABASES", ""));
            Collection<String> databases = extractFirstColumnValues(result);
            return databases.contains(database);
        } catch (Exception e) {
            log.error("Error checking if database exists: {}", database, e);
            return false;
        }
    }

    /**
     * 创建数据库
     *
     * @param database    数据库名称
     * @param userName    用户名
     * @param password    密码
     * @param shard       分片数
     * @param replication 副本数
     * @param keepDay     保留天数
     * @param interval    间隔
     */
    public void createDatabase(String database, String userName, String password, int shard, int replication, int keepDay, int interval) {
        String createDbQuery = generateCreateDatabaseSql(database, shard, replication, keepDay, interval);
        influxDB.query(new org.influxdb.dto.Query(createDbQuery, ""));
    }

    /**
     * 生成创建数据库的SQL
     * 基于OpenGemini语法：CREATE DATABASE <database_name> [WITH [DURATION <duration>] [REPLICATION <n>] [SHARD DURATION <duration>] [INDEX DURATION <duration>] [NAME <retention-policy-name>]]
     *
     * @param database    数据库名称
     * @param shard       分片数（用于设置SHARD DURATION，单位为小时）
     * @param replication 副本数
     * @param keepDay     保留天数（DURATION参数）
     * @param interval    索引保留间隔（小时，用于INDEX DURATION）
     * @return SQL语句
     */
    private String generateCreateDatabaseSql(String database, int shard, int replication, int keepDay, int interval) {
        StringBuilder sql = new StringBuilder();
        sql.append("CREATE DATABASE \"").append(database).append("\"");

        // 检查是否需要添加WITH子句
        boolean needWith = shard > 0 || replication > 0 || keepDay > 0 || interval > 0;

        if (needWith) {
            sql.append(" WITH");

            // 添加数据保留时长
            if (keepDay > 0) {
                sql.append(" DURATION ").append(keepDay).append("d");
            }

            // 添加副本数
            if (replication > 0) {
                sql.append(" REPLICATION ").append(replication);
            }

            // 添加分片组时间跨度
            if (shard > 0) {
                sql.append(" SHARD DURATION ").append(shard).append("h");
            }

            // 添加索引数据保留时长
            if (interval > 0) {
                sql.append(" INDEX DURATION ").append(interval).append("h");
            }

            // 默认使用autogen作为保留策略名称
            sql.append(" NAME \"autogen\"");
        }

        return sql.toString();
    }

    /**
     * 写入数据点
     *
     * @param database 数据库名称
     * @param points   数据点列表
     */
    public void write(String database, List<TSDBPoint> points) {
        if (database == null) {
            throw new IllegalArgumentException("database is null");
        }
        if (points == null || points.isEmpty()) {
            return;
        }

        TimeUnit timeUnit = points.get(0).getTimeUnit();
        if (timeUnit == null) {
            timeUnit = TimeUnit.MILLISECONDS;
        }

        BatchPoints batchPoints = BatchPoints.database(database)
                .precision(timeUnit)
                .consistency(InfluxDB.ConsistencyLevel.QUORUM)
                .retentionPolicy("autogen")
                .build();

        int validPointsCount = 0;

        for (TSDBPoint tsdbPoint : points) {
            Point point = convertTSDBPointToPoint(tsdbPoint);
            if (point != null) {
                batchPoints.point(point);
                validPointsCount++;
            }
        }

        // 检查是否有有效点要写入
        if (validPointsCount == 0) {
            log.debug("[无有效数据点] 没有有效的数据点写入数据库 {}", database);
            return;
        }

        // 写入数据
        writePoints(database, batchPoints.getPoints());
    }

    /**
     * 将TSDBPoint转换为InfluxDB Point
     *
     * @param tsdbPoint 数据点
     * @return 转换后的Point，如果无效则返回null
     */
    private Point convertTSDBPointToPoint(TSDBPoint tsdbPoint) {
        // 验证measurement
        final String measurement = tsdbPoint.getMeasurement();
        if (measurement == null) {
            log.debug("[无效measurement] 跳过measurement为空的数据点: {}", tsdbPoint);
            return null;
        }

        Point.Builder pointBuilder = Point.measurement(measurement)
                .time(tsdbPoint.getTimestamp(), tsdbPoint.getTimeUnit());

        // 处理标签
        processTags(tsdbPoint, pointBuilder, measurement);

        // 处理字段
        boolean hasValidFields = processFields(tsdbPoint, pointBuilder, measurement);

        // 检查是否有有效字段
        if (!hasValidFields || !pointBuilder.hasFields()) {
            log.error("[无有效字段] 跳过measurement {} 的数据点，因为它没有有效字段，时间戳: {}",
                    measurement, tsdbPoint.getTimestamp());

            if (tsdbPoint.getFields() != null && !tsdbPoint.getFields().isEmpty()) {
                log.error("[原始字段] 所有无效的原始字段: {}", tsdbPoint.getFields());
            }
            return null;
        }

        // 构建数据点
        return pointBuilder.build();
    }

    /**
     * 处理标签
     *
     * @param tsdbPoint 数据点
     * @param pointBuilder 点构建器
     * @param measurement 测量名称
     * @return 有效标签数量
     */
    private int processTags(TSDBPoint tsdbPoint, Point.Builder pointBuilder, String measurement) {
        int validTagsCount = 0;
        int invalidTagsCount = 0;

        if (tsdbPoint.getTags() != null) {
            for (Map.Entry<String, String> tag : tsdbPoint.getTags().entrySet()) {
                String key = tag.getKey();
                String value = tag.getValue();

                // 验证标签键
                if (key == null || key.trim().isEmpty()) {
                    log.debug("[无效标签键] 跳过measurement {} 中键为空的标签: {}={}",
                            measurement, key, value);
                    invalidTagsCount++;
                    continue;
                }

                // 验证标签值
                if (value == null) {
                    log.debug("[无效标签值] measurement {} 中键 '{}' 的标签值为空，使用空字符串",
                            measurement, key);
                    value = StringUtils.EMPTY;
                } else {
                    value = formatTagValue(value);
                }

                pointBuilder.tag(key, value);
                validTagsCount++;
            }
        }

        if (invalidTagsCount > 0) {
            log.debug("[标签统计] measurement {}: {} 个有效标签, {} 个无效标签被跳过",
                    measurement, validTagsCount, invalidTagsCount);
        }

        return validTagsCount;
    }

    /**
     * 处理字段
     *
     * @param tsdbPoint 数据点
     * @param pointBuilder 点构建器
     * @param measurement 测量名称
     * @return 是否有有效字段
     */
    private boolean processFields(TSDBPoint tsdbPoint, Point.Builder pointBuilder, String measurement) {
        int validFieldsCount = 0;
        int invalidFieldsCount = 0;

        if (tsdbPoint.getFields() == null || tsdbPoint.getFields().isEmpty()) {
            return false;
        }

        Map<String, Object> fields = tsdbPoint.getFields();

        for (Map.Entry<String, Object> field : fields.entrySet()) {
            String fieldKey = field.getKey();
            Object value = field.getValue();

            // 验证字段键
            if (fieldKey == null || fieldKey.trim().isEmpty()) {
                log.debug("[无效字段键] 跳过measurement {} 中键为空的字段", measurement);
                invalidFieldsCount++;
                continue;
            }

            // 验证字段值
            if (value == null) {
                log.debug("[空字段值] 跳过measurement {} 中字段 '{}' 的空值",
                        measurement, fieldKey);
                invalidFieldsCount++;
                continue;
            }

            // 处理字符串"null"的情况
            if (value instanceof String && "null".equals(value)) {
                log.debug("[字符串null值] 跳过measurement {} 中字段 '{}' 的字符串'null'值",
                        measurement, fieldKey);
                invalidFieldsCount++;
                continue;
            }

            // 处理数字类型
            if (value instanceof Number) {
                if (processNumberField(pointBuilder, fieldKey, (Number) value, measurement)) {
                    validFieldsCount++;
                } else {
                    invalidFieldsCount++;
                }
            }
            // 处理字符串类型 - 尝试转换为数字
            else if (value instanceof String) {
                if (processStringField(pointBuilder, fieldKey, (String) value, measurement)) {
                    validFieldsCount++;
                } else {
                    invalidFieldsCount++;
                }
            }
            // 处理其他类型 - 不支持
            else {
                log.debug("[不支持的类型] 跳过measurement {} 中非数字字段 '{}': {} (类型: {})",
                        measurement, fieldKey, value, value.getClass().getName());
                invalidFieldsCount++;
            }
        }

        // 记录字段处理统计
        if (invalidFieldsCount > 0) {
            log.debug("[字段统计] measurement {}: {} 个有效字段, {} 个无效字段被跳过",
                    measurement, validFieldsCount, invalidFieldsCount);
        }

        return validFieldsCount > 0;
    }

    /**
     * 处理数字类型字段
     *
     * @param pointBuilder 点构建器
     * @param fieldKey 字段名
     * @param numValue 数字值
     * @param measurement 测量名称
     * @return 是否处理成功
     */
    private boolean processNumberField(Point.Builder pointBuilder, String fieldKey, Number numValue, String measurement) {
        // 检查NaN和Infinity
        if ((numValue instanceof Double && (((Double) numValue).isNaN() || ((Double) numValue).isInfinite())) ||
                (numValue instanceof Float && (((Float) numValue).isNaN() || ((Float) numValue).isInfinite()))) {
            log.debug("[无效数字] 跳过measurement {} 中字段 '{}' 的NaN/无穷大值: {}",
                    measurement, fieldKey, numValue);
            return false;
        }

        // 添加数字字段，保留原始类型
        pointBuilder.addField(fieldKey, numValue);
        return true;
    }

    /**
     * 处理字符串类型字段
     *
     * @param pointBuilder 点构建器
     * @param fieldKey 字段名
     * @param strValue 字符串值
     * @param measurement 测量名称
     * @return 是否处理成功
     */
    private boolean processStringField(Point.Builder pointBuilder, String fieldKey, String strValue, String measurement) {
        // 检查空字符串
        if (strValue.trim().isEmpty()) {
            log.debug("[空字符串] 跳过measurement {} 中字段 '{}' 的空字符串",
                    measurement, fieldKey);
            return false;
        }

        // 尝试转换为数字
        try {
            // 尝试解析为整数
            if (strValue.matches("-?\\d+")) {
                try {
                    long longValue = Long.parseLong(strValue);
                    pointBuilder.addField(fieldKey, longValue);
                    return true;
                } catch (NumberFormatException e) {
                    // 超出Long范围，尝试解析为Double
                    double doubleValue = Double.parseDouble(strValue);
                    pointBuilder.addField(fieldKey, doubleValue);
                    return true;
                }
            } else {
                // 尝试解析为浮点数
                double doubleValue = Double.parseDouble(strValue);
                pointBuilder.addField(fieldKey, doubleValue);
                return true;
            }
        } catch (NumberFormatException e) {
            log.debug("[解析错误] 无法将measurement {} 中字段 '{}' 的字符串解析为数字: '{}'",
                    measurement, fieldKey, strValue);
            return false;
        }
    }

    /**
     * 写入数据点
     *
     * @param database 数据库名称
     * @param points 数据点列表
     */
    private void writePoints(String database, List<Point> points) {
        for (Point point : points) {
            try {
                // 根据批处理是否启用选择写入方式
                if (this.batchEnabled) {
                    // 已经启用了攒批写入，所以这里推荐使用单point写入
                    influxDB.write(database, "autogen", point);
                } else {
                    // 批处理未启用，使用同步写入方式
                    log.debug("批处理未启用，使用同步写入方式");
                    influxDB.write(database, "autogen", point);
                }
            } catch (Exception e) {
                log.error("[写入错误] 写入数据点到数据库 {} 失败: {}", database, e.getMessage(), e);
            }
        }
    }

    /**
     * 显示标签键
     *
     * @param query 查询对象
     * @return 标签键集合
     */
    public Collection<String> showTagKeys(Query query) {
        QueryResult queryResult = influxDB.query(new org.influxdb.dto.Query(query.getCommand(), query.getDatabase()));
        return extractFirstColumnValues(queryResult);
    }

    /**
     * 从查询结果中提取第一列的值
     *
     * @param queryResult 查询结果
     * @return 第一列的值集合
     */
    private Collection<String> extractFirstColumnValues(QueryResult queryResult) {
        if (queryResult.hasError() || queryResult.getResults().isEmpty() || queryResult.getResults().get(0).getSeries() == null) {
            return Collections.emptyList();
        }

        List<List<Object>> values = queryResult.getResults().get(0).getSeries().get(0).getValues();
        if (values == null) {
            return Collections.emptyList();
        }

        return values.stream()
                .filter(Objects::nonNull)
                .filter(value -> !value.isEmpty())
                .map(value -> String.valueOf(value.get(0)))
                .collect(Collectors.toList());
    }

    /**
     * 检查批处理是否已启用
     *
     * @return 批处理是否已启用
     */
    public boolean isBatchEnabled() {
        return this.batchEnabled && this.influxDB != null && !closed.get();
    }

    /**
     * 启用批处理
     *
     * @param context 上下文信息，用于日志记录
     */
    private void enableBatchProcessing(String context) {
        try {
            // 从配置中读取批处理参数
            BatchOptions options = createBatchOptions();

            // 启用批处理
            if (this.influxDB != null) {
                this.influxDB.enableBatch(options);
                this.batchEnabled = true;
                log.info("批处理在 {} 期间成功启用", context);
            } else {
                log.error("无法在 {} 期间启用批处理 - influxDB为空", context);
            }
        } catch (Exception e) {
            log.error("在 {} 期间启用批处理失败: {}", context, e.getMessage(), e);
            // 批处理启用失败，但连接可能仍然有效，继续使用
        }
    }

    /**
     * 创建批处理选项
     *
     * @return 批处理选项
     */
    private BatchOptions createBatchOptions() {
        // 默认值
        int batchActions = 500;
        int flushDuration = 10000;
        int bufferLimit = 10000;
        int jitterDuration = 2000;

        try {
            // 读取并验证batchActions
            batchActions = getConfigIntValue("batchActions", batchActions);

            // 读取并验证flushDuration
            flushDuration = getConfigIntValue("flushDuration", flushDuration);

            // 读取并验证bufferLimit
            bufferLimit = getConfigIntValue("bufferLimit", bufferLimit);

            // 读取并验证jitterDuration
            jitterDuration = getConfigIntValue("jitterDuration", jitterDuration);
        } catch (NumberFormatException e) {
            log.error("解析批处理配置参数时出错，使用默认值", e);
        }

        // 确保bufferLimit大于batchActions，以启用RetryCapableBatchWriter
        if (bufferLimit <= batchActions) {
            bufferLimit = batchActions * 2;
            log.info("bufferLimit必须大于batchActions才能启用重试功能，已自动调整bufferLimit={}", bufferLimit);
        }

        log.info("创建批处理选项，动作数={}，刷新时间={}，抖动时间={}，缓冲区限制={}",
                batchActions, flushDuration, jitterDuration, bufferLimit);

        return BatchOptions.DEFAULTS
                // 设置批量大小，减少单次失败的数据量
                .actions(batchActions)
                // 设置刷新间隔，避免频繁写入
                .flushDuration(flushDuration)
                // 添加抖动时间，避免多个客户端同时写入
                .jitterDuration(jitterDuration)
                // 设置足够大的缓冲区，确保能够存储足够的失败数据点
                // 注意：必须大于actions值才会启用RetryCapableBatchWriter
                .bufferLimit(bufferLimit)
                // 设置为false，当队列满时阻塞而不是丢弃数据
                .dropActionsOnQueueExhaustion(false)
                // 设置异常处理器，记录错误并进行告警
                .exceptionHandler((failedPoints, throwable) -> {
                    // 记录错误日志
                    log.error("批量写入失败，将进行重试. 错误: " + throwable.getMessage(), throwable);

                    for (Point failedPoint : failedPoints) {
                        if (failedPoint == null) {
                            continue;
                        }
                        log.error("失败的数据点: {}", failedPoint.lineProtocol());
                        // 使用OtelMetricUtil记录指标数据
                        Map<String, String> tags = new HashMap<>();
                        tags.put("measurement", getMeasurementFromLineProtocol(failedPoint));
                        tags.put("error", throwable.getClass().getSimpleName());
                        tags.put("detailMessage", throwable.getMessage());
                        OtelMetricUtil.logCounter("tsdb.send.failed", tags, 1);
                    }

                    // 记录失败的数据点数量
                    if (failedPoints != null) {
                        log.error("失败的数据点数量: {}", failedPoints.spliterator().estimateSize());
                    }
                })
                // 设置更高的一致性级别，确保数据写入多个节点
                .consistency(InfluxDB.ConsistencyLevel.ALL);
    }

    public String getMeasurementFromLineProtocol(Point point) {
        String lineProtocol = point.lineProtocol();
        // 行协议格式: measurement,tag1=value1,tag2=value2 field1=value1,field2=value2 timestamp
        // 提取第一个逗号或空格前的内容
        int commaIndex = lineProtocol.indexOf(',');
        int spaceIndex = lineProtocol.indexOf(' ');

        if (commaIndex == -1 && spaceIndex == -1) {
            return lineProtocol; // 没有标签和字段的极端情况
        }

        if (commaIndex == -1) {
            return lineProtocol.substring(0, spaceIndex);
        }

        if (spaceIndex == -1) {
            return lineProtocol.substring(0, commaIndex);
        }

        return lineProtocol.substring(0, Math.min(commaIndex, spaceIndex));
    }

    /**
     * 从配置中获取整数值，并验证是否大于0
     *
     * @param key 配置键
     * @param defaultValue 默认值
     * @return 配置值或默认值
     */
    private int getConfigIntValue(String key, int defaultValue) {
        if (config.containsKey(key)) {
            try {
                int value = Integer.parseInt(config.get(key).toString());
                if (value <= 0) {
                    log.debug("无效的 {} 值: {}，使用默认值: {}", key, value, defaultValue);
                    return defaultValue;
                }
                return value;
            } catch (NumberFormatException e) {
                log.debug("无效的 {} 值格式，使用默认值: {}", key, defaultValue);
                return defaultValue;
            }
        }
        return defaultValue;
    }

    @Override
    public boolean isConnected() {
        try {
            if (influxDB == null || closed.get()) {
                return false;
            }

            return isPingConnected() || isQueryConnected();
        } catch (Exception e) {
            log.warn("检查OpenGeminiWrapper连接时出错: {}", e.getMessage());
            return false;
        }
    }

    /**
     * 通过ping操作检查连接是否有效
     *
     * @return 连接是否有效
     */
    private boolean isPingConnected() {
        try {
            Pong response = influxDB.ping();
            // 只检查response是否为null，不再检查version
            // 因为在某些情况下version可能为null但连接仍然有效
            if (response != null) {
                log.debug("连接ping成功，版本: {}",
                        response.getVersion() != null ? response.getVersion() : "未知");
                return true;
            }
        } catch (Exception e) {
            log.warn("Ping失败，将尝试其他连接检查方式: {}", e.getMessage());
            // 即使ping失败，我们也继续尝试其他方法
        }
        return false;
    }

    /**
     * 通过简单查询检查连接是否有效
     *
     * @return 连接是否有效
     */
    private boolean isQueryConnected() {
        try {
            // 执行一个简单的查询，如果不抛出异常，则认为连接有效
            influxDB.query(new org.influxdb.dto.Query("SHOW DATABASES", ""));
            log.info("通过简单查询验证连接成功");
            return true;
        } catch (Exception e) {
            log.warn("简单查询失败: {}", e.getMessage());
            return false;
        }
    }

    @Override
    public boolean reconnect() {
        try {
            log.info("正在尝试重新连接OpenGeminiWrapper");
            // 关闭现有连接
            closeConnection();

            // 重置关闭状态
            closed.set(false);

            // 重新创建连接
            createConnection();

            // 重新启用批处理
            this.batchEnabled = false;
            if (this.influxDB != null) {
                enableBatchProcessing("reconnect");
            }

            // 验证连接是否成功
            boolean connected = isConnected();
            if (connected) {
                log.info("OpenGeminiWrapper重新连接成功");
            } else {
                log.error("OpenGeminiWrapper重新连接失败");
            }
            return connected;
        } catch (Exception e) {
            log.error("OpenGeminiWrapper重新连接时出错: {}", e.getMessage(), e);
            return false;
        }
    }

    /**
     * 关闭当前数据库连接
     */
    private void closeConnection() {
        if (influxDB != null) {
            try {
                influxDB.close();
            } catch (Exception e) {
                log.warn("关闭现有连接时出错: {}", e.getMessage());
            } finally {
                influxDB = null;
            }
        }
    }

    @Override
    public void resetState() {
        try {
            // 对于InfluxDB连接，重置状态主要是确保连接有效
            if (!isConnected()) {
                reconnect();
            }

            // 重置任何查询设置，如数据库选择、保留策略等
            if (influxDB != null) {
                // 清除当前选择的数据库，让下一次查询显式指定
                influxDB.setDatabase(null);
                // 重置保留策略
                influxDB.setRetentionPolicy(null);
            }
        } catch (Exception e) {
            log.warn("重置OpenGeminiWrapper状态时出错: {}", e.getMessage());
        }
    }

    @Override
    public void clearTemporaryState() {
        try {
            // 对于InfluxDB连接，清理临时状态可能包括清理查询缓存或其他临时资源
            if (influxDB != null) {
                // 清除当前选择的数据库，让下一次查询显式指定
                influxDB.setDatabase(null);
                // 重置保留策略
                influxDB.setRetentionPolicy(null);
            }
        } catch (Exception e) {
            log.warn("清除OpenGeminiWrapper中的临时状态时出错: {}", e.getMessage());
        }
    }

    /**
     * 格式化标签值，按照InfluxDB/OpenGemini Line Protocol规范处理特殊字符
     * 特殊字符（逗号、等号、空格）需要用反斜杠转义而不是替换为自定义占位符
     * 同时处理已经包含转义字符的情况，避免双重转义
     * 参考: https://docs.influxdata.com/influxdb/cloud/reference/syntax/line-protocol/
     *
     * @param value 原始标签值
     * @return 格式化后的标签值
     */
    private String formatTagValue(String value) {
        if (value == null) {
            return null;
        }

        // 步骤1: 处理换行符和回车符 - 将它们替换为空格
        value = value.replace("\n", " ").replace("\r", " ");

        // 步骤2: 规范化空白字符 - 将多个连续的空格替换为一个空格
        value = value.replaceAll("\\s+", " ");
        // 将多个连续的tab替换为一个tab
        value = value.replaceAll("\\t+", "\t");

        return value.trim();
    }

}
