package com.databuff.common.audit;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Collection;
import java.util.function.Supplier;

/**
 * 审计实体
 *
 * <AUTHOR>
 * @package com.databuff.webapp.audit
 * @company: dacheng
 */
@ApiModel(description = "审计实体")
@Data
@NoArgsConstructor
public class AuditEntity {
    @ApiModelProperty(value = "唯一标识符")
    private String id;

    @ApiModelProperty(value = "名称")
    private String name;

    @ApiModelProperty(value = "描述")
    private String desc;

    @ApiModelProperty(value = "结果")
    private String outcome;
    public String getOutcome() {
        if (outcome == null) {
            return "成功";
        }
        return outcome;
    }

    @ApiModelProperty(value = "变更前值")
    private String beforeValue;

    @ApiModelProperty(value = "变更后值")
    private String afterValue;

    @ApiModelProperty(value = "错误信息")
    private String errorMessage;

    @ApiModelProperty(value = "操作")
    private String action;

    @ApiModelProperty(value = "账户名称")
    private String actor;

    @ApiModelProperty(value = "域ID")
    private String gid;

    AuditEntity(String id, String name, String desc, String outcome, String beforeValue, String afterValue, String errorMessage, String action, String actor, String gid) {
        this.id = id;
        this.name = name;
        this.desc = desc;
        this.outcome = outcome;
        this.beforeValue = beforeValue;
        this.afterValue = afterValue;
        this.errorMessage = errorMessage;
        this.action = action;
        this.actor = actor;
        this.gid = gid;
    }

    public static AuditEntityBuilder builder() {
        return new AuditEntityBuilder();
    }

    public static class AuditEntityBuilder {
        private String id;
        private String name;
        private String desc;
        private String outcome;
        private String beforeValue;
        private String afterValue;
        private String errorMessage;
        private String action;
        private String actor;
        private String gid;

        AuditEntityBuilder() {
        }

        public AuditEntityBuilder name(String name) {
            this.name = name;
            return this;
        }

        public AuditEntityBuilder name(Supplier<String> nameSupplier) {
            this.name = nameSupplier.get();
            return this;
        }

        public AuditEntityBuilder id(String id) {
            this.id = id;
            return this;
        }

        public AuditEntityBuilder id(Supplier<String> idSupplier) {
            this.id = idSupplier.get();
            return this;
        }

        public AuditEntityBuilder desc(String desc) {
            this.desc = desc;
            return this;
        }

        public AuditEntityBuilder desc(Supplier<String> descSupplier) {
            this.desc = descSupplier.get();
            return this;
        }

        public AuditEntityBuilder outcome(String outcome) {
            this.outcome = outcome;
            return this;
        }

        public AuditEntityBuilder outcome(Supplier<String> outcomeSupplier) {
            this.outcome = outcomeSupplier.get();
            return this;
        }

        public AuditEntityBuilder beforeValue(String beforeValue) {
            this.beforeValue = beforeValue;
            return this;
        }

        public AuditEntityBuilder beforeValue(Supplier<String> beforeValueSupplier) {
            this.beforeValue = beforeValueSupplier.get();
            return this;
        }

        public AuditEntityBuilder afterValue(String afterValue) {
            this.afterValue = afterValue;
            return this;
        }

        public AuditEntityBuilder afterValue(Supplier<String> afterValueSupplier) {
            this.afterValue = afterValueSupplier.get();
            return this;
        }

        public AuditEntityBuilder errorMessage(String errorMessage) {
            this.errorMessage = errorMessage;
            return this;
        }

        public AuditEntityBuilder errorMessage(Supplier<String> errorMessageSupplier) {
            this.errorMessage = errorMessageSupplier.get();
            return this;
        }

        public AuditEntityBuilder action(String action) {
            this.action = action;
            return this;
        }

        public AuditEntityBuilder action(Supplier<String> actionSupplier) {
            this.action = actionSupplier.get();
            return this;
        }

        public AuditEntityBuilder actor(String actor) {
            this.actor = actor;
            return this;
        }

        public AuditEntityBuilder actor(Supplier<String> actorSupplier) {
            this.actor = actorSupplier.get();
            return this;
        }

        public AuditEntityBuilder gid(String gid) {
            this.gid = gid;
            return this;
        }

        public AuditEntityBuilder gid(Supplier<String> gidSupplier) {
            this.gid = gidSupplier.get();
            return this;
        }

        public AuditEntity build() {
            final AuditEntity auditEntity = new AuditEntity(this.id, this.name, this.desc, this.outcome, this.beforeValue, this.afterValue, this.errorMessage, this.action, this.actor, this.gid);
            AuditParamUtil.setThreadLocal(auditEntity);
            return auditEntity;
        }

        public AuditEntity add() {
            final AuditEntity auditEntity = new AuditEntity(this.id, this.name, this.desc, this.outcome, this.beforeValue, this.afterValue, this.errorMessage, this.action, this.actor, this.gid);
            final Collection<AuditEntity> auditEntities = AuditParamUtil.get();
            if (auditEntities == null) {
                AuditParamUtil.setThreadLocal(auditEntity);
            } else {
                auditEntities.add(auditEntity);
            }
            return auditEntity;
        }

        public String toString() {
            return "AuditEntity.AuditEntityBuilder(id=" + this.id + ", name=" + this.name + ", desc=" + this.desc + ", outcome=" + this.outcome + ", beforeValue=" + this.beforeValue + ", afterValue=" + this.afterValue + ", errorMessage=" + this.errorMessage + ", action=" + this.action + ", actor=" + this.actor + ", gid=" + this.gid + ")";
        }
    }
}