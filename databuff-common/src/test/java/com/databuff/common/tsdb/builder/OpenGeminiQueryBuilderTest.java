package com.databuff.common.tsdb.builder;

import com.alibaba.fastjson.JSONObject;
import com.databuff.common.tsdb.model.*;
import com.google.common.collect.Lists;
import org.junit.Test;
import org.junit.jupiter.api.DisplayName;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

import static org.junit.Assert.*;

public class OpenGeminiQueryBuilderTest {

    @Test
    public void testBuildQuery() {
        // 创建查询构建器
        QueryBuilder builder = new QueryBuilder();
        builder.setDatabaseName("NzhEMjlBOTk3MzkxRjk5MjQyMzMzQzI4_infrastructure");
        builder.setMeasurement("system.core");
        builder.addAgg(new Aggregation(AggFun.MEAN, "usage"));
        builder.setInterval(60);
        builder.setIntervalUnit("s");

        // 创建OpenGeminiQueryBuilder
        OpenGeminiQueryBuilder queryBuilder = new OpenGeminiQueryBuilder(builder);

        // 执行测试
        String sql = queryBuilder.buildQuery();

        // 打印完整SQL
        System.out.println("\n===== testBuildQuery =====\n" + sql + "\n=====================\n");

        // 验证结果
        assertNotNull(sql);
        assertTrue(sql.contains("SELECT"));
        assertTrue(sql.contains("mean(\"usage\")"));
        assertTrue(sql.contains("FROM \"system.core\""));
        assertTrue(sql.contains("GROUP BY time(60s)"));
    }

    @Test
    public void testBuildQueryWithWhereConditions() {
        // 创建查询构建器
        QueryBuilder builder = new QueryBuilder();
        builder.setDatabaseName("NzhEMjlBOTk3MzkxRjk5MjQyMzMzQzI4_infrastructure");
        builder.setMeasurement("system.core");
        builder.addAgg(new Aggregation(AggFun.MEAN, "usage"));
        builder.addWhere(new Where("host", WhereOp.EQ, "server01"));
        builder.addWhere(new Where("time", WhereOp.GTE, 1609459200000L));
        builder.addWhere(new Where("time", WhereOp.LT, 1609545600000L));
        builder.setInterval(60);
        builder.setIntervalUnit("s");

        // 创建OpenGeminiQueryBuilder
        OpenGeminiQueryBuilder queryBuilder = new OpenGeminiQueryBuilder(builder);

        // 执行测试
        String sql = queryBuilder.buildQuery();

        // 打印完整SQL
        System.out.println("\n===== testBuildQueryWithWhereConditions =====\n" + sql + "\n=====================\n");

        // 验证结果
        assertNotNull(sql);
        assertTrue(sql.contains("SELECT"));
        assertTrue(sql.contains("mean(\"usage\")"));
        assertTrue(sql.contains("FROM \"system.core\""));
        assertTrue(sql.contains("WHERE"));
        assertTrue(sql.contains("\"host\" = \"server01\""));
        assertTrue(sql.contains("time >= 1609459200000"));
        assertTrue(sql.contains("time < 1609545600000"));
        assertTrue(sql.contains("GROUP BY time(60s)"));
    }

    @Test
    public void testBuildQueryWithGroupBy() {
        // 创建查询构建器
        QueryBuilder builder = new QueryBuilder();
        builder.setDatabaseName("NzhEMjlBOTk3MzkxRjk5MjQyMzMzQzI4_infrastructure");
        builder.setMeasurement("system.core");
        builder.addAgg(new Aggregation(AggFun.MEAN, "usage"));
        builder.addGroupBy("host");
        builder.addGroupBy("region");
        builder.setInterval(60);
        builder.setIntervalUnit("s");

        // 创建OpenGeminiQueryBuilder
        OpenGeminiQueryBuilder queryBuilder = new OpenGeminiQueryBuilder(builder);

        // 执行测试
        String sql = queryBuilder.buildQuery();

        // 打印完整SQL
        System.out.println("\n===== testBuildQueryWithGroupBy =====\n" + sql + "\n=====================\n");

        // 验证结果
        assertNotNull(sql);
        assertTrue(sql.contains("SELECT"));
        assertTrue(sql.contains("mean(\"usage\")"));
        assertTrue(sql.contains("FROM \"system.core\""));
        assertTrue(sql.contains("GROUP BY \"host\", \"region\",time(60s)"));
    }

    @Test
    public void testBuildQueryWithOrderBy() {
        // 创建查询构建器
        QueryBuilder builder = new QueryBuilder();
        builder.setDatabaseName("NzhEMjlBOTk3MzkxRjk5MjQyMzMzQzI4_infrastructure");
        builder.setMeasurement("system.core");
        builder.addAgg(new Aggregation(AggFun.MEAN, "usage"));
        builder.setOrderBy(Lists.newArrayList(new OrderBy("usage", false)));
        builder.setInterval(60);
        builder.setIntervalUnit("s");

        // 创建OpenGeminiQueryBuilder
        OpenGeminiQueryBuilder queryBuilder = new OpenGeminiQueryBuilder(builder);

        // 执行测试
        String sql = queryBuilder.buildQuery();

        // 打印完整SQL
        System.out.println("\n===== testBuildQueryWithOrderBy =====\n" + sql + "\n=====================\n");

        // 验证结果
        assertNotNull(sql);
        assertTrue(sql.contains("SELECT"));
        assertTrue(sql.contains("mean(\"usage\")"));
        assertTrue(sql.contains("FROM \"system.core\""));
        assertTrue(sql.contains("ORDER BY \"usage\" DESC"));
        assertTrue(sql.contains("GROUP BY time(60s)"));
    }

    @Test
    public void testBuildQueryWithLimit() {
        // 创建查询构建器
        QueryBuilder builder = new QueryBuilder();
        builder.setDatabaseName("NzhEMjlBOTk3MzkxRjk5MjQyMzMzQzI4_infrastructure");
        builder.setMeasurement("system.core");
        builder.addAgg(new Aggregation(AggFun.MEAN, "usage"));
        builder.setLimit(100);
        builder.setOffset(10);
        builder.setInterval(60);
        builder.setIntervalUnit("s");

        // 创建OpenGeminiQueryBuilder
        OpenGeminiQueryBuilder queryBuilder = new OpenGeminiQueryBuilder(builder);

        // 执行测试
        String sql = queryBuilder.buildQuery();

        // 打印完整SQL
        System.out.println("\n===== testBuildQueryWithLimit =====\n" + sql + "\n=====================\n");

        // 验证结果
        assertNotNull(sql);
        assertTrue(sql.contains("SELECT"));
        assertTrue(sql.contains("mean(\"usage\")"));
        assertTrue(sql.contains("FROM \"system.core\""));
        assertTrue(sql.contains("LIMIT 100"));
        assertTrue(sql.contains("OFFSET 10"));
        assertTrue(sql.contains("GROUP BY time(60s)"));
    }

    @Test
    public void testBuildShowTagValueQueryMap() {
        // 创建查询构建器
        QueryBuilder builder = new QueryBuilder();
        builder.setDatabaseName("NzhEMjlBOTk3MzkxRjk5MjQyMzMzQzI4_infrastructure");
        builder.setMeasurement("system.core");
        builder.addGroupBy("host");
        builder.addGroupBy("region");

        // 创建OpenGeminiQueryBuilder
        OpenGeminiQueryBuilder queryBuilder = new OpenGeminiQueryBuilder(builder);

        // 执行测试
        Map<String, String> queryMap = queryBuilder.buildShowTagValueQueryMap();

        // 打印完整SQL
        System.out.println("\n===== SQL Queries (ShowTagValue) =====");
        queryMap.forEach((tag, querySql) -> {
            System.out.println("Tag: " + tag + "\nSQL: " + querySql);
        });
        System.out.println("======================================\n");

        // 验证结果
        assertNotNull(queryMap);
        assertEquals(2, queryMap.size());
        assertTrue(queryMap.containsKey("host"));
        assertTrue(queryMap.containsKey("region"));
        assertTrue(queryMap.get("host").contains("SHOW TAG VALUES FROM \"system.core\" WITH KEY = \"host\""));
        assertTrue(queryMap.get("region").contains("SHOW TAG VALUES FROM \"system.core\" WITH KEY = \"region\""));
    }

    @Test
    public void testBuildDiffAggSpecificSelect() {
        // 创建查询构建器
        QueryBuilder builder = new QueryBuilder();
        builder.setDatabaseName("NzhEMjlBOTk3MzkxRjk5MjQyMzMzQzI4_infrastructure");
        builder.setMeasurement("system.core");
        builder.addAgg(new Aggregation(AggFun.LAST, "usage"));

        // 创建OpenGeminiQueryBuilder
        OpenGeminiQueryBuilder queryBuilder = new OpenGeminiQueryBuilder(builder);

        // 执行测试
        StringBuilder sql = queryBuilder.buildDiffAggSpecificSelect(AggFun.MEAN, AggFun.SUM, AggFun.MAX, new JSONObject());

        // 打印完整SQL
        System.out.println("\n===== SQL Query (DiffAgg) =====\n" + sql.toString() + "\n==============================\n");

        // 验证结果
        assertNotNull(sql);
        assertTrue(sql.toString().contains("SELECT"));
        // 验证子查询结构
        assertTrue(sql.toString().contains("max(middle_value)"));
        assertTrue(sql.toString().contains("sum(inner_value) AS middle_value"));
        assertTrue(sql.toString().contains("mean(\"usage\") AS inner_value"));
    }

    @Test
    public void testFormatInCondition() {
        // 创建查询构建器
        QueryBuilder builder = new QueryBuilder();
        builder.setDatabaseName("NzhEMjlBOTk3MzkxRjk5MjQyMzMzQzI4_infrastructure");
        builder.setMeasurement("system.core");
        builder.addAgg(new Aggregation(AggFun.MEAN, "usage"));
        builder.addWhere(new Where("host", WhereOp.IN, Arrays.asList("server01", "server02", "server03")));

        // 创建OpenGeminiQueryBuilder
        OpenGeminiQueryBuilder queryBuilder = new OpenGeminiQueryBuilder(builder);

        // 执行测试
        String sql = queryBuilder.buildQuery();

        // 验证结果
        assertNotNull(sql);
        assertTrue(sql.contains("\"host\" IN (\"server01\", \"server02\", \"server03\")"));
    }

    @Test
    public void testTimeValueConversion() {
        // 创建查询构建器
        QueryBuilder builder = new QueryBuilder();
        builder.setDatabaseName("NzhEMjlBOTk3MzkxRjk5MjQyMzMzQzI4_infrastructure");
        builder.setMeasurement("system.core");
        builder.addAgg(new Aggregation(AggFun.MEAN, "usage"));

        // 测试13位的毫秒级时间戳，应该被转换为纳秒级（乘以1000000）
        String millisTimestamp = "1625097600000";
        builder.addWhere(new Where("TIME", WhereOp.GT, millisTimestamp));

        // 创建OpenGeminiQueryBuilder
        OpenGeminiQueryBuilder queryBuilder = new OpenGeminiQueryBuilder(builder);

        // 执行测试
        String sql = queryBuilder.buildQuery();

        // 验证结果
        assertNotNull(sql);
        long expectedNanoTimestamp = Long.parseLong(millisTimestamp) * 1000000;
        assertTrue(sql.contains("TIME > " + expectedNanoTimestamp));

        // 测试非13位的数字时间戳，应该保持不变
        builder = new QueryBuilder();
        builder.setDatabaseName("NzhEMjlBOTk3MzkxRjk5MjQyMzMzQzI4_infrastructure");
        builder.setMeasurement("system.core");
        builder.addAgg(new Aggregation(AggFun.MEAN, "usage"));
        builder.addWhere(new Where("TIME", WhereOp.GT, "1625097600"));

        queryBuilder = new OpenGeminiQueryBuilder(builder);
        sql = queryBuilder.buildQuery();

        assertNotNull(sql);
        assertTrue(sql.contains("TIME > 1625097600"));

        // 测试非数字的时间表达式，应该保持不变
        builder = new QueryBuilder();
        builder.setDatabaseName("NzhEMjlBOTk3MzkxRjk5MjQyMzMzQzI4_infrastructure");
        builder.setMeasurement("system.core");
        builder.addAgg(new Aggregation(AggFun.MEAN, "usage"));
        builder.addWhere(new Where("TIME", WhereOp.GT, "now()"));

        queryBuilder = new OpenGeminiQueryBuilder(builder);
        sql = queryBuilder.buildQuery();

        // 打印完整SQL
        System.out.println("\n===== testTimeValueConversion (now) =====\n" + sql + "\n=====================\n");

        assertNotNull(sql);
        assertTrue(sql.contains("TIME > now()"));
    }

    @Test
    public void testLikeOperator() {
        // 创建查询构建器
        QueryBuilder builder = new QueryBuilder();
        builder.setDatabaseName("NzhEMjlBOTk3MzkxRjk5MjQyMzMzQzI4_infrastructure");
        builder.setMeasurement("system.core");
        builder.addAgg(new Aggregation(AggFun.MEAN, "usage"));
        builder.addWhere(new Where("host", WhereOp.LIKE, "server"));

        // 创建OpenGeminiQueryBuilder
        OpenGeminiQueryBuilder queryBuilder = new OpenGeminiQueryBuilder(builder);

        // 执行测试
        String sql = queryBuilder.buildQuery();

        // 打印完整SQL
        System.out.println("\n===== testLikeOperator =====\n" + sql + "\n=====================\n");

        // 验证结果
        assertNotNull(sql);
        assertTrue(sql.contains("\"host\"::tag =~ /.*server.*/"));
    }

    @Test
    public void testNotLikeOperator() {
        // 创建查询构建器
        QueryBuilder builder = new QueryBuilder();
        builder.setDatabaseName("NzhEMjlBOTk3MzkxRjk5MjQyMzMzQzI4_infrastructure");
        builder.setMeasurement("system.core");
        builder.addAgg(new Aggregation(AggFun.MEAN, "usage"));
        builder.addWhere(new Where("host", WhereOp.NOT_LIKE, "server"));

        // 创建OpenGeminiQueryBuilder
        OpenGeminiQueryBuilder queryBuilder = new OpenGeminiQueryBuilder(builder);

        // 执行测试
        String sql = queryBuilder.buildQuery();

        // 打印完整SQL
        System.out.println("\n===== testNotLikeOperator =====\n" + sql + "\n=====================\n");

        // 验证结果
        assertNotNull(sql);
        assertTrue(sql.contains("\"host\" !~ /.*server.*/"));
    }

    @Test
    public void testStartWithOperator() {
        // 创建查询构建器
        QueryBuilder builder = new QueryBuilder();
        builder.setDatabaseName("NzhEMjlBOTk3MzkxRjk5MjQyMzMzQzI4_infrastructure");
        builder.setMeasurement("system.core");
        builder.addAgg(new Aggregation(AggFun.MEAN, "usage"));
        builder.addWhere(new Where("host", WhereOp.START_WITH, "server"));

        // 创建OpenGeminiQueryBuilder
        OpenGeminiQueryBuilder queryBuilder = new OpenGeminiQueryBuilder(builder);

        // 执行测试
        String sql = queryBuilder.buildQuery();

        // 打印完整SQL
        System.out.println("\n===== testStartWithOperator =====\n" + sql + "\n=====================\n");

        // 验证结果
        assertNotNull(sql);
        assertTrue(sql.contains("\"host\" =~ /^server.*/"));
    }

    @Test
    public void testEndWithOperator() {
        // 创建查询构建器
        QueryBuilder builder = new QueryBuilder();
        builder.setDatabaseName("NzhEMjlBOTk3MzkxRjk5MjQyMzMzQzI4_infrastructure");
        builder.setMeasurement("system.core");
        builder.addAgg(new Aggregation(AggFun.MEAN, "usage"));
        builder.addWhere(new Where("host", WhereOp.END_WITH, "01"));

        // 创建OpenGeminiQueryBuilder
        OpenGeminiQueryBuilder queryBuilder = new OpenGeminiQueryBuilder(builder);

        // 执行测试
        String sql = queryBuilder.buildQuery();

        // 打印完整SQL
        System.out.println("\n===== testEndWithOperator =====\n" + sql + "\n=====================\n");

        // 验证结果
        assertNotNull(sql);
        assertTrue(sql.contains("\"host\" =~ /.*01$/"));
    }

    @Test
    public void testRegexOperator() {
        // 创建查询构建器
        QueryBuilder builder = new QueryBuilder();
        builder.setDatabaseName("NzhEMjlBOTk3MzkxRjk5MjQyMzMzQzI4_infrastructure");
        builder.setMeasurement("system.core");
        builder.addAgg(new Aggregation(AggFun.MEAN, "usage"));
        builder.addWhere(new Where("host", WhereOp.REGEX, "server[0-9]+"));

        // 创建OpenGeminiQueryBuilder
        OpenGeminiQueryBuilder queryBuilder = new OpenGeminiQueryBuilder(builder);

        // 执行测试
        String sql = queryBuilder.buildQuery();

        // 打印完整SQL
        System.out.println("\n===== testRegexOperator =====\n" + sql + "\n=====================\n");

        // 验证结果
        assertNotNull(sql);
        assertTrue(sql.contains("\"host\" =~ /server[0-9]+/"));
    }

    @Test
    public void testIsNullOperator() {
        // 创建查询构建器
        QueryBuilder builder = new QueryBuilder();
        builder.setDatabaseName("NzhEMjlBOTk3MzkxRjk5MjQyMzMzQzI4_infrastructure");
        builder.setMeasurement("system.core");
        builder.addAgg(new Aggregation(AggFun.MEAN, "usage"));
        builder.addWhere(new Where("host", WhereOp.IS));

        // 创建OpenGeminiQueryBuilder
        OpenGeminiQueryBuilder queryBuilder = new OpenGeminiQueryBuilder(builder);

        // 执行测试
        String sql = queryBuilder.buildQuery();

        // 打印完整SQL
        System.out.println("\n===== testIsNullOperator =====\n" + sql + "\n=====================\n");

        // 验证结果
        assertNotNull(sql);
        assertTrue(sql.contains("\"host\" IS NULL"));
    }

    @Test
    public void testIsNotNullOperator() {
        // 创建查询构建器
        QueryBuilder builder = new QueryBuilder();
        builder.setDatabaseName("NzhEMjlBOTk3MzkxRjk5MjQyMzMzQzI4_infrastructure");
        builder.setMeasurement("system.core");
        builder.addAgg(new Aggregation(AggFun.MEAN, "usage"));
        builder.addWhere(new Where("host", WhereOp.IS_NOT));

        // 创建OpenGeminiQueryBuilder
        OpenGeminiQueryBuilder queryBuilder = new OpenGeminiQueryBuilder(builder);

        // 执行测试
        String sql = queryBuilder.buildQuery();

        // 打印完整SQL
        System.out.println("\n===== testIsNotNullOperator =====\n" + sql + "\n=====================\n");

        // 验证结果
        assertNotNull(sql);
        assertTrue(sql.contains("\"host\" IS NOT NULL"));
    }

    @Test
    public void testInOperatorWithNumbers() {
        // 创建查询构建器
        QueryBuilder builder = new QueryBuilder();
        builder.setDatabaseName("NzhEMjlBOTk3MzkxRjk5MjQyMzMzQzI4_infrastructure");
        builder.setMeasurement("system.core");
        builder.addAgg(new Aggregation(AggFun.MEAN, "usage"));
        builder.addWhere(new Where("value", WhereOp.IN, Arrays.asList(10, 20, 30)));

        // 创建OpenGeminiQueryBuilder
        OpenGeminiQueryBuilder queryBuilder = new OpenGeminiQueryBuilder(builder);

        // 执行测试
        String sql = queryBuilder.buildQuery();

        // 打印完整SQL
        System.out.println("\n===== testInOperatorWithNumbers =====\n" + sql + "\n=====================\n");

        // 验证结果
        assertNotNull(sql);
        assertTrue(sql.contains("\"value\" IN ('10', '20', '30')"));
    }

    @Test
    public void testNotInOperator() {
        // 创建查询构建器
        QueryBuilder builder = new QueryBuilder();
        builder.setDatabaseName("NzhEMjlBOTk3MzkxRjk5MjQyMzMzQzI4_infrastructure");
        builder.setMeasurement("system.core");
        builder.addAgg(new Aggregation(AggFun.MEAN, "usage"));
        builder.addWhere(new Where("host", WhereOp.NOT_IN, Arrays.asList("server01", "server02")));

        // 创建OpenGeminiQueryBuilder
        OpenGeminiQueryBuilder queryBuilder = new OpenGeminiQueryBuilder(builder);

        // 执行测试
        String sql = queryBuilder.buildQuery();

        // 打印完整SQL
        System.out.println("\n===== testNotInOperator =====\n" + sql + "\n=====================\n");

        // 验证结果
        assertNotNull(sql);
        assertTrue(sql.contains("(\"host\" != 'server01' AND \"host\" != 'server02')"));
    }

    @Test
    public void testMixedConditions() {
        // 创建查询构建器
        QueryBuilder builder = new QueryBuilder();
        builder.setDatabaseName("NzhEMjlBOTk3MzkxRjk5MjQyMzMzQzI4_infrastructure");
        builder.setMeasurement("system.core");
        builder.addAgg(new Aggregation(AggFun.MEAN, "usage"));
        builder.addWhere(new Where("host", WhereOp.LIKE, "server"));
        builder.addWhere(new Where("value", WhereOp.GT, 50));
        builder.addWhere(new Where("region", WhereOp.IN, Arrays.asList("east", "west")));
        builder.setLimit(100);
        builder.setOrderBy(Lists.newArrayList(new OrderBy("time", false)));

        // 创建OpenGeminiQueryBuilder
        OpenGeminiQueryBuilder queryBuilder = new OpenGeminiQueryBuilder(builder);

        // 执行测试
        String sql = queryBuilder.buildQuery();

        // 打印完整SQL
        System.out.println("\n===== testMixedConditions =====\n" + sql + "\n=====================\n");

        // 验证结果
        assertNotNull(sql);
        assertTrue(sql.contains("\"host\" =~ /.*server.*/"));
        assertTrue(sql.contains("\"value\" > 50"));
        assertTrue(sql.contains("\"region\" IN (\"east\", \"west\")"));
        assertTrue(sql.contains("LIMIT 100"));
        assertTrue(sql.contains("ORDER BY \"time\" DESC"));
    }

    /**
     * 测试各种字段名的格式化，确保单独的字段名被双引号包裹，而公式或聚合函数不被包裹
     */

    @Test
    public void testTagWithNumericValue() {
        // 创建查询构建器
        QueryBuilder builder = new QueryBuilder();
        builder.setDatabaseName("NzhEMjlBOTk3MzkxRjk5MjQyMzMzQzI4_apm_metric");
        builder.setMeasurement("service.other");
        builder.addAgg(new Aggregation(AggFun.SUM, "cnt", "allCnt"));
        builder.addWhere(new Where("serviceId", WhereOp.EQ, "5b202a7c4f8a9424"));
        builder.addWhere(new Where("isIn", WhereOp.EQ, 1));

        // 创建OpenGeminiQueryBuilder
        OpenGeminiQueryBuilder queryBuilder = new OpenGeminiQueryBuilder(builder);

        // 执行测试
        String sql = queryBuilder.buildQuery();

        // 打印完整SQL
        System.out.println("\n===== testTagWithNumericValue =====\n" + sql + "\n=====================\n");

        // 验证结果
        assertNotNull(sql);
        assertTrue(sql.contains("\"serviceId\"::tag = '5b202a7c4f8a9424'"));
        assertTrue(sql.contains("\"isIn\"::tag = '1'"));
    }

    @Test
    public void testTopNestedQuery() {
        // 创建查询构建器
        QueryBuilder builder = new QueryBuilder();
        builder.setDatabaseName("NzhEMjlBOTk3MzkxRjk5MjQyMzMzQzI4_infrastructure");
        builder.setMeasurement("service");
        builder.addAgg(new Aggregation(AggFun.SUM, "cnt", "callCnt"));
        builder.addAgg(new Aggregation(null, "sum(cnt) / 3600", "reqRate"));
        builder.addAgg(new Aggregation(AggFun.SUM, "error", "errCnt"));
        builder.addAgg(new Aggregation(null, "sum(error) / sum(cnt)", "errRate"));
        builder.addAgg(new Aggregation(null, "sum(sumDuration) / sum(cnt)", "avgLatency"));
        builder.addAgg(new Aggregation(AggFun.MEAN, "apdex", "apdex"));
        builder.addGroupBy("serviceId");
        builder.setOrderBy(Lists.newArrayList(new OrderBy("callCnt", false))); // 降序，使用top函数
        builder.setLimit(5);
        builder.addWhere(new Where("time", WhereOp.GTE, 1745800020000000000L));
        builder.addWhere(new Where("time", WhereOp.LT, 1745803620000000000L));

        // 创建OpenGeminiQueryBuilder
        OpenGeminiQueryBuilder queryBuilder = new OpenGeminiQueryBuilder(builder);

        // 执行测试
        String sql = queryBuilder.buildQuery();

        // 打印完整SQL
        System.out.println("\n===== testTopNestedQuery =====\n" + sql + "\n=====================\n");

        // 验证结果
        assertNotNull(sql);
        assertTrue("SQL应该包含top函数", sql.contains("top(\"callCnt\", 5)"));
        assertTrue("SQL应该是嵌套查询", sql.contains("FROM ("));
        assertTrue("SQL应该包含内层查询的聚合函数", sql.contains("sum(\"cnt\") AS \"callCnt\""));
        assertTrue("SQL应该包含内层查询的时间条件", sql.contains("time >= 1745800020000000000"));
        assertTrue("SQL应该包含外层查询的时间条件", sql.contains("WHERE time >= 1745800020000000000"));
    }

    @Test
    public void testBottomNestedQuery() {
        // 创建查询构建器
        QueryBuilder builder = new QueryBuilder();
        builder.setDatabaseName("NzhEMjlBOTk3MzkxRjk5MjQyMzMzQzI4_infrastructure");
        builder.setMeasurement("service");
        builder.addAgg(new Aggregation(AggFun.SUM, "cnt", "callCnt"));
        builder.addAgg(new Aggregation(null, "sum(cnt) / 3600", "reqRate"));
        builder.addAgg(new Aggregation(AggFun.SUM, "error", "errCnt"));
        builder.addAgg(new Aggregation(null, "sum(error) / sum(cnt)", "errRate"));
        builder.addAgg(new Aggregation(null, "sum(sumDuration) / sum(cnt)", "avgLatency"));
        builder.addAgg(new Aggregation(AggFun.MEAN, "apdex", "apdex"));
        builder.addGroupBy("serviceId");
        builder.setOrderBy(Lists.newArrayList(new OrderBy("callCnt", true))); // 升序，使用bottom函数
        builder.setLimit(5);
        builder.addWhere(new Where("time", WhereOp.GTE, 1745800020000000000L));
        builder.addWhere(new Where("time", WhereOp.LT, 1745803620000000000L));

        // 创建OpenGeminiQueryBuilder
        OpenGeminiQueryBuilder queryBuilder = new OpenGeminiQueryBuilder(builder);

        // 执行测试
        String sql = queryBuilder.buildQuery();

        // 打印完整SQL
        System.out.println("\n===== testBottomNestedQuery =====\n" + sql + "\n=====================\n");

        // 验证结果
        assertNotNull(sql);
        assertTrue("SQL应该包含bottom函数", sql.contains("bottom(\"callCnt\", 5)"));
        assertTrue("SQL应该是嵌套查询", sql.contains("FROM ("));
        assertTrue("SQL应该包含内层查询的聚合函数", sql.contains("sum(\"cnt\") AS \"callCnt\""));
        assertTrue("SQL应该包含内层查询的时间条件", sql.contains("time >= 1745800020000000000"));
        assertTrue("SQL应该包含外层查询的时间条件", sql.contains("WHERE time >= 1745800020000000000"));
    }

    @Test
    public void testTimeFieldOrderBy() {
        // 创建查询构建器
        QueryBuilder builder = new QueryBuilder();
        builder.setDatabaseName("NzhEMjlBOTk3MzkxRjk5MjQyMzMzQzI4_infrastructure");
        builder.setMeasurement("service");
        builder.addAgg(new Aggregation(AggFun.SUM, "cnt", "callCnt"));
        builder.addAgg(new Aggregation(AggFun.SUM, "error", "errCnt"));
        builder.addGroupBy("serviceId");
        builder.setOrderBy(Lists.newArrayList(new OrderBy("time", false))); // time字段降序
        builder.setLimit(5);
        builder.addWhere(new Where("time", WhereOp.GTE, 1745800020000000000L));
        builder.addWhere(new Where("time", WhereOp.LT, 1745803620000000000L));

        // 创建OpenGeminiQueryBuilder
        OpenGeminiQueryBuilder queryBuilder = new OpenGeminiQueryBuilder(builder);

        // 执行测试
        String sql = queryBuilder.buildQuery();

        // 打印完整SQL
        System.out.println("\n===== testTimeFieldOrderBy =====\n" + sql + "\n=====================\n");

        // 验证结果
        assertNotNull(sql);
        // 应该使用正常的ORDER BY语法，而不是top/bottom函数
        assertTrue("SQL应该使用正常的ORDER BY语法", sql.contains("ORDER BY \"time\" DESC"));
        assertFalse("SQL不应该包含top函数", sql.contains("top(\"time\""));
        assertFalse("SQL不应该包含bottom函数", sql.contains("bottom(\"time\""));
        assertFalse("SQL不应该是嵌套查询", sql.contains("FROM (SELECT"));
    }

    @Test
    @DisplayName("测试当排序字段同时包含time和非time字段时使用两次查询方法")
    public void testTimeAndNonTimeFieldOrderByWithTwoQueries() {
        // 创建查询构建器
        QueryBuilder builder = new QueryBuilder();
        builder.setDatabaseName("NzhEMjlBOTk3MzkxRjk5MjQyMzMzQzI4_infrastructure");
        builder.setMeasurement("service.http");
        builder.addAgg(new Aggregation(AggFun.SUM, "cnt", "callCnt"));
        builder.addAgg(new Aggregation(AggFun.SUM, "slow", "slowCnt"));
        builder.addAgg(new Aggregation(AggFun.SUM, "sumDuration", "totalTime"));
        builder.addAgg(new Aggregation(null, "sum(cnt) / 3600", "reqRate"));
        builder.addAgg(new Aggregation(AggFun.SUM, "error", "errCnt"));
        builder.addAgg(new Aggregation(null, "sum(error) / sum(cnt)", "errRate"));
        builder.addAgg(new Aggregation(null, "sum(slow) / sum(cnt)", "slowRate"));
        builder.addAgg(new Aggregation(null, "sum(sumDuration) / sum(cnt)", "avgLatency"));

        // 添加分组字段
        builder.addGroupBy("serviceId");
        builder.addGroupBy("resource");
        builder.addGroupBy("httpMethod");

        // 添加排序字段，包含time和非time字段
        List<OrderBy> orderByList = new ArrayList<>();
        orderByList.add(new OrderBy("callCnt", false)); // 非time字段，降序
        orderByList.add(new OrderBy("time", true));     // time字段，升序
        builder.setOrderBy(orderByList);

        // 设置查询条件
        builder.setLimit(5);
        builder.addWhere(new Where("time", WhereOp.GTE, 1746491280000000000L));
        builder.addWhere(new Where("time", WhereOp.LT, 1746494880000000000L));
        builder.addWhere(new Where("isIn", WhereOp.EQ, 1));

        // 创建OpenGeminiQueryBuilder
        OpenGeminiQueryBuilder queryBuilder = new OpenGeminiQueryBuilder(builder);

        // 执行测试
        String sql = queryBuilder.buildQuery();

        // 打印完整SQL
        System.out.println("\n===== testTimeAndNonTimeFieldOrderByWithTwoQueries =====\n" + sql + "\n=====================\n");

        // 验证结果
        assertNotNull(sql);

        // 验证第一次查询的结构
        assertTrue("SQL应该包含top函数获取tag值", sql.contains("top(\"callCnt\", 5) AS \"callCnt\", *::tag"));
        assertTrue("SQL应该包含子查询", sql.contains("FROM (SELECT"));
        assertTrue("SQL应该包含sum(\"cnt\") AS \"callCnt\"", sql.contains("sum(\"cnt\") AS \"callCnt\""));

        // 打印期望的SQL格式供参考
        System.out.println("\n===== 期望的SQL格式 =====\n" +
                "SELECT\n" +
                "\ttop(\"callCnt\", 5) AS \"callCnt\",\n" +
                "\t*::tag\n" +
                "FROM\n" +
                "\t(\n" +
                "\tSELECT\n" +
                "\t\tsum(\"cnt\") AS \"callCnt\"\n" +
                "\tFROM\n" +
                "\t\t\"service.http\"\n" +
                "\tWHERE\n" +
                "\t\ttime >= 1746491280000000000\n" +
                "\t\tAND time < 1746494880000000000\n" +
                "\t\tAND \"isIn\"::tag = '1'\n" +
                "\tGROUP BY\n" +
                "\t\t\"serviceId\",\n" +
                "\t\t\"resource\",\n" +
                "\t\t\"httpMethod\")\n" +
                "\n=====================\n");

        // 验证查询条件
        assertTrue("SQL应该包含时间范围条件", sql.contains("time >= 1746491280000000000"));
        assertTrue("SQL应该包含时间范围条件", sql.contains("time < 1746494880000000000"));
        assertTrue("SQL应该包含isIn条件", sql.contains("\"isIn\"::tag = '1'"));

        // 验证分组条件
        assertTrue("SQL应该包含GROUP BY语句", sql.contains("GROUP BY \"serviceId\", \"resource\", \"httpMethod\""));
    }

    @Test
    public void testMultipleOrderByFields() {
        // 创建查询构建器
        QueryBuilder builder = new QueryBuilder();
        builder.setOffset(1);
        builder.setLimit(10);
        builder.setDatabaseName("NzhEMjlBOTk3MzkxRjk5MjQyMzMzQzI4_infrastructure");
        builder.setMeasurement("service");
        builder.addAgg(new Aggregation(AggFun.SUM, "cnt", "callCnt"));
        builder.addAgg(new Aggregation(null, "sum(cnt) / 3600", "reqRate"));
        builder.addAgg(new Aggregation(AggFun.SUM, "error", "errCnt"));
        builder.addGroupBy("serviceId");

        // 添加多个排序字段，包含不同的排序方向
        List<OrderBy> orderByList = new ArrayList<>();
        orderByList.add(new OrderBy("callCnt", false)); // 降序，使用top函数
        orderByList.add(new OrderBy("reqRate", true));  // 升序，使用bottom函数
        builder.setOrderBy(orderByList);

        builder.setLimit(5);
        builder.addWhere(new Where("time", WhereOp.GTE, 1745800020000000000L));
        builder.addWhere(new Where("time", WhereOp.LT, 1745803620000000000L));

        // 创建OpenGeminiQueryBuilder
        OpenGeminiQueryBuilder queryBuilder = new OpenGeminiQueryBuilder(builder);

        // 执行测试
        String sql = queryBuilder.buildQuery();

        // 打印完整SQL
        System.out.println("\n===== testMultipleOrderByFields =====\n" + sql + "\n=====================\n");

        // 验证结果
        assertNotNull(sql);
        // 应该是两层嵌套查询
        assertTrue("SQL应该包含top函数", sql.contains("top(\"callCnt\", 5)"));
        assertTrue("SQL应该包含bottom函数", sql.contains("bottom(\"reqRate\", 5)"));

        // 验证嵌套层次，应该是从内到外的顺序：reqRate(内层) -> callCnt(外层)
        int reqRateIndex = sql.indexOf("bottom(\"reqRate\"");
        int callCntIndex = sql.indexOf("top(\"callCnt\"");
        assertTrue("reqRate应该在内层，callCnt应该在外层", reqRateIndex > callCntIndex);

        // 验证每一层都有时间范围条件
        int whereCount = countOccurrences(sql, "WHERE time >=");
        assertEquals("SQL应该在每一层都有时间范围条件", 3, whereCount);
    }

    @Test
    public void testMixedTimeAndNonTimeOrderBy() {
        // 创建查询构建器
        QueryBuilder builder = new QueryBuilder();
        builder.setDatabaseName("NzhEMjlBOTk3MzkxRjk5MjQyMzMzQzI4_infrastructure");
        builder.setMeasurement("service");
        builder.addAgg(new Aggregation(AggFun.SUM, "cnt", "callCnt"));
        builder.addAgg(new Aggregation(AggFun.SUM, "error", "errCnt"));
        builder.addGroupBy("serviceId");

        // 添加混合的time和非time排序字段
        List<OrderBy> orderByList = new ArrayList<>();
        orderByList.add(new OrderBy("callCnt", false)); // 非time字段，降序
        orderByList.add(new OrderBy("time", true));     // time字段，升序
        builder.setOrderBy(orderByList);

        builder.setLimit(5);
        builder.addWhere(new Where("time", WhereOp.GTE, 1745800020000000000L));
        builder.addWhere(new Where("time", WhereOp.LT, 1745803620000000000L));

        // 创建OpenGeminiQueryBuilder
        OpenGeminiQueryBuilder queryBuilder = new OpenGeminiQueryBuilder(builder);

        // 执行测试
        String sql = queryBuilder.buildQuery();

        // 打印完整SQL
        System.out.println("\n===== testMixedTimeAndNonTimeOrderBy =====\n" + sql + "\n=====================\n");

        // 验证结果
        assertNotNull(sql);
        // 应该先构建非time字段的嵌套查询，然后在最外层添加time排序
        assertTrue("SQL应该包含top函数", sql.contains("top(\"callCnt\", 5)"));
        assertTrue("SQL应该在最外层有ORDER BY time", sql.contains("ORDER BY \"time\" ASC"));

        // 验证最外层是包含全部嵌套查询的SELECT
        assertTrue("SQL应该在最外层有SELECT * FROM", sql.contains("SELECT * FROM ("));

        // 验证最外层有GROUP BY语句
        assertTrue("SQL应该在最外层有GROUP BY语句", sql.contains("GROUP BY \"serviceId\" ORDER BY"));

        // 验证每一层都有时间范围条件
        int whereCount = countOccurrences(sql, "WHERE time >=");
        assertEquals("SQL应该在每一层都有时间范围条件", 3, whereCount);
    }

    @Test
    public void testOuterGroupByWithTopFunction() {
        // 创建查询构建器
        QueryBuilder builder = new QueryBuilder();
        builder.setDatabaseName("NzhEMjlBOTk3MzkxRjk5MjQyMzMzQzI4_infrastructure");
        builder.setMeasurement("service");
        builder.addAgg(new Aggregation(AggFun.SUM, "cnt", "callCnt"));
        builder.addAgg(new Aggregation(null, "sum(cnt) / 86400", "reqRate"));
        builder.addAgg(new Aggregation(AggFun.SUM, "error", "errCnt"));
        builder.addAgg(new Aggregation(null, "sum(error) / sum(cnt)", "errRate"));
        builder.addAgg(new Aggregation(null, "sum(sumDuration) / sum(cnt)", "avgLatency"));
        builder.addAgg(new Aggregation(AggFun.MEAN, "apdex", "apdex"));
        builder.addGroupBy("serviceId");
        builder.setOrderBy(Lists.newArrayList(new OrderBy("callCnt", false))); // 降序，使用top函数
        builder.setLimit(50);
        builder.addWhere(new Where("serviceId", WhereOp.IN, Arrays.asList("11219c01edcaa370")));
        builder.addWhere(new Where("time", WhereOp.GTE, 1745718360000000000L));
        builder.addWhere(new Where("time", WhereOp.LT, 1745804760000000000L));

        // 创建OpenGeminiQueryBuilder
        OpenGeminiQueryBuilder queryBuilder = new OpenGeminiQueryBuilder(builder);

        // 执行测试
        String sql = queryBuilder.buildQuery();

        // 打印完整SQL
        System.out.println("\n===== testOuterGroupByWithTopFunction =====\n" + sql + "\n=====================\n");

        // 验证结果
        assertNotNull(sql);
        assertTrue("SQL应该包含top函数", sql.contains("top(\"callCnt\", 50)"));
        assertTrue("SQL应该包含内层GROUP BY", sql.contains("GROUP BY \"serviceId\""));

        // 验证最外层有GROUP BY语句
        int groupByCount = countOccurrences(sql, "GROUP BY \"serviceId\"");
        assertEquals("SQL应该在内层和外层都有GROUP BY语句", 2, groupByCount);

        // 验证每一层都有时间范围条件
        int whereCount = countOccurrences(sql, "WHERE");
        assertEquals("SQL应该在每一层都有WHERE条件", 2, whereCount);
    }

    // 计算字符串中指定子字符串的出现次数
    private int countOccurrences(String str, String subStr) {
        int count = 0;
        int index = 0;
        while ((index = str.indexOf(subStr, index)) != -1) {
            count++;
            index += subStr.length();
        }
        return count;
    }

    @Test
    public void testPaginationWithMultipleOrderBy() {
        // 创建查询构建器
        QueryBuilder builder = new QueryBuilder();
        builder.setDatabaseName("NzhEMjlBOTk3MzkxRjk5MjQyMzMzQzI4_infrastructure");
        builder.setMeasurement("service");
        builder.addAgg(new Aggregation(AggFun.SUM, "cnt", "callCnt"));
        builder.addAgg(new Aggregation(null, "sum(cnt) / 3600", "reqRate"));
        builder.addAgg(new Aggregation(AggFun.SUM, "error", "errCnt"));
        builder.addGroupBy("serviceId");

        // 添加多个排序字段，包含不同的排序方向
        List<OrderBy> orderByList = new ArrayList<>();
        orderByList.add(new OrderBy("callCnt", false)); // 降序，使用top函数
        orderByList.add(new OrderBy("reqRate", true));  // 升序，使用bottom函数
        builder.setOrderBy(orderByList);

        // 设置分页参数
        builder.setLimit(5);
        builder.setOffset(1);

        builder.addWhere(new Where("time", WhereOp.GTE, 1745800020000000000L));
        builder.addWhere(new Where("time", WhereOp.LT, 1745803620000000000L));

        // 创建OpenGeminiQueryBuilder
        OpenGeminiQueryBuilder queryBuilder = new OpenGeminiQueryBuilder(builder);

        // 执行测试
        String sql = queryBuilder.buildQuery();

        // 打印完整SQL
        System.out.println("\n===== testPaginationWithMultipleOrderBy =====\n" + sql + "\n=====================\n");

        // 验证结果
        assertNotNull(sql);
        // 验证SQL包含分页参数
        assertTrue("SQL应该包含LIMIT", sql.contains("LIMIT 5"));
        assertTrue("SQL应该包含OFFSET", sql.contains("OFFSET 1"));

        // 验证分页参数应用在最外层查询
        int lastSelectIndex = sql.lastIndexOf("SELECT");
        int limitIndex = sql.indexOf("LIMIT");
        int groupByIndex = sql.lastIndexOf("GROUP BY");

        // 确保LIMIT在最后一个SELECT之后
        assertTrue("LIMIT应该在最后一个SELECT之后", limitIndex > lastSelectIndex);

        // 确保GROUP BY在LIMIT之前
        assertTrue("GROUP BY应该在LIMIT之前", groupByIndex < limitIndex);
    }

    @Test
    @DisplayName("测试非time字段排序的查询带分页参数")
    public void testNonTimeOrderQueryWithPagination() {
        // 创建查询构建器
        QueryBuilder builder = new QueryBuilder();
        builder.setDatabaseName("NzhEMjlBOTk3MzkxRjk5MjQyMzMzQzI4_infrastructure");
        builder.setMeasurement("service.http");
        builder.addAgg(new Aggregation(AggFun.SUM, "cnt", "callCnt"));
        builder.addAgg(new Aggregation(AggFun.SUM, "slow", "slowCnt"));
        builder.addAgg(new Aggregation(AggFun.SUM, "sumDuration", "totalTime"));
        builder.addAgg(new Aggregation(null, "sum(cnt) / 7200", "reqRate"));
        builder.addAgg(new Aggregation(AggFun.SUM, "error", "errCnt"));
        builder.addAgg(new Aggregation(null, "sum(error) / sum(cnt)", "errRate"));
        builder.addAgg(new Aggregation(null, "sum(slow) / sum(cnt)", "slowRate"));
        builder.addAgg(new Aggregation(null, "sum(sumDuration) / sum(cnt)", "avgLatency"));

        // 添加分组字段
        builder.addGroupBy("serviceId");
        builder.addGroupBy("resource");
        builder.addGroupBy("httpMethod");

        // 添加排序字段
        builder.setOrderBy(Lists.newArrayList(new OrderBy("callCnt", false))); // 降序，使用top函数

        // 设置分页参数
        builder.setLimit(50);
        builder.setOffset(50);

        // 设置时间范围
        builder.addWhere(new Where("time", WhereOp.GTE, 1746515220000000000L));
        builder.addWhere(new Where("time", WhereOp.LT, 1746522420000000000L));
        builder.addWhere(new Where("isIn", WhereOp.EQ, 1));

        // 创建OpenGeminiQueryBuilder
        OpenGeminiQueryBuilder queryBuilder = new OpenGeminiQueryBuilder(builder);

        // 执行测试
        String sql = queryBuilder.buildQuery();

        // 打印完整SQL
        System.out.println("\n===== testNonTimeOrderQueryWithPagination =====\n" + sql + "\n=====================\n");

        // 验证结果
        assertNotNull(sql);

        // 验证SQL包含top函数和分页参数
        int expectedTopLimit = 50 + 50; // limit + offset
        assertTrue("SQL应该包含top函数", sql.contains("top(\"callCnt\", " + expectedTopLimit + ")"));
        assertTrue("SQL应该包含LIMIT", sql.contains("LIMIT 50"));
        assertTrue("SQL应该包含OFFSET", sql.contains("OFFSET 50"));

        // 验证SQL结构是三层嵌套查询
        int fromCount = countOccurrences(sql, "FROM (");
        assertEquals("SQL应该有两层嵌套查询", 2, fromCount);

        // 验证最外层查询包含分页参数
        int lastSelectIndex = sql.lastIndexOf("SELECT");
        int limitIndex = sql.indexOf("LIMIT");
        assertTrue("LIMIT应该在最后一个SELECT之后", limitIndex > lastSelectIndex);

        // 验证时间条件在每一层都存在
        int whereTimeCount = countOccurrences(sql, "time >= 1746515220000000000");
        assertEquals("时间条件应该在每一层都存在", 2, whereTimeCount);
    }

    @Test
    public void testFieldNameFormatting() {
        // 测试用例1: 单独字段名 - error AS "errorCnt"
        QueryBuilder builder1 = new QueryBuilder();
        builder1.setDatabaseName("NzhEMjlBOTk3MzkxRjk5MjQyMzMzQzI4_apm_metric");
        builder1.setMeasurement("business.service");
        builder1.addAgg(new Aggregation(null, "error", "errorCnt"));
        builder1.addGroupBy("errorType");
        builder1.setInterval(120);
        builder1.setIntervalUnit("s");
        OpenGeminiQueryBuilder queryBuilder1 = new OpenGeminiQueryBuilder(builder1);
        String sql1 = queryBuilder1.buildQuery();
        System.out.println("\n===== Test Case 1: Simple Field =====\n" + sql1 + "\n=====================\n");
        assertTrue(sql1.contains("SELECT \"error\" AS \"errorCnt\" FROM"));

        // 测试用例2: 单独字段名 - name AS "errorCnt"
        QueryBuilder builder2 = new QueryBuilder();
        builder2.setDatabaseName("NzhEMjlBOTk3MzkxRjk5MjQyMzMzQzI4_apm_metric");
        builder2.setMeasurement("business.service");
        builder2.addAgg(new Aggregation(null, "name", "errorCnt"));
        builder2.addGroupBy("errorType");
        builder2.setInterval(120);
        builder2.setIntervalUnit("s");
        OpenGeminiQueryBuilder queryBuilder2 = new OpenGeminiQueryBuilder(builder2);
        String sql2 = queryBuilder2.buildQuery();
        System.out.println("\n===== Test Case 2: Simple Field =====\n" + sql2 + "\n=====================\n");
        assertTrue(sql2.contains("SELECT \"name\" AS \"errorCnt\" FROM"));

        // 测试用例3: 公式 - sum(a)/sum(b) AS "errorCnt"
        QueryBuilder builder3 = new QueryBuilder();
        builder3.setDatabaseName("NzhEMjlBOTk3MzkxRjk5MjQyMzMzQzI4_apm_metric");
        builder3.setMeasurement("business.service");
        builder3.addAgg(new Aggregation(null, "sum(a)/sum(b)", "errorCnt"));
        builder3.addGroupBy("errorType");
        builder3.setInterval(120);
        builder3.setIntervalUnit("s");
        OpenGeminiQueryBuilder queryBuilder3 = new OpenGeminiQueryBuilder(builder3);
        String sql3 = queryBuilder3.buildQuery();
        System.out.println("\n===== Test Case 3: Formula =====\n" + sql3 + "\n=====================\n");
        assertTrue(sql3.contains("SELECT sum(a)/sum(b) AS \"errorCnt\" FROM"));

        // 测试用例4: 复杂公式 - 1+sum(a)/sum(b)+1 AS "errorCnt"
        QueryBuilder builder4 = new QueryBuilder();
        builder4.setDatabaseName("NzhEMjlBOTk3MzkxRjk5MjQyMzMzQzI4_apm_metric");
        builder4.setMeasurement("business.service");
        builder4.addAgg(new Aggregation(null, "1+sum(a)/sum(b)+1", "errorCnt"));
        builder4.addGroupBy("errorType");
        builder4.setInterval(120);
        builder4.setIntervalUnit("s");
        OpenGeminiQueryBuilder queryBuilder4 = new OpenGeminiQueryBuilder(builder4);
        String sql4 = queryBuilder4.buildQuery();
        System.out.println("\n===== Test Case 4: Complex Formula =====\n" + sql4 + "\n=====================\n");
        assertTrue(sql4.contains("SELECT 1+sum(a)/sum(b)+1 AS \"errorCnt\" FROM"));

        // 测试用例5: 包含特殊运算符的公式 - sum(a)+-*/sum(b) AS "errorCnt"
        QueryBuilder builder5 = new QueryBuilder();
        builder5.setDatabaseName("NzhEMjlBOTk3MzkxRjk5MjQyMzMzQzI4_apm_metric");
        builder5.setMeasurement("business.service");
        builder5.addAgg(new Aggregation(null, "sum(a)+-*/sum(b)", "errorCnt"));
        builder5.addGroupBy("errorType");
        builder5.setInterval(120);
        builder5.setIntervalUnit("s");
        OpenGeminiQueryBuilder queryBuilder5 = new OpenGeminiQueryBuilder(builder5);
        String sql5 = queryBuilder5.buildQuery();
        System.out.println("\n===== Test Case 5: Formula with Special Operators =====\n" + sql5 + "\n=====================\n");
        assertTrue(sql5.contains("SELECT sum(a)+-*/sum(b) AS \"errorCnt\" FROM"));

        // 测试用例6: 聚合函数 - sum(a) AS "errorCnt"
        QueryBuilder builder6 = new QueryBuilder();
        builder6.setDatabaseName("NzhEMjlBOTk3MzkxRjk5MjQyMzMzQzI4_apm_metric");
        builder6.setMeasurement("business.service");
        builder6.addAgg(new Aggregation(AggFun.SUM, "a", "errorCnt"));
        builder6.addGroupBy("errorType");
        builder6.setInterval(120);
        builder6.setIntervalUnit("s");
        OpenGeminiQueryBuilder queryBuilder6 = new OpenGeminiQueryBuilder(builder6);
        String sql6 = queryBuilder6.buildQuery();
        System.out.println("\n===== Test Case 6: Aggregation Function =====\n" + sql6 + "\n=====================\n");
        assertTrue(sql6.contains("SELECT sum(\"a\") AS \"errorCnt\" FROM"));

        // 测试用例7: 已经被双引号包裹的字段名 - "error" AS "errorCnt"
        QueryBuilder builder7 = new QueryBuilder();
        builder7.setDatabaseName("NzhEMjlBOTk3MzkxRjk5MjQyMzMzQzI4_apm_metric");
        builder7.setMeasurement("business.service");
        builder7.addAgg(new Aggregation(null, "\"error\"", "errorCnt"));
        builder7.addGroupBy("errorType");
        builder7.setInterval(120);
        builder7.setIntervalUnit("s");
        OpenGeminiQueryBuilder queryBuilder7 = new OpenGeminiQueryBuilder(builder7);
        String sql7 = queryBuilder7.buildQuery();
        System.out.println("\n===== Test Case 7: Already Quoted Field =====\n" + sql7 + "\n=====================\n");
        assertTrue(sql7.contains("SELECT \"error\" AS \"errorCnt\" FROM"));

        // 测试用例8: 已经被单引号包裹的字段名 - 'error' AS "errorCnt"
        QueryBuilder builder8 = new QueryBuilder();
        builder8.setDatabaseName("NzhEMjlBOTk3MzkxRjk5MjQyMzMzQzI4_apm_metric");
        builder8.setMeasurement("business.service");
        builder8.addAgg(new Aggregation(null, "'error'", "errorCnt"));
        builder8.addGroupBy("errorType");
        builder8.setInterval(120);
        builder8.setIntervalUnit("s");
        OpenGeminiQueryBuilder queryBuilder8 = new OpenGeminiQueryBuilder(builder8);
        String sql8 = queryBuilder8.buildQuery();
        System.out.println("\n===== Test Case 8: Already Quoted Field (Single Quotes) =====\n" + sql8 + "\n=====================\n");
        assertTrue(sql8.contains("SELECT 'error' AS \"errorCnt\" FROM"));
    }
}
