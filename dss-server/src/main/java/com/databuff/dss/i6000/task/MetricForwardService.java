package com.databuff.dss.i6000.task;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.databuff.common.annotation.LogExecutionTime;
import com.databuff.common.tsdb.dto.explore.QueryRequest;
import com.databuff.common.tsdb.model.TSDBSeries;
import com.databuff.common.utils.TimeUtil;
import com.databuff.dss.config.property.MetricI6000Properties;
import com.databuff.dss.dto.MetricDataDto;
import com.databuff.dss.util.ThreadPoolUtil;
import com.databuff.entity.MetricsQuery;
import com.databuff.entity.TraceServiceEntity;
import com.databuff.kafka.KafkaSender;
import com.databuff.metric.MetricAggregator;
import com.databuff.service.MetricsQueryService;
import com.databuff.service.ServiceSyncService;
import com.google.common.collect.Lists;
import com.sun.istack.NotNull;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.apache.kafka.clients.producer.KafkaProducer;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.TimeUnit;
import java.util.function.Consumer;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.databuff.common.utils.JSONUtils.areAllValuesEmpty;
import static com.databuff.metric.moredb.SQLParser.INTERVAL;

@Component
@Slf4j
public class MetricForwardService {

    @Autowired
    private MetricI6000Properties metricProperties;

    @Autowired
    private I6000Config i6000Config;

    @Autowired
    private BusinessServiceUtil businessServiceUtil;

    @Autowired
    private ServiceSyncService serviceSyncService;

    @Autowired
    private MetricForwardService metricForwardService;

    @Autowired
    private KafkaSender kafkaSender;

    private KafkaProducer kafkaProducer;

    @Autowired
    private MetricsQueryService metricsQueryService;

    @Autowired
    protected MetricAggregator metricAggregator;

    private Map<String, JSONObject> serviceExceptionMap = new ConcurrentHashMap<>();
    private Map<JSONObject, JSONObject> serviceTriggerMap = new ConcurrentHashMap<>();
    private Map<JSONObject, JSONObject> serviceInstanceTriggerMap = new ConcurrentHashMap<>();
    private Map<JSONObject, JSONObject> serviceEndpointTriggerMap = new ConcurrentHashMap<>();
    private Map<JSONObject, JSONObject> serviceDBTriggerMap = new ConcurrentHashMap<>();
    private Map<JSONObject, JSONObject> serviceDBSQLTriggerMap = new ConcurrentHashMap<>();


    @PostConstruct
    @LogExecutionTime
    public void init() {
        final JSONObject p = metricProperties.getProducer();
        final String servers = p.getString("servers");
        final String user = p.getString("user");
        final String password = p.getString("password");
        this.kafkaProducer = kafkaSender.getProducer(servers, user, password);
    }

    /**
     * 任务提交到线程池中执行。每个任务都由一个 Consumer 函数表示，
     * 应用于所提供的配置映射中的每个键。 CountDownLatch 用于跟踪完成情况
     * 任务数。每个任务执行后，锁存器的计数就会递减。
     *
     * @param <T>    配置映射中键的类型
     * @param config 配置映射，其中每个键代表一个任务
     * @param task   应用于配置映射中每个键的 Consumer 函数
     * @paramlatch CountDownLatch 用于跟踪任务的完成情况
     */
    @LogExecutionTime
    public <T> void submitTasks(Map<T, String> config, Consumer<T> task, CountDownLatch latch) {
        for (T key : config.keySet()) {
            ThreadPoolUtil.SERVICE_EXECUTOR.submit(() -> {
                try {
                    task.accept(key);
                } finally {
                    latch.countDown();
                }
            });
        }
    }

    @SneakyThrows
    @LogExecutionTime
    public void awaitTasksCompletion(CountDownLatch latch) {
        try {
            latch.await(5, TimeUnit.MINUTES);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
    }

    @LogExecutionTime
    public void sendDataIfNotEmpty() {
        if (MapUtils.isEmpty(serviceTriggerMap) && MapUtils.isEmpty(serviceInstanceTriggerMap) && MapUtils.isEmpty(serviceEndpointTriggerMap)) {
            log.warn("No data to sync");
            return;
        }

        final JSONObject p = metricProperties.getProducer();
        final String topic = p.getString("topic");

        List<CompletableFuture<Void>> serviceFutures = serviceTriggerMap.entrySet().parallelStream()
                .map(serviceEntry -> CompletableFuture.runAsync(() -> {
                    final JSONObject serviceTransform = metricForwardService.transformJsonApmService(serviceEntry,
                            serviceInstanceTriggerMap,
                            serviceExceptionMap,
                            serviceEndpointTriggerMap
                    );
                    if (serviceTransform == null) {
                        return;
                    }
                    kafkaSender.data2Kafka(kafkaProducer, JSONObject.toJSONString(serviceTransform, SerializerFeature.DisableCircularReferenceDetect), topic);
                }))
                .collect(Collectors.toList());

        List<CompletableFuture<Void>> dbFutures = serviceDBTriggerMap.entrySet().parallelStream()
                .map(serviceEntry -> CompletableFuture.runAsync(() -> {
                    final JSONObject dbTransform = metricForwardService.transformJsonApmDB(serviceEntry, serviceDBSQLTriggerMap);
                    if (dbTransform == null) {
                        return;
                    }
                    kafkaSender.data2Kafka(kafkaProducer, JSONObject.toJSONString(dbTransform, SerializerFeature.DisableCircularReferenceDetect), topic);
                }))
                .collect(Collectors.toList());

        // 等待所有任务完成
        CompletableFuture<Void> allFutures = CompletableFuture.allOf(
                Stream.concat(serviceFutures.stream(), dbFutures.stream()).toArray(CompletableFuture[]::new)
        );
        allFutures.join();

        // 清理映射
        serviceTriggerMap.clear();
        serviceInstanceTriggerMap.clear();
        serviceEndpointTriggerMap.clear();
        serviceDBTriggerMap.clear();
        serviceDBSQLTriggerMap.clear();
        serviceExceptionMap.clear();
    }

    @LogExecutionTime(tag = {"metric"})
    public void processServiceMetric(String metric) {
        MetricsQuery metricsQuery = metricsQueryService.findOpenByIdentifier(metric);
        if (metricsQuery == null) return;

        JSONObject query = metricProperties.getQuery();
        if (query == null) return;

        final Integer timeOffset = query.getInteger("timeOffset");
        final Integer period = query.getInteger("period");
        final Integer interval = query.getInteger("interval");

        QueryRequest queryRequest = new QueryRequest();
        queryRequest.setTimeOffset(timeOffset);
        queryRequest.setPeriod(period);
        queryRequest.setInterval(interval);
        queryRequest.setMetric(metricsQuery.getIdentifier());
        queryRequest.setBy(Lists.newArrayList("serviceId"));

        final Map<Map<String, String>, TSDBSeries> result = metricAggregator.seriesResult(queryRequest);
        final int intValue = query.getIntValue(INTERVAL);
        if (result != null && !result.isEmpty()) {
            List<MetricDataDto> metricDataDtos = processResult(result, metricsQuery, intValue);
            final Map<String, Double> serviceDefValue = metricProperties.getServiceDefValue();
            final Map<String, String> serviceConfig = metricProperties.getServiceConfig();

            // 设置默认值
            for (MetricDataDto metricDataDto : metricDataDtos) {
                final JSONObject tag = metricDataDto.getTag();
                for (Map.Entry<String, String> entry : serviceConfig.entrySet()) {
                    String m = entry.getValue();
                    if (m == null) {
                        continue;
                    }
                    Double defValue = serviceDefValue.get(entry.getKey());
                    if (defValue == null) {
                        defValue = 0.0D;
                    }
                    JSONObject existingValue = serviceTriggerMap.get(tag);
                    if (existingValue != null) {
                        existingValue.putIfAbsent(m, defValue.toString());
                    } else {
                        serviceTriggerMap.put(tag, new JSONObject().fluentPut(m, defValue.toString()));
                    }
                }
            }

            // 设计指标值
            for (MetricDataDto metricDataDto : metricDataDtos) {
                if (metricDataDto == null) {
                    continue;
                }
                final String identifier = metricDataDto.getIdentifier();
                String m = serviceConfig.get(identifier);
                if (m == null) {
                    m = identifier;
                }
                final Double metricValue = metricDataDto.getMetricValue();
                final JSONObject tag = metricDataDto.getTag();
                JSONObject existingValue = serviceTriggerMap.get(tag);
                if (existingValue != null) {
                    existingValue.fluentPut(m, metricValue.toString());
                } else {
                    serviceTriggerMap.put(tag, new JSONObject().fluentPut(m, metricValue.toString()));
                }
            }
        }
    }

    @LogExecutionTime(tag = {"metric"})
    public void processServiceExceptionMetric(String metric) {
        MetricsQuery metricsQuery = metricsQueryService.findOpenByIdentifier(metric);
        if (metricsQuery == null) return;

        JSONObject query = metricProperties.getQuery();
        if (query == null) return;

        final Integer timeOffset = query.getInteger("timeOffset");
        final Integer period = query.getInteger("period");
        final Integer interval = query.getInteger("interval");

        QueryRequest queryRequest = new QueryRequest();
        queryRequest.setTimeOffset(timeOffset);
        queryRequest.setPeriod(period);
        queryRequest.setInterval(interval);
        queryRequest.setMetric(metricsQuery.getIdentifier());
        queryRequest.setBy(Lists.newArrayList("serviceId", "exceptionName"));

        final Map<Map<String, String>, TSDBSeries> result = metricAggregator.seriesResult(queryRequest);
        final int intValue = query.getIntValue(INTERVAL);
        if (result != null && !result.isEmpty()) {
            List<MetricDataDto> metricDataDtos = processResult(result, metricsQuery, intValue);
            final Map<String, String> serviceConfig = metricProperties.getServiceExceptionConfig();

            // 设计指标值
            for (MetricDataDto metricDataDto : metricDataDtos) {
                if (metricDataDto == null) {
                    continue;
                }
                final String identifier = metricDataDto.getIdentifier();
                String m = serviceConfig.get(identifier);
                if (m == null) {
                    m = identifier;
                }
                final Double metricValue = metricDataDto.getMetricValue();
                final JSONObject tag = metricDataDto.getTag();
                final String serviceId = tag.getString("serviceId");
                final String exceptionName = tag.getString("exceptionName");

                JSONObject existingValue = serviceExceptionMap.get(serviceId);
                if (existingValue != null) {
                    existingValue.fluentPut(exceptionName, metricValue.toString());
                } else {
                    serviceExceptionMap.put(serviceId, new JSONObject().fluentPut(exceptionName, metricValue.toString()));
                }
            }
        }
    }

    @LogExecutionTime(tag = {"metric"})
    public void processServiceInstanceMetric(String metric) {
        MetricsQuery metricsQuery = metricsQueryService.findOpenByIdentifier(metric);
        if (metricsQuery == null) return;

        JSONObject query = metricProperties.getQuery();
        if (query == null) return;

        final Integer timeOffset = query.getInteger("timeOffset");
        final Integer period = query.getInteger("period");
        final Integer interval = query.getInteger("interval");

        QueryRequest queryRequest = new QueryRequest();
        queryRequest.setTimeOffset(timeOffset);
        queryRequest.setPeriod(period);
        queryRequest.setInterval(interval);
        queryRequest.setMetric(metricsQuery.getIdentifier());
        queryRequest.setBy(Lists.newArrayList("serviceInstance", "serviceId"));

        final Map<Map<String, String>, TSDBSeries> result = metricAggregator.seriesResult(queryRequest);

        final int intValue = query.getIntValue(INTERVAL);
        if (result != null && !result.isEmpty()) {
            final List<MetricDataDto> metricDataDtos = processResult(result, metricsQuery, intValue);
            final Map<String, String> serviceInstanceConfig = metricProperties.getServiceInstanceConfig();
            final Map<String, Double> serviceInstanceDefValue = metricProperties.getServiceInstanceDefValue();
            // 设置默认值
            for (MetricDataDto metricDataDto : metricDataDtos) {
                final JSONObject tag = metricDataDto.getTag();
                for (Map.Entry<String, String> entry : serviceInstanceConfig.entrySet()) {
                    String m = entry.getValue();
                    if (m == null) {
                        continue;
                    }
                    Double defValue = serviceInstanceDefValue.get(entry.getKey());
                    if (defValue == null) {
                        defValue = 0.0D;
                    }
                    JSONObject existingValue = serviceInstanceTriggerMap.get(tag);
                    if (existingValue != null) {
                        existingValue.putIfAbsent(m, defValue.toString());
                    } else {
                        serviceInstanceTriggerMap.put(tag, new JSONObject().fluentPut(m, defValue.toString()));
                    }
                }
            }
            // 设计指标值
            for (MetricDataDto metricDataDto : metricDataDtos) {
                final String identifier = metricDataDto.getIdentifier();
                String m = serviceInstanceConfig.get(identifier);
                if (m == null) {
                    m = identifier;
                }
                final Double metricValue = metricDataDto.getMetricValue();
                final JSONObject tag = metricDataDto.getTag();
                JSONObject existingValue = serviceInstanceTriggerMap.get(tag);
                if (existingValue != null) {
                    existingValue.fluentPut(m, metricValue.toString());
                } else {
                    serviceInstanceTriggerMap.put(tag, new JSONObject().fluentPut(m, metricValue.toString()));
                }
            }
        }
    }


    @LogExecutionTime(tag = {"metric"})
    public void processServiceEndpointMetric(String metric) {
        MetricsQuery metricsQuery = metricsQueryService.findOpenByIdentifier(metric);
        if (metricsQuery == null) return;

        JSONObject query = metricProperties.getQuery();
        if (query == null) return;

        final Integer timeOffset = query.getInteger("timeOffset");
        final Integer period = query.getInteger("period");
        final Integer interval = query.getInteger("interval");

        QueryRequest queryRequest = new QueryRequest();
        queryRequest.setTimeOffset(timeOffset);
        queryRequest.setPeriod(period);
        queryRequest.setInterval(interval);
        queryRequest.setMetric(metricsQuery.getIdentifier());
        queryRequest.setBy(Lists.newArrayList("resource", "serviceId"));

        final Map<Map<String, String>, TSDBSeries> result = metricAggregator.seriesResult(queryRequest);

        final int intValue = query.getIntValue(INTERVAL);
        if (result != null && !result.isEmpty()) {
            List<MetricDataDto> metricDataDtos = processResult(result, metricsQuery, intValue);
            // 设置默认值
            final Map<String, String> serviceEndpointConfig = metricProperties.getServiceEndpointConfig();
            final Map<String, Double> serviceEndpointDefValue = metricProperties.getServiceEndpointDefValue();
            for (MetricDataDto metricDataDto : metricDataDtos) {
                final JSONObject tag = metricDataDto.getTag();
                for (Map.Entry<String, String> entry : serviceEndpointConfig.entrySet()) {
                    String m = entry.getValue();
                    if (m == null) {
                        continue;
                    }
                    Double defValue = serviceEndpointDefValue.get(entry.getKey());
                    if (defValue == null) {
                        defValue = 0.0D;
                    }
                    JSONObject existingValue = serviceEndpointTriggerMap.get(tag);
                    if (existingValue != null) {
                        existingValue.putIfAbsent(m, defValue.toString());
                    } else {
                        serviceEndpointTriggerMap.put(tag, new JSONObject().fluentPut(m, defValue.toString()));
                    }
                }
            }

            // 设计指标值
            for (MetricDataDto metricDataDto : metricDataDtos) {
                final String identifier = metricDataDto.getIdentifier();
                String m = serviceEndpointConfig.get(identifier);
                if (m == null) {
                    m = identifier;
                }
                final Double metricValue = metricDataDto.getMetricValue();
                JSONObject existingValue = serviceEndpointTriggerMap.get(metricDataDto.getTag());
                if (existingValue != null) {
                    existingValue.fluentPut(m, metricValue.toString());
                } else {
                    serviceEndpointTriggerMap.put(metricDataDto.getTag(), new JSONObject().fluentPut(m, metricValue.toString()));
                }
            }
        }
    }

    @LogExecutionTime(tag = {"metric"})
    public void processServiceDBMetric(String metric) {
        MetricsQuery metricsQuery = metricsQueryService.findOpenByIdentifier(metric);
        if (metricsQuery == null) return;

        JSONObject query = metricProperties.getQuery();
        if (query == null) return;

        final Integer timeOffset = query.getInteger("timeOffset");
        final Integer period = query.getInteger("period");
        final Integer interval = query.getInteger("interval");

        QueryRequest queryRequest = new QueryRequest();
        queryRequest.setTimeOffset(timeOffset);
        queryRequest.setPeriod(period);
        queryRequest.setInterval(interval);
        queryRequest.setMetric(metricsQuery.getIdentifier());
        queryRequest.setBy(Lists.newArrayList("serviceId"));

        final Map<Map<String, String>, TSDBSeries> result = metricAggregator.seriesResult(queryRequest);

        final int intValue = query.getIntValue(INTERVAL);
        if (result != null && !result.isEmpty()) {
            List<MetricDataDto> metricDataDtos = processResult(result, metricsQuery, intValue);
            // 设置默认值
            final Map<String, String> metricConfig = metricProperties.getServiceDBConfig();
            final Map<String, Double> metricDefValue = metricProperties.getServiceDBDefValue();
            for (MetricDataDto metricDataDto : metricDataDtos) {
                final JSONObject tag = metricDataDto.getTag();
                for (Map.Entry<String, String> entry : metricConfig.entrySet()) {
                    String m = entry.getValue();
                    if (m == null) {
                        continue;
                    }
                    Double defValue = metricDefValue.get(entry.getKey());
                    if (defValue == null) {
                        defValue = 0.0D;
                    }
                    JSONObject existingValue = serviceDBTriggerMap.get(tag);
                    if (existingValue != null) {
                        existingValue.putIfAbsent(m, defValue.toString());
                    } else {
                        serviceDBTriggerMap.put(tag, new JSONObject().fluentPut(m, defValue.toString()));
                    }
                }
            }

            // 设计指标值
            for (MetricDataDto metricDataDto : metricDataDtos) {
                final String identifier = metricDataDto.getIdentifier();
                String m = metricConfig.get(identifier);
                if (m == null) {
                    m = identifier;
                }
                final Double metricValue = metricDataDto.getMetricValue();
                JSONObject existingValue = serviceDBTriggerMap.get(metricDataDto.getTag());
                if (existingValue != null) {
                    existingValue.fluentPut(m, metricValue.toString());
                } else {
                    serviceDBTriggerMap.put(metricDataDto.getTag(), new JSONObject().fluentPut(m, metricValue.toString()));
                }
            }
        }
    }

    @LogExecutionTime(tag = {"metric"})
    public void processServiceDBSQLMetric(String metric) {
        MetricsQuery metricsQuery = metricsQueryService.findOpenByIdentifier(metric);
        if (metricsQuery == null) return;

        JSONObject query = metricProperties.getQuery();
        if (query == null) return;

        final Integer timeOffset = query.getInteger("timeOffset");
        final Integer period = query.getInteger("period");
        final Integer interval = query.getInteger("interval");

        QueryRequest queryRequest = new QueryRequest();
        queryRequest.setTimeOffset(timeOffset);
        queryRequest.setPeriod(period);
        queryRequest.setInterval(interval);
        queryRequest.setMetric(metricsQuery.getIdentifier());
        queryRequest.setBy(Lists.newArrayList("serviceId", "sqlContent"));

        final Map<Map<String, String>, TSDBSeries> result = metricAggregator.seriesResult(queryRequest);

        final int intValue = query.getIntValue(INTERVAL);
        if (result != null && !result.isEmpty()) {
            List<MetricDataDto> metricDataDtos = processResult(result, metricsQuery, intValue);
            // 设置默认值
            final Map<String, String> metricConfig = metricProperties.getServiceDBSQLConfig();
            final Map<String, Double> metricDefValue = metricProperties.getServiceDBSQLDefValue();
            for (MetricDataDto metricDataDto : metricDataDtos) {
                final JSONObject tag = metricDataDto.getTag();
                for (Map.Entry<String, String> entry : metricConfig.entrySet()) {
                    String m = entry.getValue();
                    if (m == null) {
                        continue;
                    }
                    Double defValue = metricDefValue.get(entry.getKey());
                    if (defValue == null) {
                        defValue = 0.0D;
                    }
                    JSONObject existingValue = serviceDBSQLTriggerMap.get(tag);
                    if (existingValue != null) {
                        existingValue.putIfAbsent(m, defValue.toString());
                    } else {
                        serviceDBSQLTriggerMap.put(tag, new JSONObject().fluentPut(m, defValue.toString()));
                    }
                }
            }

            // 设计指标值
            for (MetricDataDto metricDataDto : metricDataDtos) {
                final String identifier = metricDataDto.getIdentifier();
                String m = metricConfig.get(identifier);
                if (m == null) {
                    m = identifier;
                }
                final Double metricValue = metricDataDto.getMetricValue();
                JSONObject existingValue = serviceDBSQLTriggerMap.get(metricDataDto.getTag());
                if (existingValue != null) {
                    existingValue.fluentPut(m, metricValue.toString());
                } else {
                    serviceDBSQLTriggerMap.put(metricDataDto.getTag(), new JSONObject().fluentPut(m, metricValue.toString()));
                }
            }
        }
    }

    private List<MetricDataDto> processResult(Map<Map<String, String>, TSDBSeries> result, MetricsQuery metricsQuery, Integer interval) {
        List<MetricDataDto> metricDataDtos = new ArrayList<>();
        // 当前时间，取整分钟
        long curTime = System.currentTimeMillis() / 60000 * 60000;
        for (Map.Entry<Map<String, String>, TSDBSeries> entry : result.entrySet()) {
            final Map<String, String> tags = entry.getKey();
            if (areAllValuesEmpty(tags)) {
                continue;
            }

            TSDBSeries value = entry.getValue();
            List<List<Object>> values = value.getValues();

            for (List<Object> array : values) {
                long timestamp = (long) array.get(0);
                Double metricValue = ((Number) array.get(1)).doubleValue();
                if (metricValue == null) continue;

                // 构建指标数据
                final String id = timestamp + "_" + metricsQuery.getIdentifier() + "_" + tags.hashCode();
                final String uuid = UUID.nameUUIDFromBytes(id.getBytes()).toString().replace("-", "");
                MetricDataDto metricDataDto = MetricDataDto.builder()
                        .id(uuid)
                        .type1(metricsQuery.getType1())
                        .type2(metricsQuery.getType2())
                        .type3(metricsQuery.getType3())
                        .identifier(metricsQuery.getIdentifier())
                        .database(metricsQuery.getDatabase())
                        .measurement(metricsQuery.getMeasurement())
                        .aggregatorType(metricsQuery.getAggregatorType())
                        .formula(metricsQuery.getFormula())
                        .unitCn(metricsQuery.getUnitCn())
                        .timestamp(timestamp)
                        .time(TimeUtil.formatLongToString(timestamp))
                        .metricValue(metricValue)
                        .interval(interval)
                        .latency((curTime - timestamp) / 1000)
                        .tag(new JSONObject().fluentPutAll(tags))
                        .build();
                metricDataDtos.add(metricDataDto);
            }
        }
        return metricDataDtos;
    }


    @LogExecutionTime
    public JSONObject transformJsonApmService(Map.Entry<JSONObject, JSONObject> serviceEntry,
                                              Map<JSONObject, JSONObject> serviceInstanceTriggerMap,
                                              Map<String, JSONObject> serviceExceptionMap,
                                              Map<JSONObject, JSONObject> serviceEndpointTriggerMap) {
        if (serviceEntry == null) {
            return null;
        }

        final int timeOffset = metricProperties.getQuery().getIntValue("timeOffset");
        // 获取当前时间
        LocalDateTime now = LocalDateTime.now();
        // 减去时间偏移量
        LocalDateTime fixedEndTime = now.minusSeconds(timeOffset);
        // 创建一个日期时间格式化器
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        // 格式化当前时间
        String formattedNow = fixedEndTime.format(formatter);

        final JSONArray appResource = new JSONArray();
        final String cmdbIds = i6000Config.getCmdbids();
        if (cmdbIds == null) {
            return new JSONObject().fluentPut("head", new JSONObject().fluentPut("success", false));
        }

        String[] split = cmdbIds.split(",");
        Set<String> uniqueCmdbIds = new HashSet<>(Arrays.asList(split));
        for (String cmdbId : uniqueCmdbIds) {
            List<JSONObject> apmServiceResource = new ArrayList<>();
            final String svcId = serviceEntry.getKey().getString("serviceId");
                if (svcId == null) {
                    continue;
                }

                final String id = businessServiceUtil.getCmdbId(svcId);
                if (!Objects.equals(id, cmdbId)) {
                    continue;
                }

                final TraceServiceEntity serviceEntity = serviceSyncService.getTraceServiceEntityByServiceId(svcId);
                if (serviceEntity == null) {
                    continue;
                }
                String serviceCode = serviceEntity.getService();
                final JSONObject serviceExceptionData = serviceExceptionMap.get(svcId);

                JSONObject service = new JSONObject()
                        .fluentPut("success", true)
                        .fluentPut("cmdbId", serviceCode + "@" + metricProperties.getSecZoneId())
                        .fluentPut("metric", createServiceMetrics(serviceEntry, serviceEntity, serviceCode, serviceExceptionData))
                        .fluentPut("subResourceType", new JSONArray()
                                .fluentAdd(new JSONObject()
                                        .fluentPut("success", true)
                                        .fluentPut("resourceTypeCode", "ApmServiceInst")
                                        .fluentPut("resource", createServiceInstanceMetrics(svcId, serviceInstanceTriggerMap)))
                                .fluentAdd(new JSONObject()
                                        .fluentPut("success", true)
                                        .fluentPut("resourceTypeCode", "ApmEndpoint")
                                        .fluentPut("resource", createEndpointMetrics(svcId, serviceEndpointTriggerMap)))
                        );
                apmServiceResource.add(service);

            appResource.add(new JSONObject()
                    .fluentPut("success", true)
                    .fluentPut("cmdbId", cmdbId)
                    .fluentPut("subResourceType", new JSONArray()
                            .fluentAdd(new JSONObject()
                                    .fluentPut("success", true)
                                    .fluentPut("resourceTypeCode", "ApmService")
                                    .fluentPut("resource", apmServiceResource))
                    ));
        }

        if (appResource.isEmpty()) {
            log.warn("No data to sync");
            return null;
        }

        return new JSONObject()
                .fluentPut("head", new JSONObject()
                        .fluentPut("userToken", "")
                        .fluentPut("serviceCode", metricProperties.getServiceCode())
                        .fluentPut("version", metricProperties.getVersion())
                        .fluentPut("userId", metricProperties.getUserId())
                        .fluentPut("bgId", metricProperties.getBgId()))
                .fluentPut("body", new JSONObject()
                        .fluentPut("metricCls", "PERF")
                        .fluentPut("dataTime", formattedNow)
                        .fluentPut("intervalUnit", "MI")
                        .fluentPut("intervalValue", metricProperties.getQuery().getIntValue("interval") / 60)
                        .fluentPut("resourceType", new JSONArray()
                                .fluentAdd(new JSONObject()
                                        .fluentPut("success", true)
                                        .fluentPut("resourceTypeCode", "App")
                                        .fluentPut("resource", appResource))));
    }

    @LogExecutionTime
    public JSONObject transformJsonApmDB(Map.Entry<JSONObject, JSONObject> serviceDBEntry,
                                         Map<JSONObject, JSONObject> serviceDBSQLTriggerMap) {
        if (serviceDBEntry == null) {
            return null;
        }

        final int timeOffset = metricProperties.getQuery().getIntValue("timeOffset");
        // 获取当前时间
        LocalDateTime now = LocalDateTime.now();
        // 减去时间偏移量
        LocalDateTime fixedEndTime = now.minusSeconds(timeOffset);
        // 创建一个日期时间格式化器
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        // 格式化当前时间
        String formattedNow = fixedEndTime.format(formatter);

        final JSONArray appResource = new JSONArray();
        final String cmdbIds = i6000Config.getCmdbids();
        if (cmdbIds == null) {
            return new JSONObject().fluentPut("head", new JSONObject().fluentPut("success", false));
        }

        String[] split = cmdbIds.split(",");
        Set<String> uniqueCmdbIds = new HashSet<>(Arrays.asList(split));
        for (String cmdbId : uniqueCmdbIds) {
            List<JSONObject> apmDBResource = new ArrayList<>();
            final String svcId = serviceDBEntry.getKey().getString("serviceId");
            if (svcId == null) {
                continue;
            }

            final String id = businessServiceUtil.getCmdbId(svcId);
            if (!Objects.equals(id, cmdbId)) {
                continue;
            }

            final TraceServiceEntity serviceEntity = serviceSyncService.getTraceServiceEntityByServiceId(svcId);
            if (serviceEntity == null) {
                continue;
            }
            String serviceCode = serviceEntity.getService();

            JSONObject service = new JSONObject()
                    .fluentPut("success", true)
                    .fluentPut("cmdbId", serviceCode)
                    .fluentPut("metric", createDBMetrics(serviceDBEntry, serviceCode))
                    .fluentPut("subResourceType", new JSONArray()
                            .fluentAdd(new JSONObject()
                                    .fluentPut("success", true)
                                    .fluentPut("resourceTypeCode", "ApmSql")
                                    .fluentPut("resource", createDBSQLMetrics(svcId, serviceDBSQLTriggerMap)))
                    );
            apmDBResource.add(service);

            appResource.add(new JSONObject()
                    .fluentPut("success", true)
                    .fluentPut("cmdbId", cmdbId)
                    .fluentPut("subResourceType", new JSONArray()
                            .fluentAdd(new JSONObject()
                                    .fluentPut("success", true)
                                    .fluentPut("resourceTypeCode", "ApmDB")
                                    .fluentPut("resource", apmDBResource))
                    ));
        }

        if (appResource.isEmpty()) {
            log.warn("No data to sync");
            return null;
        }

        return new JSONObject()
                .fluentPut("head", new JSONObject()
                        .fluentPut("userToken", "")
                        .fluentPut("serviceCode", metricProperties.getServiceCode())
                        .fluentPut("version", metricProperties.getVersion())
                        .fluentPut("userId", metricProperties.getUserId())
                        .fluentPut("bgId", metricProperties.getBgId()))
                .fluentPut("body", new JSONObject()
                        .fluentPut("metricCls", "PERF")
                        .fluentPut("dataTime", formattedNow)
                        .fluentPut("intervalUnit", "MI")
                        .fluentPut("intervalValue", metricProperties.getQuery().getIntValue("interval") / 60)
                        .fluentPut("resourceType", new JSONArray()
                                .fluentAdd(new JSONObject()
                                        .fluentPut("success", true)
                                        .fluentPut("resourceTypeCode", "App")
                                        .fluentPut("resource", appResource))));
    }


    private List<JSONObject> createMetrics(JSONObject metricsData) {
        List<JSONObject> metrics = new ArrayList<>();
        for (Map.Entry<String, Object> objectEntry : metricsData.entrySet()) {
            JSONObject metric = new JSONObject()
                    .fluentPut("success", true)
                    .fluentPut("metricValue", objectEntry.getValue().toString())
                    .fluentPut("metricCode", objectEntry.getKey());
            metrics.add(metric);
        }
        return metrics;
    }

    private List<JSONObject> createExceptionStatistics(JSONObject metricsData) {
        List<JSONObject> metrics = new ArrayList<>();
        if (metricsData == null) {
            return metrics;
        }
        for (Map.Entry<String, Object> objectEntry : metricsData.entrySet()) {
            metrics.add(new JSONObject()
                    .fluentPut("excepType", objectEntry.getKey())
                    .fluentPut("COUNT", objectEntry.getValue()));
        }
        return metrics;
    }

    @NotNull
    private List<JSONObject> createServiceMetrics(Map.Entry<JSONObject, JSONObject> entry,
                                                  TraceServiceEntity serviceEntity,
                                                  String serviceCode,
                                                  JSONObject serviceExceptionData) {
        final List<JSONObject> metrics = createMetrics(entry.getValue());

        final List<JSONObject> exceptionStatistics = createExceptionStatistics(serviceExceptionData);
        metrics.add(new JSONObject()
                .fluentPut("success", true)
                .fluentPut("metricCode", "ExceptionStatistics")
                .fluentPut("metricValue", JSONArray.toJSONString(exceptionStatistics)));

        metrics.add(new JSONObject()
                .fluentPut("success", true)
                .fluentPut("metricCode", "SecZoneId")
                .fluentPut("metricValue", metricProperties.getSecZoneId()));

        metrics.add(new JSONObject()
                .fluentPut("success", true)
                .fluentPut("metricCode", "APMServiceName")
                .fluentPut("metricValue", serviceEntity.getName()));

        metrics.add(new JSONObject()
                .fluentPut("success", true)
                .fluentPut("metricCode", "APMServiceCode")
                .fluentPut("metricValue", serviceCode));
        return metrics;
    }

    private List<JSONObject> createDBMetrics(Map.Entry<JSONObject, JSONObject> entry, String serviceCode) {
        final List<JSONObject> metrics = createMetrics(entry.getValue());
        metrics.add(new JSONObject()
                .fluentPut("success", true)
                .fluentPut("metricCode", "APMDBPeer")
                .fluentPut("metricValue", serviceCode));
        return metrics;
    }

    private JSONArray createServiceInstanceMetrics(String serviceId, Map<JSONObject, JSONObject> serviceInstanceTriggerMap) {
        JSONArray instanceResource = new JSONArray();
        final TraceServiceEntity serviceEntity = serviceSyncService.getTraceServiceEntityByServiceId(serviceId);
        if (serviceEntity == null) {
            return instanceResource;
        }
        String service = serviceEntity.getService();

        for (Map.Entry<JSONObject, JSONObject> objectEntry : serviceInstanceTriggerMap.entrySet()) {
            final JSONObject instanceTag = objectEntry.getKey();
            if (instanceTag.getString("serviceId").equals(serviceId)) {
                final String serviceInstance = instanceTag.getString("serviceInstance");
                if (serviceInstance == null) {
                    continue;
                }
                List<JSONObject> instanceMetrics = createMetrics(objectEntry.getValue());

                final String APMServiceInstId = service + "@" + serviceInstance;
                instanceMetrics.add(new JSONObject()
                        .fluentPut("success", true)
                        .fluentPut("metricCode", "APMServiceInstId")
                        .fluentPut("metricValue", APMServiceInstId));
                instanceMetrics.add(new JSONObject()
                        .fluentPut("success", true)
                        .fluentPut("metricCode", "APMServiceInstIp")
                        .fluentPut("metricValue", serviceInstance));

                instanceResource.add(new JSONObject()
                        .fluentPut("success", true)
                        .fluentPut("cmdbId", APMServiceInstId)
                        .fluentPut("metric", instanceMetrics));
            }
        }
        return instanceResource;
    }

    private JSONArray createDBSQLMetrics(String serviceId, Map<JSONObject, JSONObject> dbSQLTriggerMap) {
        JSONArray instanceResource = new JSONArray();
        final TraceServiceEntity serviceEntity = serviceSyncService.getTraceServiceEntityByServiceId(serviceId);
        if (serviceEntity == null) {
            return instanceResource;
        }
        String service = serviceEntity.getService();

        for (Map.Entry<JSONObject, JSONObject> objectEntry : dbSQLTriggerMap.entrySet()) {
            final JSONObject instanceTag = objectEntry.getKey();
            if (instanceTag.getString("serviceId").equals(serviceId)) {
                final String sqlContent = instanceTag.getString("sqlContent");
                if (sqlContent == null) {
                    continue;
                }
                List<JSONObject> instanceMetrics = createMetrics(objectEntry.getValue());

                instanceMetrics.add(new JSONObject()
                        .fluentPut("success", true)
                        .fluentPut("metricCode", "APMDBPeer")
                        .fluentPut("metricValue", service));
                instanceMetrics.add(new JSONObject()
                        .fluentPut("success", true)
                        .fluentPut("metricCode", "APMDBStatment")
                        .fluentPut("metricValue", sqlContent));

                instanceResource.add(new JSONObject()
                        .fluentPut("success", true)
                        .fluentPut("cmdbId", sqlContent)
                        .fluentPut("metric", instanceMetrics));
            }
        }
        return instanceResource;
    }


    private JSONArray createEndpointMetrics(String serviceId, Map<JSONObject, JSONObject> serviceInstanceTriggerMap) {
        JSONArray instanceResource = new JSONArray();
        for (Map.Entry<JSONObject, JSONObject> objectEntry : serviceInstanceTriggerMap.entrySet()) {
            final JSONObject instanceTag = objectEntry.getKey();
            if (instanceTag.getString("serviceId").equals(serviceId)) {
                final String resource = instanceTag.getString("resource");
                if (resource == null) {
                    continue;
                }
                List<JSONObject> metrics = createMetrics(objectEntry.getValue());

                metrics.add(new JSONObject()
                        .fluentPut("success", true)
                        .fluentPut("metricCode", "APMEndpointName")
                        .fluentPut("metricValue", resource));

                instanceResource.add(new JSONObject()
                        .fluentPut("success", true)
                        .fluentPut("cmdbId", resource)
                        .fluentPut("metric", metrics));
            }
        }
        return instanceResource;
    }
}