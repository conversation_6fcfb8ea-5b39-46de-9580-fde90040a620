<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.databuff.dao.mysql.ReportMapper">

    <resultMap id="BaseResultMap" type="com.databuff.entity.ReportTemplateEntity" >
        <result column="id" property="id" />
        <result column="name" property="name" />
        <result column="type" property="type" />
        <result column="status" property="status" />
        <result column="content" property="content" />
        <result column="create_time" property="createTime" />
        <result column="creator" property="creator" />
        <result column="dingable" property="dingable" />
        <result column="emailable" property="emailable" />
        <result column="receivers" property="receivers" />
        <result column="cycle_time" property="cycleTime" />
        <result column="custom_time" property="customTime" />
        <result column="api_key" property="apiKey" />
        <result column="update_time" property="updateTime" />
    </resultMap>

    <select id="getReportTemplateList" resultMap="BaseResultMap">
        select * from `dc_report_template`
        <where>
            <if test="apiKey != null and apiKey != ''">
                api_key = #{apiKey}
            </if>
        </where>
    </select>

    <insert id="addReport" useGeneratedKeys="true" keyColumn="id" keyProperty="id" parameterType="com.databuff.entity.ReportEntity">
        insert into `dc_report` (`name`,`type`,`template`,`api_key`) values (#{name},#{type},#{template},#{apiKey})
    </insert>

    <select id="getReportById" resultType="com.databuff.entity.ReportEntity">
        select * from `dc_report` where `id`=#{id}
    </select>

    <delete id="deleteReportByName">
        delete from `dc_report` where `name`=#{name}
    </delete>

    <update id="updateReportStatus">
        update `dc_report` set `status`=#{status} where `id`=#{id}
    </update>



</mapper>