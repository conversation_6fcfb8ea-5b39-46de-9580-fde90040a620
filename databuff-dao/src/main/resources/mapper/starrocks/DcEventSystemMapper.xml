<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.databuff.dao.starrocks.DcEventSystemMapper">

    <select id="findMonitorEventV2" resultType="com.databuff.entity.dto.DCEventDto">

    </select>

    <select id="findEventListByEventIds" resultMap="DCEventDtoResultMap">
        select * from dc_event_system
        where 1 = 1
        <if test="eventIds != null and eventIds.length > 0">
            and id in
            <foreach collection="eventIds" item="eventId" open="(" close=")" separator=",">
                #{eventId}
            </foreach>
        </if>
        order by startTriggerTime desc
    </select>

    <select id="findEventByEventId" resultMap="DCEventDtoResultMap">
        select 	es.*,ess.`readTime`,ess.`read`
        from dc_event_system es
        left join dc_event_system_status ess
        on es.id = ess.id
        where es.id = #{eventId}
    </select>

    <select id="findEventTimeByEventIds" resultType="com.databuff.entity.dto.DCEventDto">
        select id, triggerTime from dc_event_system
        where 1 = 1
        <if test="eventIds != null and eventIds.length > 0">
            and id in
            <foreach collection="eventIds" item="eventId" open="(" close=")" separator=",">
                #{eventId}
            </foreach>
        </if>
    </select>

    <resultMap id="DCEventDtoResultMap" type="com.databuff.entity.dto.DCEventDto">
        <result property="id" column="id"/>
<!--        <result property="apiKey" column="apiKey"/>-->
        <result property="monitorId" column="monitorId"/>
        <result property="value" column="value"/>
        <result property="duration" column="duration"/>
        <result property="silence" column="silence"/>
        <result property="level" column="level"/>
        <result property="read" column="read"/>
        <result property="triggerTime" column="triggerTime"/>
        <result property="creatorId" column="creatorId"/>
        <result property="editorId" column="editorId"/>
        <result property="source" column="source"/>
        <result property="classification" column="classification"/>
        <result property="type" column="type"/>
        <result property="trgTrd" column="trgTrd"/>
        <result property="triggerObjType" column="triggerObjType"/>
        <result property="group" column="group"/>
        <result property="ruleName" column="ruleName"/>
        <result property="message" column="message"/>
        <result property="eventStatus" column="eventStatus"/>
        <result property="trigger" column="trigger" typeHandler="com.baomidou.mybatisplus.extension.handlers.FastjsonTypeHandler"/>
        <result property="tags" column="tags" typeHandler="com.baomidou.mybatisplus.extension.handlers.FastjsonTypeHandler"/>
        <result property="query" column="query" typeHandler="com.databuff.handler.MultiDetectQueryRequestHandler"/>
        <result property="thresholds" column="thresholds" typeHandler="com.baomidou.mybatisplus.extension.handlers.FastjsonTypeHandler"/>
        <result property="multithresholds" column="multithresholds" typeHandler="com.baomidou.mybatisplus.extension.handlers.FastjsonTypeHandler"/>
        <result property="metric" column="metric" javaType="java.util.List" typeHandler="com.databuff.handler.StringArrayToListTypeHandler"/>
        <result property="metrics" column="metrics"  javaType="java.util.List" typeHandler="com.databuff.handler.StringArrayToListTypeHandler"/>
        <result property="busName" column="busName"  javaType="java.util.List" typeHandler="com.databuff.handler.StringArrayToListTypeHandler"/>
        <result property="host" column="host"  javaType="java.util.List" typeHandler="com.databuff.handler.StringArrayToListTypeHandler"/>
        <result property="serviceId" column="serviceId"  javaType="java.util.List" typeHandler="com.databuff.handler.StringArrayToListTypeHandler"/>
        <result property="serviceInstance" column="serviceInstance"  javaType="java.util.List" typeHandler="com.databuff.handler.StringArrayToListTypeHandler"/>
        <result property="deviceName" column="deviceName" javaType="java.util.List" typeHandler="com.databuff.handler.StringArrayToListTypeHandler"/>
    </resultMap>

    <select id="findEventsByCondition" resultMap="DCEventDtoResultMap">
<!--        SELECT id,monitorId, tags FROM dc_event_system-->
        SELECT * FROM dc_event_system
        WHERE 1=1
        <if test="apiKey != null and apiKey != ''">
            AND apiKey = #{apiKey}
        </if>
        <if test="startTime != null and startTime != 0">
            AND triggerTime &gt;= #{startTime}
        </if>
        <if test="endTime != null and endTime != 0">
            AND triggerTime &lt; #{endTime}
        </if>
        <if test="condition != null and condition != ''">
            AND ${condition}
        </if>
    </select>

</mapper>