<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.databuff.dao.starrocks.RumIosDetailMapper">

    <sql id="baseFilter">
        AND app_id = #{request.appId}
        AND start_time BETWEEN #{request.fromTime} AND #{request.toTime}
    </sql>

    <select id="selectLaunchById" resultType="com.databuff.entity.rum.web.RumIosLaunchDto">
        SELECT
        launch_id,
        launch_type,
        start_time,
        duration,
        app_id,
        user_id,
        device_id,
        session_id,
        app_version,
        is_jailbroken,
        probe_version,
        ip,
        region,
        isp,
        os,
        device_identifier,
        network_type,
        device_arch,
        ui_orientation,
        "ios" as appType
        FROM dc_rum_ios_launch
        WHERE launch_id = #{request.launchId}
        <include refid="baseFilter"/>
        limit 1
    </select>


    <select id="selectLifecycleMethods" resultType="com.databuff.entity.rum.starrocks.RumIosLifecycleMethod">
        SELECT
            method_name,
            page_name,
            start,
            end,
            duration,
            type
    FROM dc_rum_ios_lifecycle_method
    WHERE associated_id = #{associatedId}
        AND type = #{type}
        AND app_id = #{request.appId}
        AND start_time BETWEEN #{request.fromTime} AND #{request.toTime}
        ORDER BY page_name, start ASC
    </select>

    <select id="selectLaunchSpans" resultType="com.databuff.entity.rum.web.RumIosLaunchSpanDto">
        SELECT
        launch_id,
        start_time,
        app_id,
        trace_id,
        span_id,
        parent_id,
        service,
        http_url,
        status_code,
        start,
        end,
        duration
        FROM dc_rum_ios_launch_span
        WHERE launch_id = #{request.launchId}
        <include refid="baseFilter"/>
        ORDER BY start ASC
    </select>


    <select id="selectPageById" resultType="com.databuff.entity.rum.web.RumIosPageDto">
        SELECT
        page_id,
        page_name,
        start_time,
        duration,
        app_id,
        user_id,
        device_id,
        session_id,
        app_version,
        is_jailbroken,
        probe_version,
        ip,
        region,
        isp,
        os,
        device_identifier,
        network_type,
        device_arch,
        ui_orientation,
        "ios" as appType
        FROM dc_rum_ios_page
        WHERE page_id = #{request.pageId}
        <include refid="baseFilter"/>
        limit 1
    </select>

    <select id="selectPageSpans" resultType="com.databuff.entity.rum.web.RumIosPageSpanDto">
        SELECT
        page_id,
        start_time,
        app_id,
        trace_id,
        span_id,
        parent_id,
        service,
        http_url,
        status_code,
        start,
        end,
        duration
        FROM dc_rum_ios_page_span
        WHERE page_id = #{request.pageId}
        <include refid="baseFilter"/>
        ORDER BY start ASC
    </select>

    <select id="selectActionById" resultType="com.databuff.entity.rum.web.RumIosActionDto">
        SELECT
        action_id,
        action_name,
        page_name,
        start_time,
        duration,
        app_id,
        user_id,
        device_id,
        session_id,
        app_version,
        is_jailbroken,
        probe_version,
        ip,
        region,
        isp,
        os,
        device_identifier,
        network_type,
        device_arch,
        ui_orientation,
        "ios" as appType
        FROM dc_rum_ios_action
        WHERE action_id = #{request.actionId}
        <include refid="baseFilter"/>
        limit 1
    </select>

    <select id="selectActionSpans" resultType="com.databuff.entity.rum.web.RumIosActionSpanDto">
        SELECT
        action_id,
        start_time,
        app_id,
        trace_id,
        span_id,
        parent_id,
        service,
        http_url,
        status_code,
        start,
        end,
        duration
        FROM dc_rum_ios_action_span
        WHERE action_id = #{request.actionId}
        <include refid="baseFilter"/>
        ORDER BY start ASC
    </select>

    <select id="selectAnrById" resultType="com.databuff.entity.rum.web.RumIosAnrDto">
        SELECT
        anr_id,
        anr_name,
        start_time,
        app_id,
        user_id,
        device_id,
        session_id,
        app_launch_time,
        app_version,
        probe_version,
        ip,
        region,
        isp,
        os,
        device_identifier,
        network_type,
        device_arch,
        ui_orientation,
        device_memory,
        available_memory,
        ROUND(CAST(available_memory AS DOUBLE) / NULLIF(device_memory, 0) * 100, 2) as available_memory_rate,
        device_storage,
        available_storage,
        ROUND(CAST(available_storage AS DOUBLE) / NULLIF(device_storage, 0) * 100, 2) as available_storage_rate,
        binary_image_uuid,
        "ios" as appType
        FROM dc_rum_ios_anr
        WHERE anr_id = #{request.anrId}
        <include refid="baseFilter"/>
        limit 1
    </select>

    <select id="selectAnrStack" resultType="com.databuff.entity.rum.web.RumIosAnrDto">
        SELECT stack_info,device_identifier
        FROM dc_rum_ios_anr
        WHERE anr_id = #{request.anrId}
        <include refid="baseFilter"/>
        limit 1
    </select>

    <select id="selectCrashById" resultType="com.databuff.entity.rum.web.RumIosCrashDto">
        SELECT
        crash_id,
        crash_name,
        crash_type,
        start_time,
        app_id,
        user_id,
        device_id,
        session_id,
        app_launch_time,
        app_version,
        probe_version,
        ip,
        region,
        isp,
        os,
        device_identifier,
        network_type,
        device_arch,
        ui_orientation,
        device_memory,
        available_memory,
        ROUND(CAST(available_memory AS DOUBLE) / NULLIF(device_memory, 0) * 100, 2) as available_memory_rate,
        device_storage,
        available_storage,
        ROUND(CAST(available_storage AS DOUBLE) / NULLIF(device_storage, 0) * 100, 2) as available_storage_rate,
        binary_image_uuid,
        "ios" as appType
        FROM dc_rum_ios_crash
        WHERE crash_id = #{request.crashId}
        <include refid="baseFilter"/>
        limit 1
    </select>

    <select id="selectCrashStack" resultType="com.databuff.entity.rum.web.RumIosCrashDto">
        SELECT stack_info,device_identifier
        FROM dc_rum_ios_crash
        WHERE crash_id = #{request.crashId}
        <include refid="baseFilter"/>
        limit 1
    </select>

    <select id="selectExceptionTrace" resultType="com.databuff.entity.rum.web.RumIosActionDto">
        SELECT
            action_id, action_name, page_name, start_time, app_id
        FROM dc_rum_ios_action
        WHERE start_time BETWEEN DATE_SUB(#{startTime}, INTERVAL 1 HOUR) AND #{startTime}
          AND app_id = #{appId}
          AND user_id = #{userId}
        ORDER BY start_time DESC
        LIMIT 5
    </select>

    <select id="selectLaunchStart" resultType="java.lang.Long">
    SELECT start 
    FROM dc_rum_ios_launch
    WHERE launch_id = #{request.launchId} 
    <include refid="baseFilter"/>
    LIMIT 1
    </select>

    <select id="selectPageStart" resultType="java.lang.Long">
        SELECT start 
        FROM dc_rum_ios_page
        WHERE page_id = #{request.pageId} 
        <include refid="baseFilter"/>
        LIMIT 1
    </select>

    <select id="selectActionStart" resultType="java.lang.Long">
        SELECT start 
        FROM dc_rum_ios_action
        WHERE action_id = #{request.actionId} 
        <include refid="baseFilter"/>
        LIMIT 1
    </select>

</mapper>
