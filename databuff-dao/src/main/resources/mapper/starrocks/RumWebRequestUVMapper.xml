<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.databuff.dao.starrocks.RumWebRequestUVMapper">

    <select id="getRequestErrorUVTrend" resultType="com.databuff.entity.dto.TimeValue">
        SELECT
        ${timeBucket} AS ts,
        HLL_UNION_AGG(uv) AS value
        FROM dc_rum_web_request_uv
        WHERE app_id = #{appId}
        AND startTime >= #{fromTime}
        AND startTime &lt;= #{toTime}
        AND is_error = 1
        <include refid="commonFilters"/>
        GROUP BY ts
        ORDER BY ts
    </select>


    <select id="getRequestUVWithErrorUVForUrls" resultType="com.databuff.entity.rum.web.RequestUVDto">
        SELECT
        processed_http_url AS url,
        HLL_UNION_AGG(uv) AS totalUV,
        HLL_UNION_AGG(CASE WHEN is_error = 1 THEN uv ELSE NULL END) AS errorUV
        FROM dc_rum_web_request_uv
        WHERE app_id = #{appId}
        AND startTime >= #{fromTime}
        AND startTime &lt;= #{toTime}
        <include refid="commonFilters"/>
        GROUP BY processed_http_url
    </select>


    <select id="getRequestUVWithErrorUV" resultType="com.databuff.entity.rum.web.RequestUVDto">
        SELECT
        processed_http_url AS url,
        HLL_UNION_AGG(uv) AS totalUV,
        HLL_UNION_AGG(CASE WHEN is_error = 1 THEN uv ELSE NULL END) AS errorUV
        FROM dc_rum_web_request_uv
        WHERE app_id = #{appId}
        AND startTime >= #{fromTime}
        AND startTime &lt;= #{toTime}
        <include refid="commonFiltersWithoutProcessedHttpUrls"/>
        GROUP BY processed_http_url
        ORDER BY
        <choose>
            <when test="criteria.sortField == 'userCount'">
                totalUV ${criteria.sortOrder}
            </when>
            <when test="criteria.sortField == 'errorAffectedUserCount'">
                errorUV ${criteria.sortOrder}
            </when>
            <otherwise>
                totalUV DESC
            </otherwise>
        </choose>
        LIMIT #{offset}, #{limit}
    </select>


    <select id="countRequestUVWithErrorUV" resultType="long">
        SELECT COUNT(DISTINCT processed_http_url)
        FROM dc_rum_web_request_uv
        WHERE app_id = #{appId}
        AND startTime >= #{fromTime}
        AND startTime &lt;= #{toTime}
        <include refid="commonFiltersWithoutProcessedHttpUrls"/>
    </select>



    <sql id="commonFilters">
        <if test="criteria.requestType != null">
            AND request_type = #{criteria.requestType}
        </if>
        <if test="criteria.domain != null and criteria.domain != ''">
            AND domain LIKE CONCAT('%', #{criteria.domain}, '%')
        </if>
        <if test="criteria.processedPath != null and criteria.processedPath != ''">
            AND processed_path LIKE CONCAT('%', #{criteria.processedPath}, '%')
        </if>
        <if test="criteria.isp != null and criteria.isp != ''">
            AND isp LIKE CONCAT('%', #{criteria.isp}, '%')
        </if>
        <if test="criteria.statusCode != null and criteria.statusCode != ''">
            AND status_code LIKE CONCAT('%', #{criteria.statusCode}, '%')
        </if>
        <if test="criteria.service != null and criteria.service != ''">
            AND service LIKE CONCAT('%', #{criteria.service}, '%')
        </if>
        <if test="processedHttpUrls != null and processedHttpUrls.size() > 0">
            AND processed_http_url IN
            <foreach item="url" collection="processedHttpUrls" open="(" separator="," close=")">
                #{url}
            </foreach>
        </if>
    </sql>

    <sql id="commonFiltersWithoutProcessedHttpUrls">
        <if test="criteria.requestType != null">
            AND request_type = #{criteria.requestType}
        </if>
        <if test="criteria.domain != null and criteria.domain != ''">
            AND domain LIKE CONCAT('%', #{criteria.domain}, '%')
        </if>
        <if test="criteria.processedPath != null and criteria.processedPath != ''">
            AND processed_path LIKE CONCAT('%', #{criteria.processedPath}, '%')
        </if>
        <if test="criteria.isp != null and criteria.isp != ''">
            AND isp LIKE CONCAT('%', #{criteria.isp}, '%')
        </if>
        <if test="criteria.statusCode != null and criteria.statusCode != ''">
            AND status_code LIKE CONCAT('%', #{criteria.statusCode}, '%')
        </if>
        <if test="criteria.service != null and criteria.service != ''">
            AND service LIKE CONCAT('%', #{criteria.service}, '%')
        </if>
    </sql>

</mapper>
