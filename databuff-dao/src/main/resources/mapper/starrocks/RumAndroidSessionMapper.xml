<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.databuff.dao.starrocks.RumAndroidSessionMapper">

    <select id="selectPartitions" resultType="java.util.Map">
        SHOW PARTITIONS FROM ${tableName}
    </select>

    <update id="dropPartition">
        ALTER TABLE ${tableName} DROP PARTITION ${partition} FORCE
    </update>


    <select id="getSessionDetail" resultType="com.databuff.entity.rum.web.SessionDetailDto">
        WITH first_event AS (
            SELECT *
            FROM (
                     SELECT
                         start_time,
                         ip,
                         region,
                         isp,
                         os,
                         device_identifier,
                         app_id,
                         probe_version,
                         app_version,
                         user_id,
                         1 as priority
                     FROM dc_rum_android_launch
                     WHERE session_id = #{sessionId}
                       AND start_time BETWEEN #{request.fromTime} AND #{request.toTime}
                     UNION ALL
                     SELECT
                         start_time,
                         ip,
                         region,
                         isp,
                         os,
                         device_identifier,
                         app_id,
                         probe_version,
                         app_version,
                         user_id,
                         2 as priority
                     FROM dc_rum_android_page
                     WHERE session_id = #{sessionId}
                       AND start_time BETWEEN #{request.fromTime} AND #{request.toTime}
                     UNION ALL
                     SELECT
                         start_time,
                         ip,
                         region,
                         isp,
                         os,
                         device_identifier,
                         app_id,
                         probe_version,
                         app_version,
                         user_id,
                         3 as priority
                     FROM dc_rum_android_action
                     WHERE session_id = #{sessionId}
                       AND start_time BETWEEN #{request.fromTime} AND #{request.toTime}
                     UNION ALL
                     SELECT
                         start_time,
                         ip,
                         region,
                         isp,
                         os,
                         device_identifier,
                         app_id,
                         probe_version,
                         app_version_code as app_version,
                         user_id,
                         4 as priority
                     FROM dc_rum_android_crash
                     WHERE session_id = #{sessionId}
                       AND start_time BETWEEN #{request.fromTime} AND #{request.toTime}
                     UNION ALL
                     SELECT
                         start_time,
                         ip,
                         region,
                         isp,
                         os,
                         device_identifier,
                         app_id,
                         probe_version,
                         app_version_code as app_version,
                         user_id,
                         5 as priority
                     FROM dc_rum_android_anr
                     WHERE session_id = #{sessionId}
                       AND start_time BETWEEN #{request.fromTime} AND #{request.toTime}
                 ) all_events
            ORDER BY
                CASE WHEN region != '未知' THEN 0 ELSE 1 END,
                start_time ASC,
                priority ASC
            LIMIT 1
        ),
             interaction_stats AS (
                 SELECT COUNT(*) as interaction_count
                 FROM (
                          SELECT 1 FROM dc_rum_android_launch
                          WHERE session_id = #{sessionId}
                            AND start_time BETWEEN #{request.fromTime} AND #{request.toTime}
                          UNION ALL
                          SELECT 1 FROM dc_rum_android_page
                          WHERE session_id = #{sessionId}
                            AND start_time BETWEEN #{request.fromTime} AND #{request.toTime}
                          UNION ALL
                          SELECT 1 FROM dc_rum_android_action
                          WHERE session_id = #{sessionId}
                            AND start_time BETWEEN #{request.fromTime} AND #{request.toTime}
                      ) interactions
             ),
             exception_stats AS (
                 SELECT COUNT(*) as exception_count
                 FROM (
                          SELECT 1 FROM dc_rum_android_crash
                          WHERE session_id = #{sessionId}
                            AND start_time BETWEEN #{request.fromTime} AND #{request.toTime}
                          UNION ALL
                          SELECT 1 FROM dc_rum_android_anr
                          WHERE session_id = #{sessionId}
                            AND start_time BETWEEN #{request.fromTime} AND #{request.toTime}
                      ) exceptions
             ),
             time_stats AS (
                 SELECT
                     MIN(start_time) as first_time,
                     MAX(start_time) as last_time
                 FROM (
                          SELECT start_time FROM dc_rum_android_launch
                          WHERE session_id = #{sessionId}
                            AND start_time BETWEEN #{request.fromTime} AND #{request.toTime}
                          UNION ALL
                          SELECT start_time FROM dc_rum_android_page
                          WHERE session_id = #{sessionId}
                            AND start_time BETWEEN #{request.fromTime} AND #{request.toTime}
                          UNION ALL
                          SELECT start_time FROM dc_rum_android_action
                          WHERE session_id = #{sessionId}
                            AND start_time BETWEEN #{request.fromTime} AND #{request.toTime}
                          UNION ALL
                          SELECT start_time FROM dc_rum_android_crash
                          WHERE session_id = #{sessionId}
                            AND start_time BETWEEN #{request.fromTime} AND #{request.toTime}
                          UNION ALL
                          SELECT start_time FROM dc_rum_android_anr
                          WHERE session_id = #{sessionId}
                            AND start_time BETWEEN #{request.fromTime} AND #{request.toTime}
                      ) all_events
             )
        SELECT
            fe.app_id as appId,
            TIMESTAMPDIFF(SECOND, ts.first_time, ts.last_time) as duration,
            ts.first_time as startTime,
            ist.interaction_count as interactionCount,
            est.exception_count as exceptionCount,
            fe.probe_version as probeVersion,
            fe.app_version as appVersion,
            fe.user_id as userId,
            #{sessionId} as sessionId,
            fe.ip as ip,
            fe.region as region,
            fe.isp as isp,
            fe.os as operatingSystem,
            fe.device_identifier as deviceIdentifier,
            'android' as appType
        FROM first_event fe, interaction_stats ist, exception_stats est, time_stats ts
        LIMIT 1
    </select>




    <select id="getInteractionData" resultType="com.databuff.entity.rum.web.InteractionDto">
        SELECT * FROM (
                          SELECT
                              start_time as startTime,
                              '启动' as interactionType,
                              launch_type as interactionName,
                              true as canJump,
                              launch_id as interactionId,
                              app_id as appId,
                              start
                          FROM dc_rum_android_launch
                          WHERE session_id = #{request.sessionId}
                            AND start_time BETWEEN #{request.fromTime} AND #{request.toTime}

                          UNION ALL

                          SELECT
                              start_time as startTime,
                              '页面' as interactionType,
                              CONCAT('加载页面', page_name) as interactionName,
                              true as canJump,
                              page_id as interactionId,
                              app_id as appId,
                              start
                          FROM dc_rum_android_page
                          WHERE session_id = #{request.sessionId}
                            AND start_time BETWEEN #{request.fromTime} AND #{request.toTime}

                          UNION ALL

                          SELECT
                              start_time as startTime,
                              '操作' as interactionType,
                              CONCAT('在页面', page_name, '上点击', action_name) as interactionName,
                              true as canJump,
                              action_id as interactionId,
                              app_id as appId,
                              start
                          FROM dc_rum_android_action
                          WHERE session_id = #{request.sessionId}
                            AND start_time BETWEEN #{request.fromTime} AND #{request.toTime}
                      ) AS interactions
        ORDER BY start ASC
    </select>


    <select id="getExceptionData" resultType="com.databuff.entity.rum.web.ExceptionDto">
        SELECT * FROM (
                          SELECT
                              start_time as errorTime,
                              '卡顿' as exceptionType,
                              anr_name as errorMessage,
                              anr_id as exceptionId,
                              app_id as appId,
                              1 as priority
                          FROM dc_rum_android_anr
                          WHERE session_id = #{request.sessionId}
                            AND start_time BETWEEN #{request.fromTime} AND #{request.toTime}

                          UNION ALL

                          SELECT
                              start_time as errorTime,
                              '崩溃' as exceptionType,
                              crash_name as errorMessage,
                              crash_id as exceptionId,
                              app_id as appId,
                              2 as priority
                          FROM dc_rum_android_crash
                          WHERE session_id = #{request.sessionId}
                            AND start_time BETWEEN #{request.fromTime} AND #{request.toTime}
                      ) AS exceptions
        ORDER BY errorTime ASC, priority ASC
    </select>


</mapper>
