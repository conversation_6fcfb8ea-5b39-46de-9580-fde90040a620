<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.databuff.dao.mysql.UserMapper">

    <!-- 定义结果映射 -->
    <resultMap id="roleRetMap" type="com.databuff.entity.RoleRet">
        <id column="id" property="id"/>
        <result column="role_name" property="name"/>
        <result column="define_type" property="defineType"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
        <result column="pid" property="pid"/>
    </resultMap>

    <!-- 创建角色 -->
    <insert id="createRole" parameterType="com.databuff.entity.Role" useGeneratedKeys="true" keyColumn="id" keyProperty="id">
        INSERT INTO dc_role (role_name, description, define_type, create_time, update_time, available, pid)
        VALUES (#{name}, #{description}, #{defineType}, #{createTime}, #{updateTime}, #{available}, COALESCE(#{pid}, 0))
    </insert>

    <insert id="addTenant" useGeneratedKeys="true" keyColumn="id" keyProperty="id">
        insert into dc_saas_tenant (tenant_name,email_addr,tenant_pw,use_type,tenant_type,
        cer_status,lic_startTime,lic_endTime,api_key,create_time,update_time,use_time)
        values (#{tenantName},#{emailAddr},#{tenantPw},#{useType},#{tenantType},#{cerStatus},
        #{licStartTime},#{licEndTime},#{apiKey},#{createTime},#{updateTime},#{useTime});
    </insert>
    <insert id="addOrg" useGeneratedKeys="true" keyColumn="id" keyProperty="id">
        insert into org (`version`,`name`,`created`,`updated`) values(0,#{name},now(),now())
    </insert>
    <insert id="addDataSource">
        INSERT INTO data_source (org_id, `version`, `type`, `name`, `access`, `url`, `database`, basic_auth, is_default, `created`,
                                 `updated`, with_credentials, uid, json_data)
        VALUES (#{orgId}, #{version}, #{type}, #{name}, #{access}, #{url}, #{database}, #{basicAuth}, #{isDefault}, #{created},
                #{updated}, #{withCredentials}, #{uid}, #{jsonData})
            ON DUPLICATE KEY UPDATE
                                 `version` = VALUES(`version`),
                                 `type` = VALUES(`type`),
                                 `access` = VALUES(`access`),
                                 `url` = VALUES(`url`),
                                 `database` = VALUES(`database`),
                                 basic_auth = VALUES(basic_auth),
                                 is_default = VALUES(is_default),
                                 `updated` = VALUES(`updated`),
                                 with_credentials = VALUES(with_credentials),
                                 json_data = VALUES(json_data)
    </insert>
    <insert id="addDashboard" useGeneratedKeys="true" keyColumn="id" keyProperty="id">
        insert into dashboard (version, slug, title, data, org_id, created, updated,folder_id,is_folder,has_acl, uid)
         values (#{version},#{slug},#{title},#{data},#{orgId},#{created},#{updated},#{folderId},#{isFolder},#{hasAcl},#{uid})
        ON DUPLICATE KEY
        UPDATE version=#{version},slug=#{slug},title=#{title},data=#{data},org_id=#{orgId},created=#{created},updated=#{updated},
            folder_id=#{folderId},is_folder=#{isFolder},has_acl=#{hasAcl},uid=#{uid}
    </insert>
    <insert id="addSaasOtherConfine">
        insert into dc_saas_other_confine(api_key,tenant_id,confine_host,confine_plugins,confine_app,confine_container,confine_status,confine_end_time)
        values (#{apiKey},#{tenantId},#{confineHost},#{confinePlugins},#{confineApp},#{confineContainer},#{confineStatus},#{confineEndTime})
        ON DUPLICATE KEY
        UPDATE confine_host=#{confineHost},confine_plugins=#{confinePlugins},confine_app=#{confineApp},
        confine_container=#{confineContainer},confine_status=#{confineStatus},confine_end_time=#{confineEndTime}
    </insert>
    <update id="updateTenantLic">
        update dc_saas_tenant set lic_endTime = #{endTime} where api_key=#{apiKey}
    </update>
    <select id="selectOne" resultType="com.databuff.entity.User">
        select id,account,password,lockexpire_time,passwd_errors,c_id,first_login,member_tenant,huawei_imc_id from dc_user
        <if test='account != null'>
            where account=#{account}
        </if>
        <if test='account == null'>
            where account='error'
        </if>
    </select>
    <select id="selectAllUserList" resultType="com.databuff.entity.User">
        select distinct u.id,u.company,u.account,u.responsible,u.mobile,u.email_addr as emailAddr,u.remark,u.lockexpire_time lockexpireTime from
        dc_user u
        LEFT JOIN dc_user_role ur on u.id = ur.user_id
        <where>
            <if test="cId !=null">
                and u.c_id = #{cId}
            </if>
            <if test="account !=null">
                and u.account LIKE binary CONCAT('%',#{account},'%')
            </if>
            <if test="responsible !=null">
                and u.responsible = #{responsible}
            </if>
            <if test="lockexpireTime !=null and status !=null and !status.equals('已锁定')">
                and #{lockexpireTime} &gt;= u.lockexpire_time
            </if>
            <if test="lockexpireTime !=null and status !=null and status.equals('已锁定')">
                and #{lockexpireTime} &lt; u.lockexpire_time
            </if>
            <if test="roleId !=null">
                and ur.role_id = #{roleId}
            </if>
            <if test="onLine !=null">
                and u.account in
                <foreach collection="onLine" item="item" index="index" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="offLine !=null">
                and u.account not in
                <foreach collection="offLine" item="item" index="index" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
        </where>
    </select>

    <!-- 查询角色列表 -->
    <select id="findRoles" resultMap="roleRetMap">
        select id, role_name, description, define_type, create_time, update_time, available, pid
        from dc_role
        where available = 0
        <if test="pid != null">
            and pid = #{pid}
        </if>
        <if test="defineType != null">
            and define_type = #{defineType}
        </if>
        <if test="keyword != null">
            and role_name LIKE binary CONCAT('%', #{keyword}, '%')
        </if>
    </select>

    <select id="findRoleByUserName" resultType="com.databuff.entity.RoleRet">
        SELECT r.id,r.role_name as `name`,r.define_type from dc_user u
        LEFT JOIN dc_user_role ur on u.id=ur.user_id
        LEFT JOIN dc_role r on ur.role_id=r.id
        where u.account=#{account}
    </select>

    <select id="getGraDataSourceByApikey" resultType="com.databuff.entity.extend.DataSource">
        select t2.id,t2.org_id,t2.version,t2.url,t2.`database`,t2.json_data,t2.type,t2.name,t2.is_default,t2.read_only from
        (select id from org where `name`=#{apiKey}) t1
        left join
        data_source t2 on t1.id = t2.org_id
    </select>
    <select id="getGraDataSourcesByApikey" resultType="com.databuff.entity.extend.DataSource">
        select t2.id,t2.org_id,t2.version,t2.url,t2.`database`,t2.json_data,t2.type,t2.name,t2.is_default,t2.read_only from
                (select id from org where `name`=#{apiKey}) t1
                    left join
            data_source t2 on t1.id = t2.org_id
    </select>
    <select id="getFontDashboardIdByOrgId" resultType="com.databuff.entity.SaasDashboard">
        select uid,id from dashboard where org_id = #{orgId} and title = #{title}
    </select>

    <select id="getConfineByApiKey" resultType="com.databuff.entity.SaasOtherConfine">
        select * from dc_saas_other_confine where api_key = #{apiKey}
    </select>

    <select id="getUserInfo" resultType="com.databuff.entity.User">
        select u.id,u.account,u.responsible,u.mobile,u.email_addr as emailAddr,u.company,u.c_id as cId,u.remark from dc_user u
        where account=#{account}
    </select>

    <update id="updateImcUserInfo" parameterType="com.databuff.entity.User">
        UPDATE dc_user
        <set>
            <if test="null != responsible ">responsible=#{responsible},</if>
            <if test="null != nickName ">nick_name = #{nickName},</if>
            <if test="null != mobile  ">mobile = #{mobile},</if>
            <if test="null != mobile  ">member_phone = #{mobile},</if>
            <if test="null != memberTenant  ">member_tenant = #{memberTenant},</if>
            <if test="null != emailAddr ">email_addr = #{emailAddr},</if>
            <if test="null != account ">account = #{account},</if>
            <if test="null != password ">password = #{password},</if>
            <if test="null != updateTime ">update_time = #{updateTime}</if>
        </set>
        WHERE huawei_imc_id = #{huaweiImcId}
    </update>

    <update id="updateTenantInfo" parameterType="com.databuff.entity.User">
        UPDATE dc_saas_tenant
        <set>
            <if test="null != mobile ">tenant_phone=#{mobile},</if>
            <if test="null != emailAddr ">email_addr = #{emailAddr},</if>
            <if test="null != password  ">tenant_pw = #{password},</if>
            <if test="null != account  ">tenant_name = #{account},</if>
            <if test="null != updateTime ">update_time = #{updateTime}</if>
        </set>
        WHERE api_key= #{cId}
    </update>


    <select id="selectOneImcUsre" resultType="com.databuff.entity.User">
        select id,account,password,lockexpire_time,passwd_errors,c_id,first_login,member_tenant,huawei_imc_id from dc_user
        where huawei_imc_id LIKE binary CONCAT(#{tenant},'%') LIMIT 1
    </select>


    <insert id="saveLicenseProduct" useGeneratedKeys="true" keyColumn="id" keyProperty="id" parameterType="com.databuff.entity.dto.LicenseProductEntry">
        insert into dc_license_product (license_product_name,license_product_version,license_product_serialnum,license_product_status,license_product_starttime,license_product_endtime,license_product_path,expire_limit)
        values(#{licenseProductName},#{licenseProductVersion},#{licenseProductSerialnum},#{licenseProductStatus},#{licenseProductStarttime},#{licenseProductEndtime},#{licenseProductPath},#{expireLimit})
    </insert>

    <insert id="insertAuthPlugins" parameterType="com.databuff.entity.MetaConfigEntity">
        insert into dc_sys_meta_config (`code`,`describe`,`params`,`enabled`,`api_key`) VALUES (#{code}, #{describe}, #{params}, #{enabled}, #{apiKey})
    </insert>

    <update id="updateAuthPlugins" parameterType="com.databuff.entity.MetaConfigEntity">
        update dc_sys_meta_config
        <set>
            <if test="null != describe ">`describe`=#{describe},</if>
            <if test="null != params ">params=#{params},</if>
            <if test="null != enabled ">enabled=#{enabled}</if>
        </set>
        where api_key=#{apiKey} and code=#{code}
    </update>

    <select id="getAuthPluginsConfig" resultType="com.databuff.entity.MetaConfigEntity">
        select * from dc_sys_meta_config where code=#{code}
    </select>

    <delete id="deleteAllAAU">
        delete from `dc_databuff_aau`
        <where>
            <if test="apiKey != null and apiKey != ''">
                api_key=#{apiKey}
            </if>
        </where>
    </delete>


</mapper>