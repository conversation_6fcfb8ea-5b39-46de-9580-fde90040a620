package com.databuff.kafka;

import org.apache.commons.pool2.PooledObjectFactory;
import org.apache.commons.pool2.impl.GenericObjectPool;
import org.apache.commons.pool2.impl.GenericObjectPoolConfig;
import org.apache.kafka.clients.producer.KafkaProducer;

/**
 * @author:TianMing
 * @date: 2023/4/19
 * @time: 19:30
 */
public class KafkaProducerPool extends GenericObjectPool<KafkaProducer> {
    public KafkaProducerPool(PooledObjectFactory<KafkaProducer> factory, GenericObjectPoolConfig config) {
        super(factory, config);
    }

    public KafkaProducer getClient() throws Exception {
        // 从池中取一个对象
        return this.borrowObject();
    }

    public void releaseConnection(KafkaProducer producer) {
        if (producer != null) {
            this.returnObject(producer);
        }
    }
}
