package com.databuff.entity;

import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * 角色与域关系
 */
@Data
public class RoleGroupRelation extends RoleGroupRelationBase{
    private Integer id;
    private String apiKey;
    private Date createTime;

    public RoleGroupRelation() {
    }

    public RoleGroupRelation(Integer id, String apiKey, Date createTime) {
        this.id = id;
        this.apiKey = apiKey;
        this.createTime = createTime;
    }
    public RoleGroupRelation(Integer id, String apiKey, Date createTime, Integer roleId, Integer gid, Boolean configAuth, Boolean dataAuth) {
        super(roleId, gid, configAuth, dataAuth);
        this.id = id;
        this.apiKey = apiKey;
        this.createTime = createTime;
    }
}
