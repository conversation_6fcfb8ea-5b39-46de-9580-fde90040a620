package com.databuff.entity.enums;

import lombok.Getter;

/**
 * 指标类型枚举
 * 标识数据来源
 */
@Getter
public enum MetricTypeEnum {
    
    // 原始指标 (Raw Metrics)
    RAW_SYSTEM("RAW_SYSTEM", "系统指标"),           // CPU、内存、磁盘、网络等直接从系统采集的指标
    RAW_APPLICATION("RAW_APPLICATION", "应用指标"),   // JVM堆内存、线程数、GC时间等应用运行时指标
    RAW_MIDDLEWARE("RAW_MIDDLEWARE", "中间件指标"), // 数据库连接数、缓存命中率、消息队列深度等
    RAW_BUSINESS("RAW_BUSINESS", "业务原始指标"), // 登录次数、订单数、页面访问量等
    
    // 衍生指标 (Derived Metrics)
    DERIVED_RATIO("DERIVED_RATIO", "比率类"),           // 错误率、成功率、利用率等
    DERIVED_STATISTICAL("DERIVED_STATISTICAL", "统计类"), // 平均值、中位数、百分位数等
    DERIVED_TIME_WINDOW("DERIVED_TIME_WINDOW", "时间窗口类"), // 变化率、趋势、同比环比等
    DERIVED_COMPOSITE("DERIVED_COMPOSITE", "复合类"),    // 健康分数、SLI指标等
    DERIVED_AGGREGATION("DERIVED_AGGREGATION", "聚合类"), // 集群级、服务级、应用级聚合指标
    
    // 预测指标 (Predictive Metrics)
    PREDICTED_TREND("PREDICTED_TREND", "趋势预测"),     // 基于历史数据预测未来趋势
    PREDICTED_ANOMALY("PREDICTED_ANOMALY", "异常预测"),   // 预测可能出现的异常情况
    PREDICTED_CAPACITY("PREDICTED_CAPACITY", "容量预测"), // 预测资源使用和需求
    PREDICTED_LIFETIME("PREDICTED_LIFETIME", "寿命预测"), // 预测组件或系统的剩余寿命
    
    // 关联指标 (Correlation Metrics)
    CORRELATION_CAUSAL("CORRELATION_CAUSAL", "因果关联"),       // 表示因果关系的指标
    CORRELATION_COEFFICIENT("CORRELATION_COEFFICIENT", "相关性指标"), // 表示不同指标间相关程度
    CORRELATION_IMPACT("CORRELATION_IMPACT", "影响度指标"),     // 量化某指标对其他指标的影响
    CORRELATION_DEPENDENCY("CORRELATION_DEPENDENCY", "依赖链指标"), // 服务调用链路上的关联指标
    
    // 上下文指标 (Contextual Metrics)
    CONTEXTUAL_ENVIRONMENT("CONTEXTUAL_ENVIRONMENT", "环境指标"), // 描述系统运行环境的指标
    CONTEXTUAL_CONFIGURATION("CONTEXTUAL_CONFIGURATION", "配置指标"), // 描述系统配置状态的指标
    CONTEXTUAL_CHANGE("CONTEXTUAL_CHANGE", "变更指标"),       // 描述系统变更情况的指标
    CONTEXTUAL_EVENT("CONTEXTUAL_EVENT", "事件指标"),         // 与特定事件相关的指标
    
    // 业务价值指标 (Business Value Metrics)
    BUSINESS_USER_EXPERIENCE("BUSINESS_USER_EXPERIENCE", "用户体验指标"), // Apdex分数、用户满意度等
    BUSINESS_IMPACT("BUSINESS_IMPACT", "业务影响指标"),               // 收入影响、用户流失等
    BUSINESS_SLA("BUSINESS_SLA", "SLA/SLO指标"),                        // 服务水平目标达成情况
    BUSINESS_COST_EFFICIENCY("BUSINESS_COST_EFFICIENCY", "成本效益指标"),   // 资源利用效率、成本分摊等
    
    // 合成指标 (Synthetic Metrics)
    SYNTHETIC_USER("SYNTHETIC_USER", "模拟用户指标"),         // 通过模拟用户行为获取的指标
    SYNTHETIC_BLACKBOX("SYNTHETIC_BLACKBOX", "黑盒测试指标"),   // 从外部测试获取的系统表现指标
    SYNTHETIC_BENCHMARK("SYNTHETIC_BENCHMARK", "基准测试指标"), // 与标准基准比较的性能指标
    SYNTHETIC_STRESS("SYNTHETIC_STRESS", "压力测试指标"),     // 在压力条件下的系统表现指标
    
    // 兼容旧版本的枚举值
    ORIGINAL("ORIGINAL", "原始采集"),
    DERIVED("DERIVED", "衍生计算"),
    PREDICTED("PREDICTED", "预测"),
    SYNTHETIC("SYNTHETIC", "合成");
    
    private final String code;
    private final String name;
    
    MetricTypeEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }
    
    /**
     * 根据编码获取枚举
     */
    public static MetricTypeEnum getByCode(String code) {
        if (code == null) {
            return null;
        }
        for (MetricTypeEnum type : values()) {
            if (type.getCode().equals(code)) {
                return type;
            }
        }
        return null;
    }
    
    /**
     * 根据名称获取枚举
     */
    public static MetricTypeEnum getByName(String name) {
        if (name == null) {
            return null;
        }
        for (MetricTypeEnum type : values()) {
            if (type.getName().equals(name)) {
                return type;
            }
        }
        return null;
    }
}
