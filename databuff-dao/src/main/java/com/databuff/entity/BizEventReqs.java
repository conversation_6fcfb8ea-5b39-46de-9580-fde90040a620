package com.databuff.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;

import javax.persistence.Column;
import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @description 业务事件关联请求
 */
@ToString
@Data
@ApiModel("业务事件关联请求")
public class BizEventReqs implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 服务或应用id
     */
    @ApiModelProperty("服务或应用id")
    private String svcId;

    /**
     * 业务事件名称
     */
    @ApiModelProperty("服务或应用名称")
    private String svcName;

    @ApiModelProperty("服务组件分类 service.rpc,service.http,service.mq,service.redis,service.db,service.es")
    private String componentType;

    @ApiModelProperty("接口资源名称")
    private List<String> resources;


    public BizEventReqs() {
    }

}