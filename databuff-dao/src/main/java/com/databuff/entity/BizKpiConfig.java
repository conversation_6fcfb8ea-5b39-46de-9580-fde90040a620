package com.databuff.entity;


import com.databuff.entity.enums.BusinessFlowType;
import com.databuff.entity.enums.CalculationMethod;
import lombok.Data;

/**
 * KPI 配置类
 */
@Data
public class BizKpiConfig {

    /**
     * 关键绩效指标的名称
     */
    private String kpiName;

    /**
     * 计算方法 ("SUM", "FIRST", "LAST")
     */
    private CalculationMethod calculationMethod;

    /**
     * kpi指标值的显示单位
     */
    private String kpiUnit;

    /**
     * 来源业务事件 ID
     */
    private String sourceEventId;

    /**
     * 来源属性 Key
     */
    private String sourceAttributeKey;

    /**
     * 业务流类型 ("FULFILLMENT" 订单履行, "CONVERSION" 业务转化, "OTHER" 其他)
     */
    private BusinessFlowType businessFlowType;

    /**
     * 自定类型名称 (只有 businessFlowType 为 "OTHER" 时需要)
     */
    private String otherFlowTypeName;

    /**
     * 业务流统计指标值的显示单位
     */
    private String businessUnit;

}
