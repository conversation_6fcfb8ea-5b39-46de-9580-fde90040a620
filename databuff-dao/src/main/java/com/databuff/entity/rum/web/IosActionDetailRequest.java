package com.databuff.entity.rum.web;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@ApiModel("iOS崩溃详情请求参数")
public class IosActionDetailRequest extends IosBaseDetailRequest {
    @ApiModelProperty(value = "崩溃ID", required = true)
    private Long actionId;
}
