package com.databuff.entity.rum.web;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
public class RumUserListRequest extends RumBaseRequest {
    @ApiModelProperty(value = "用户ID", example = "user123")
    private String userId;

    @ApiModelProperty(value = "页码", example = "1")
    protected Integer pageNum = 1;

    @ApiModelProperty(value = "分页数", example = "10")
    protected Integer pageSize = 10;
}
