package com.databuff.entity.rum.starrocks;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@TableName("dc_rum_ios_anr")
public class RumIosAnr extends BaseRumIosError {
    @ApiModelProperty(value = "卡顿ID")
    @JSONField(name = "anr_id")
    private Long anrId;

    @ApiModelProperty(value = "卡顿名称")
    @JSONField(name = "anr_name")
    private String anrName;
}
