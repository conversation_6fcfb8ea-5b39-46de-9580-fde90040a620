package com.databuff.entity.rum;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;


@Data
public class OperationMetric extends MetricBase {
    private String operationName;
    private Date operationTime;
    //以下Fields (度量值):
    @ApiModelProperty(value = "操作时间(耗时ns)")
    private Long actionDuration;

    @ApiModelProperty(value = "操作请求耗时(ns)")
    private Long actionRequestDuration;

    @ApiModelProperty(value = "服务端平均耗时")
    private Long actionServiceDuration;
    private String publicIp; // 可以后端解析填充
    private String region; // 同上，根据IP解析
    private String isp; // 同上
}
