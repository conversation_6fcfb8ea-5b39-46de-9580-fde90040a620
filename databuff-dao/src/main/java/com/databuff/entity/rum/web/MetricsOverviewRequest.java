package com.databuff.entity.rum.web;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

@Data
public class MetricsOverviewRequest implements MetricsOverviewRequestBase {
    private String apiKey;
    private Integer appId;
    private Date fromTime;
    private Date toTime;
    @ApiModelProperty(value = "间隔(秒)", example = "60")
    private Integer interval;

    @ApiModelProperty(value = "字段", example = "lcp ,cls ,fid, fcp, actionDuration, slowPageRate, jsErrorRate, actionAvailability")
    private String field;

    @ApiModelProperty(value = "聚合函数", example = "lcp ,cls ,fid, fcp, actionDuration 才需要avg,upper")
    private String aggregation;

    @ApiModelProperty(value = "upper函数", example = "有传upper 才需要传99,95,90,75,50")
    private Integer upperNumber;

}
