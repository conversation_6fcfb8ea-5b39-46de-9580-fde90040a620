package com.databuff.entity.rum.moredb;

import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Map;

@Data
@TableName("rum.web.request.percentile")
public class RumWebRequestPercentile {

    @ApiModelProperty("用户 api key")
    private String apiKey;

    @ApiModelProperty(value = "事件发生时间")
    private long startTime;

    // Tags (索引):
    @ApiModelProperty(value = "应用id")
    private String appId;

    @ApiModelProperty(value = "应用名称")
    private String appName;

    @ApiModelProperty(value = "请求类型: 0:ajax请求 1:静态资源请求")
    private Integer requestType;

    @ApiModelProperty(value = "处理过的请求URL")
    private String processedHttpUrl;

    @ApiModelProperty(value = "域名")
    private String domain;

    @ApiModelProperty(value = "处理过的路径")
    private String processedPath;

    @ApiModelProperty(value = "运营商")
    private String isp;

    @ApiModelProperty(value = "状态码")
    private String statusCode;

    @ApiModelProperty(value = "调用服务")
    private String service;

    @ApiModelProperty(value = "duration")
    private String type;

    // Fields (度量值):

    @ApiModelProperty(value = "type对应值")
    private Long metric;

    // Add other necessary fields for histogram calculation
    // These fields will be used by MoreDB to calculate percentiles
    @ApiModelProperty(value = "Histogram count")
    private Long histogramCount;

    @ApiModelProperty(value = "Histogram min")
    private Long histogramMin;

    @ApiModelProperty(value = "Histogram max")
    private Long histogramMax;

    @ApiModelProperty(value = "Histogram sum")
    private Long histogramSum;

    private Map<Integer, Long> percentileAgg;
}
