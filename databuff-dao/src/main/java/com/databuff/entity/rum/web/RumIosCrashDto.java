package com.databuff.entity.rum.web;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@TableName("dc_rum_ios_crash")
public class RumIosCrashDto extends BaseRumIosErrorDto {
    @ApiModelProperty(value = "崩溃ID")
    @JSONField(name = "crash_id")
    private String crashId;

    @ApiModelProperty(value = "崩溃名称")
    @JSONField(name = "crash_name")
    private String crashName;

    @ApiModelProperty(value = "崩溃类型")
    @JSONField(name = "crash_type")
    private String crashType;
}
