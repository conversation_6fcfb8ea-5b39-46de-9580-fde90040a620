package com.databuff.entity.rum.starrocks;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

@Data
@TableName("dc_rum_web_request_uv")
public class RumWebRequestUV {

    @ApiModelProperty(value = "请求发生时间", example = "2023-10-01 12:00:00")
    @JSONField(name = "startTime", format="yyyy-MM-dd HH:mm:ss")
    private Date startTime;

    @ApiModelProperty(value = "应用id", example = "12345")
    @JSONField(name = "app_id")
    private Integer appId;

    @ApiModelProperty(value = "是否错误", example = "0")
    @JSONField(name = "is_error")
    private Integer isError;

    @ApiModelProperty(value = "请求类型", example = "0")
    @JSONField(name = "request_type")
    private Integer requestType;

    @ApiModelProperty(value = "域名", example = "example.com")
    private String domain;

    @ApiModelProperty(value = "处理过的路径", example = "/api/v1/users")
    @JSONField(name = "processed_path")
    private String processedPath;

    @ApiModelProperty(value = "处理过的请求URL", example = "/api/v1/users")
    @JSONField(name = "processed_http_url")
    private String processedHttpUrl;

    @ApiModelProperty(value = "运营商", example = "China Mobile")
    private String isp;

    @ApiModelProperty(value = "状态码", example = "200")
    @JSONField(name = "status_code")
    private String statusCode;

    @ApiModelProperty(value = "调用服务", example = "user-service")
    private String service;

    @ApiModelProperty(value = "租户 api key id", example = "1")
    @JSONField(name = "df_api_key_id")
    private Integer dfApiKeyId;

    @ApiModelProperty(value = "用户ID")
    @JSONField(name = "user_id")
    @TableField(exist = false)
    private String userId;

    @ApiModelProperty(value = "用来计算uv")
    private String uv;
}
