package com.databuff.entity.rum.web;

import com.databuff.entity.rum.mysql.RumLicense;

import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;

/**
 * <p>
 * <strong>AuthResourceKeyBuilder</strong> 统一管理 RUM授权 中所有 Redis Key 的命名规范。
 * <br/>
 * ## 1. 何为多授权并行
 *
 * <p>
 * <strong>场景</strong>：同一应用在同一时间段内拥有多条有效授权（如<strong>授权A</strong>：订阅月均、<strong>授权B</strong>：买断、<strong>授权C</strong>：订阅年包…）。
 * <br/>
 * <strong>目标</strong>：当应用有"新设备"或"新PV"上报时，只要能从任意授权里扣减成功，都算"授权通过"。若所有授权都无剩余(或受限等原因无法扣减)，则设备/请求判定"失败/未授权"。
 * </p>
 *
 * <p>
 * 与单一授权相比，"多授权并行"在<strong>扣减逻辑</strong>和<strong>限额交互</strong>上更为复杂，需要明确优先级、每日/月度限额叠加处理等细节。
 * </p>
 *
 * <h2>2. 多授权运作方式</h2>
 *
 * <h3>2.1 优先级与分配策略</h3>
 *
 * <h4>1. 顺序尝试（典型做法）</h4>
 * <ul>
 *   <li>后台配置一份"授权优先级"列表，如：先用<strong>授权A</strong> → 若已满或不可用，再尝试<strong>授权B</strong> → … 直到找到可扣减的授权；</li>
 *   <li>一旦在某个授权上 used++ 成功，<strong>立即</strong>结束分配，判定本次"成功"。</li>
 *   <li><strong>好处</strong>：避免一台设备重复消耗多份授权配额，易于理解、实现简单；</li>
 *   <li><strong>潜在问题</strong>：如果授权A 月度限额很小，却总被优先尝试，可能导致很多设备都在A上失败，再转到B，或浪费性能；具体看商务需求。</li>
 * </ul>
 *
 * <h4>2. 并行拆分（不常见）</h4>
 * <ul>
 *   <li>也有业务希望"同一个设备可以同时用两个授权"，但这通常会<strong>重复扣费</strong>，大部分产品并不采用。</li>
 * </ul>
 *
 * <p>
 * <strong>本需求</strong>默认采纳"顺序尝试"模式：找到首个可用的授权就"成功"，其余授权<strong>不再扣减</strong>。
 * </p>
 *
 * <h3>2.2 各授权自身的周期与限额</h3>
 *
 * <h4>1. 订阅（月均/年包）</h4>
 * <ul>
 *   <li>在授权周期内(如 2024/6/1~2025/6/1)，每到月初(或年包分摊后的月初)重置其 used=0；</li>
 *   <li>若本月分配完后(如月均 10万月活已用光)，则无法再扣减此授权，需尝试下一个授权。</li>
 * </ul>
 *
 * <h4>2. 买断</h4>
 * <ul>
 *   <li>一次性总量，不会按月自动重置 used；只要总量内还够，就可扣减；用完则无法再扣减。</li>
 *   <li>也可能在后台配置"买断 10万总量拆成每月1万"以做技术限流，但<strong>本质</strong>仍是一份买断授权。</li>
 * </ul>
 *
 * <h3>2.3 与应用采样率、日/月限额的关系</h3>
 *
 * <p>设备/请求先经过应用级限制：</p>
 * <ol>
 *   <li>检查<strong>应用采样率</strong>(如 10%)：若抽到"失败"，则直接不授权；若抽到"成功"，进入下一步</li>
 *   <li>检查<strong>日限额</strong>(如每日最多 100 个新设备)：若已满，判定失败；否则继续</li>
 *   <li>检查<strong>月限额</strong>(如本月最多 1000 个新设备)：若已满，判定失败；否则继续</li>
 *   <li><strong>若以上都通过</strong> → 再去多授权分配</li>
 * </ol>
 *
 * <p>多授权分配：</p>
 * <ol>
 *   <li><strong>授权A</strong>：若其本月(或总量)未满 → 扣减 usedA++ → 成功结束；若满 → 尝试<strong>授权B</strong></li>
 *   <li><strong>授权B</strong>：若 B 未满 → usedB++，成功结束；若满 → 尝试<strong>授权C</strong></li>
 *   <li>… 直到所有授权都无法扣减 → 判定最终失败</li>
 * </ol>
 *
 * <p><strong>结果</strong>：如果设备最终使用到某个授权，就在<strong>当日/当月 allowedSet</strong>(或计数器)里+1；否则放入 failed 统计。</p>
 * <p><strong>注意</strong>：只要<strong>任意授权</strong>成功，即可算"放行"，不会二次扣其他授权。</p>
 *
 * <h3>2.4 每条授权的「可用量」计算</h3>
 *
 * <h4>1. 月均</h4>
 * <ul>
 *   <li>按<strong>起止日期</strong>天数 → 先算<strong>日均</strong>（授权总量 ÷ 总天数），再乘以<strong>当月天数</strong>(该当月若是<strong>完整月</strong>以起始日期对应日为界，若是<strong>自然月</strong>以1号~月底)向上取整，得到该月可用量。</li>
 *   <li>若授权期限非整月(如 2025/1/15 ~ 2026/1/10)，每个<strong>完整月</strong>或<strong>不完整月</strong>都需按实际天数去算。</li>
 * </ul>
 *
 * <h4>2. 年包</h4>
 * <ul>
 *   <li>视为"完整年"池，但若授权实际跨<strong>不整年</strong>（如 2025/1/2 ~ 2026/6/30 超过1年半），也先算<strong>日均</strong>= (总量 ÷ 总天数)。</li>
 *   <li><strong>每年</strong>(或每个整年区段)的配额 = 日均 ×(该年内的天数) 向上取整；若超过一年仍剩余时，再算第2年（或余下天数）。</li>
 *   <li>例如：2025/1/2 ~ 2026/6/30 → 先算2025/1/2 ~2026/1/2 (1年)配额，再算 2026/1/3~6/30(半年)的配额。</li>
 * </ul>
 *
 * <h4>3. 买断</h4>
 * <ul>
 *   <li><strong>默认</strong>一次性"总量= X"，不做月度/年度拆分；用完即止。</li>
 *   <li>若要避免在某月用太多，可在 RUM 资源管理中配置"月/日限额"，或技术上同样用<strong>日均</strong>公式对买断做拆分限制（非默认做法）。</li>
 * </ul>
 *
 * <p><strong>注意</strong>：以上仅是计算"<strong>授权本身</strong>"的月/年可用量，用于在后台判断 used < limit。真正<strong>设备月活</strong>何时重置，取决于 <strong>RUM 模块月活刷新</strong>(2. 授权月活 / 授权 PV)。</p>
 *
 * <h2>3. Redis Key 命名规范</h2>
 *   <ul>
 *     <li>(A) 应用级 Key: day/ month + (succSet/ failSet/ allCount)
 *       <ul>
 *         <li>日级成功集合(设备): rum:day:{appId}:device:20240315:succSet</li>
 *         <li>日级成功集合(页面): rum:day:{appId}:page:20240315:succSet</li>
 *         <li>日级失败集合(设备): rum:day:{appId}:device:20240315:failSet</li>
 *         <li>日级失败集合(页面): rum:day:{appId}:page:20240315:failSet</li>
 *         <li>月级成功集合(设备): rum:month:{appId}:device:202403:succSet</li>
 *         <li>月级成功集合(页面): rum:month:{appId}:page:202403:succSet</li>
 *         <li>月级失败集合(设备): rum:month:{appId}:device:202403:failSet</li>
 *         <li>月级失败集合(页面): rum:month:{appId}:page:202403:failSet</li>
 *         <li>月度总计数(设备): rum:month:{appId}:device:202403:allCount</li>
 *         <li>月度总计数(页面): rum:month:{appId}:page:202403:allCount</li>
 *       </ul>
 *     </li>
 *     <li>(B) 多授权 Key: buyout/ subscription(year or month or fullmonth)
 *       <ul>
 *         <li>买断授权(设备): rum:buyout:license:123:device:used</li>
 *         <li>买断授权(页面): rum:buyout:license:123:page:used</li>
 *         <li>年包订阅(设备): rum:year:license:123:device:20240301:used</li>
 *         <li>年包订阅(页面): rum:year:license:123:page:20240301:used</li>
 *         <li>自然月订阅(设备): rum:month:license:123:device:202403:used</li>
 *         <li>自然月订阅(页面): rum:month:license:123:page:202403:used</li>
 *         <li>完整月订阅(设备): rum:fullmonth:license:123:device:20240315:used</li>
 *         <li>完整月订阅(页面): rum:fullmonth:license:123:page:20240315:used</li>
 *       </ul>
 *     </li>
 *
 *     更新设置 如果放宽权限 webapp写key 通知dts删除缓存"config_amnesty:appId_%d:dim_%s"
 *   </ul>
 * </p>
 */
public class AuthResourceKeyBuilder {

    /**
     * 构造多授权的 usedKey
     * - 买断 => "rum:buyout:license:{licenseId}:{dimension}:used"
     * - 订阅+年包 => "rum:year:license:{licenseId}:{dimension}:{yyyyMMdd}:used"
     * - 订阅+月均+自然月 => "rum:month:license:{licenseId}:{dimension}:{yyyyMM}:used"
     * - 订阅+月均+完整月 => "rum:fullmonth:license:{licenseId}:{dimension}:{yyyyMMdd}:used"
     */
    public static String buildLicenseUsedKeyForCurrentCycle(RumLicense lic, AuthDimensionEnum dimension) {
        int coop = lic.getCooperationModel(); // 0=订阅,1=买断
        int dist = lic.getAuthDistribution(); // 0=月均,1=年包
        int rule = lic.getMonthRule();        // 0=自然月,1=完整月
        long lid = lic.getLicenseId();

        if (CooperationModelEnum.BUYOUT.getCode().equals(coop)) {
            // 买断
            return buildBuyoutLicenseKey(lid, dimension);
        } else {
            // 订阅
            if (AuthDistributionEnum.YEARLY_PACKAGE.getCode().equals(dist)) {
                return buildYearLicenseKey(lid, dimension, lic.getStartTime());
            } else {
                if (MonthRuleEnum.NATURAL_MONTH.getCode().equals(rule)) {
                    String ym = getNowYYYYMM();
                    return buildMonthLicenseKey(lid, dimension, ym);
                } else {
                    return buildFullMonthLicenseKey(lid, dimension, lic.getStartTime());
                }
            }
        }
    }


    // ================================
    //    (A) 应用级 Key
    // ================================


    /**
     * <p>
     * 日级-成功Key:
     * <br/>
     * 格式：<code>rum:day:{appId}:{dimension}:{yyyyMMdd}:succSet</code>
     * <br/>
     * 参考需求文档 §3 “应用采样率/日限额”说明：
     *  <ul>
     *   <li>每个应用每天都在 0点 重置；</li>
     *   <li>对“新设备/新PV”进行抽样或限额判断，通过后放入“succSet”。</li>
     *  </ul>
     * </p>
     *
     * @param appId     应用ID
     * @param dimension 维度: device/page/...
     * @param ymd       日期yyyyMMdd
     * @return Redis Key
     */
    public static String buildDaySuccKey(int appId, AuthDimensionEnum dimension, String ymd) {
        return String.format("rum:day:%d:%s:%s:succSet", appId, dimension.getCode(), ymd);
    }

    /**
     * <p>
     * 日级-失败Key:
     * <br/>
     * 格式：<code>rum:day:{appId}:{dimension}:{yyyyMMdd}:failSet</code>
     * <br/>
     * 参考需求文档 §3 “应用采样率/日限额”说明：
     *  <ul>
     *   <li>若抽样失败或超出日限额，则放入“failSet”。</li>
     *   <li>同一天内同一个设备/页面只需判定一次，若失败则该日不再重新授权。</li>
     *  </ul>
     * </p>
     *
     * @param appId     应用ID
     * @param dimension 维度: device/page/...
     * @param ymd       日期yyyyMMdd
     * @return Redis Key
     */
    public static String buildDayFailKey(int appId, AuthDimensionEnum dimension, String ymd) {
        return String.format("rum:day:%d:%s:%s:failSet", appId, dimension.getCode(), ymd);
    }

    /**
     * <p>
     * 月级-成功Key:
     * <br/>
     * 格式：<code>rum:month:{appId}:{dimension}:{yyyyMM}:succSet</code>
     * <br/>
     * 参考需求文档 §3 “月限额” 说明 + §2 “授权月活 / 授权PV”：
     *  <ul>
     *    <li>自然月(1号)或完整月(在后台记录)都会在新周期重置统计。</li>
     *    <li>若本月已经成功授权过，则不用重复扣减(视为旧设备/旧PV)。</li>
     *  </ul>
     * </p>
     *
     * @param appId     应用ID
     * @param dimension 维度: device/page/...
     * @param ym        年月yyyyMM
     * @return Redis Key
     */
    public static String buildMonthSuccKey(int appId, AuthDimensionEnum dimension, String ym) {
        return String.format("rum:month:%d:%s:%s:succSet", appId, dimension.getCode(), ym);
    }

    /**
     * <p>
     * 月级-失败Key:
     * <br/>
     * 格式：<code>rum:month:{appId}:{dimension}:{yyyyMM}:failSet</code>
     * <br/>
     * 参考需求文档 §3 “月限额” 说明：
     *  <ul>
     *    <li>当超出月限额(或无可用授权)时，将设备记为“failSet”。</li>
     *    <li>后续同一设备若仍在本月，算旧设备不会再扣减。</li>
     *  </ul>
     * </p>
     */
    public static String buildMonthFailKey(int appId, AuthDimensionEnum dimension, String ym) {
        return String.format("rum:month:%d:%s:%s:failSet", appId, dimension.getCode(), ym);
    }

    /**
     * <p>
     * 月级-出现过的设备总数计数Key:
     * <br/>
     * 格式：<code>rum:month:{appId}:{dimension}:{yyyyMM}:allCount</code>
     * <br/>
     * 参考需求文档 §6 “本月采样率计算”：
     *  <ul>
     *    <li>分母=本月出现的全部新设备/新PV（无论成功或失败都计入）。</li>
     *    <li>本 Key 通常是一个数字(如String/Json存储)或可用Redis的 incr计数。</li>
     *  </ul>
     * </p>
     */
    public static String buildMonthAllKey(int appId, AuthDimensionEnum dimension, String ym) {
        return String.format("rum:month:%d:%s:%s:allCount", appId, dimension.getCode(), ym);
    }

    // 设备维度的月度成功Key
    public static String buildMonthDeviceSuccKey(int appId, String ym) {
        return buildMonthSuccKey(appId, AuthDimensionEnum.DEVICE, ym);
    }

    // 页面维度的月度成功Key
    public static String buildMonthPageSuccKey(int appId, String ym) {
        return buildMonthSuccKey(appId, AuthDimensionEnum.PAGE, ym);
    }

    // 设备维度的月度总数Key
    public static String buildMonthDeviceAllKey(int appId, String ym) {
        return buildMonthAllKey(appId, AuthDimensionEnum.DEVICE, ym);
    }

    // 页面维度的月度总数Key
    public static String buildMonthPageAllKey(int appId, String ym) {
        return buildMonthAllKey(appId, AuthDimensionEnum.PAGE, ym);
    }


    // ================================
    //    (B) 多授权 Key
    // ================================

    /**
     * <p>
     * 买断授权Key:
     * <br/>
     * 格式：<code>rum:buyout:license:{licenseId}:{dimension}:used</code>
     * <br/>
     * 参考需求文档 §2 “买断”，§4 "多授权并行",
     *   <ul>
     *    <li>一次性总量，用完即止，不会自动重置。</li>
     *   </ul>
     * </p>
     *
     * @param licenseId License ID
     * @param dimension page 或 device
     * @return Redis Key
     */
    public static String buildBuyoutLicenseKey(long licenseId, AuthDimensionEnum dimension) {
        return String.format("rum:buyout:license:%d:%s:used", licenseId, dimension.getCode());
    }

    /**
     * <p>
     * 订阅-年包 Key:
     * <br/>
     * 格式：<code>rum:year:license:{licenseId}:{dimension}:{yyyyMMdd}:used</code>
     * <br/>
     * 参考需求文档 §1.1 “订阅-年包” & §4 “多授权并行”：
     *   <ul>
     *     <li>每年(或自定义完整年周期)自动重置 used=0</li>
     *     <li>Key中 yyyyMMdd 来自启动周期</li>
     *   </ul>
     * </p>
     *
     * @param licenseId   License ID
     * @param dimension   page 或 device
     * @param startMillis License startTime
     * @return Redis Key
     */
    public static String buildYearLicenseKey(long licenseId, AuthDimensionEnum dimension, long startMillis) {
        String ymd = new SimpleDateFormat("yyyyMMdd").format(new Date(startMillis));
        return String.format("rum:year:license:%d:%s:%s:used", licenseId, dimension.getCode(), ymd);
    }


    /**
     * 构造 monthly license Key
     * e.g. "rum:month:license:{licenseId}:{dimension}:{yyyyMM}:used"
     */
    public static String buildMonthLicenseKey(long licenseId, AuthDimensionEnum dimension, String ym) {
        return String.format("rum:month:license:%d:%s:%s:used", licenseId, dimension.getCode(), ym);
    }

    /**
     * <p>
     * 订阅-月均(自然月) Key:
     * <br/>
     * 格式：<code>rum:month:license:{licenseId}:{dimension}:{yyyyMM}:used</code>
     * <br/>
     * 参考需求文档 §1.1 “订阅-月均(自然月)” & §4 “多授权并行”：
     *   <ul>
     *     <li>每个月 1 号 0 点自动重置 used=0</li>
     *   </ul>
     * </p>
     *
     * @param licenseId License ID
     * @param nowMillis 当前时间(用于格式化 yyyyMM)
     * @return Redis Key
     */
    public static String buildMonthLicenseKey(long licenseId, AuthDimensionEnum dimension, long nowMillis) {
        String ym = new SimpleDateFormat("yyyyMM").format(new Date(nowMillis));
        return String.format("rum:month:license:%d:%s:%s:used", licenseId, dimension.getCode(), ym);
    }

    /**
     * <p>
     * 订阅-月均(完整月) Key:
     * <br/>
     * 格式：<code>rum:fullmonth:license:{licenseId}:{dimension}:{yyyyMMdd}:used</code>
     * <br/>
     * 参考需求文档 §1.1 “订阅-月均(完整月)”，以及多授权并行说明：
     *   <ul>
     *     <li>完整月含义：从 license startDay~startDay+1month。</li>
     *     <li>Key里 {yyyyMMdd} 表示当月周期开始日。</li>
     *   </ul>
     * </p>
     *
     * @param licenseId       License ID
     * @param dimension       page 或 device
     * @param startTimeMillis 授权开始时间
     * @return Redis Key
     */
    public static String buildFullMonthLicenseKey(long licenseId, AuthDimensionEnum dimension, long startTimeMillis) {
        String fullMonthMark = calcFullMonthMark(startTimeMillis);
        return String.format("rum:fullmonth:license:%d:%s:%s:used", licenseId, dimension, fullMonthMark);
    }

    public static String buildAmnestyFlagKey(Integer appId, AuthDimensionEnum dimension) {
        return String.format("config_amnesty:appId_%d:dim_%s", appId, dimension.getCode());
    }

    public static String getNowYYYYMM() {
        return new SimpleDateFormat("yyyyMM").format(new Date());
    }

    /**
     * <p>
     * 用于计算“完整月”周期起始日(yyyyMMdd)：
     * 参考需求文档 §1.1 “订阅-月均(完整月)”
     *   <ul>
     *     <li>例如 startTime=2025-01-15 => 每月15号刷新。</li>
     *     <li>若当前日期2025-09-17 => 本周期标识= 20250915。</li>
     *     <li>若当月只有30天而startDay=31, 则 realDay=30。</li>
     *   </ul>
     * </p>
     *
     * @param startTimeMillis license.getStartTime()
     * @return yyyyMMdd 代表完整月Key
     */
    public static String calcFullMonthMark(long startTimeMillis) {
        Calendar startCal = Calendar.getInstance();
        startCal.setTimeInMillis(startTimeMillis);
        int baseDay = startCal.get(Calendar.DAY_OF_MONTH);

        Calendar nowCal = Calendar.getInstance();
        int maxDay = nowCal.getActualMaximum(Calendar.DAY_OF_MONTH);
        int realDay = Math.min(baseDay, maxDay);
        nowCal.set(Calendar.DAY_OF_MONTH, realDay);

        return new SimpleDateFormat("yyyyMMdd").format(nowCal.getTime());
    }

}
