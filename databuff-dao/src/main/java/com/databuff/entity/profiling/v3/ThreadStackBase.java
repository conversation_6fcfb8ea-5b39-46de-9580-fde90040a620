package com.databuff.entity.profiling.v3;

import com.alibaba.fastjson.annotation.JSONField;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
@ApiModel(description = "线程堆栈信息基座")
public class ThreadStackBase {

    @ApiModelProperty(value = "摘要ID", example = "摘录ID=创建日期（精确到日）+服务ID+服务实例+资源名称+资源类型")
    private String excerptId;

    @ApiModelProperty(value = "资源名称", example = "GET /methodB0")
    private String resource;

    @ApiModelProperty(value = "资源类型", example = "http/rpc")
    private String rsType;

    @ApiModelProperty(value = "frameTypeIds", example = "11111111111111111111111111111111111111111111111111111111111111111111111111")
    @JSONField(name = "frameTypeIds")
    private String frameTypeIds;

    @ApiModelProperty(value = "堆栈跟踪信息", example = "[\"com.zaxxer.hikari.pool.ProxyConnection.close\", \"org.springframework.jdbc.datasource.DataSourceUtils.doCloseConnection\", \"java.util.concurrent.ThreadPoolExecutor$Worker.run\", \"org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run\"]")
    private List<String> stackTraces;

}