package com.databuff.entity;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.annotation.TableName;
import com.databuff.entity.enums.AggregationMethodEnum;
import com.databuff.entity.enums.BasicJudgmentEnum;
import com.databuff.entity.enums.MetricSourceEnum;
import com.databuff.entity.enums.MetricTypeEnum;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Arrays;
import java.util.Collection;
import java.util.HashMap;
import java.util.Map;
import java.util.stream.Collectors;

@Data
@Builder
@TableName("dc_databuff_metrics_core")
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class MetricsCore {

    @ApiModelProperty(value = "下一个指标")
    private MetricsCore next;

    public boolean hasNext() {
        return next != null;
    }

    /**
     * 设置下一个 MetricsCore 节点。
     * 如果当前节点已有后续节点，则将新节点插入到当前节点之后。
     * 注意：此方法不允许设置自身或形成循环引用。
     */
    public void setNext(MetricsCore next) {
        if (next == null) {
            return;
        }

        // 不允许设置自己为下一个节点
        if (next == this) {
            return;
        }

        // 检查是否形成循环引用（包括间接循环）
        MetricsCore current = next;
        int depth = 0;
        final int MAX_DEPTH = 100; // 防止无限循环检测
        while (current.getNext() != null) {
            current = current.getNext();
            if (current == this || current == next) {
                // 检测到环形引用
                throw new IllegalArgumentException("不允许设置会导致循环引用的节点");
            }
            if (++depth > MAX_DEPTH) {
                throw new IllegalArgumentException("检测到疑似无限链表，请检查输入节点");
            }
        }

        if (this.next != null) {
            this.next.setNext(next);
        }

        this.next = next;
    }

    @ApiModelProperty(value = "主键ID")
    private Long id;

    @ApiModelProperty(value = "类型1")
    private String type1;

    @ApiModelProperty(value = "类型2")
    private String type2;

    @ApiModelProperty(value = "类型3")
    private String type3;

    @ApiModelProperty(value = "应用")
    private String app;

    @ApiModelProperty(value = "数据库")
    private String database;

    @ApiModelProperty(value = "测量")
    private String measurement;

    @ApiModelProperty(value = "描述")
    private String desc;

    @ApiModelProperty(value = "维度key")
    private JSONObject tagKey;

    @ApiModelProperty(value = "维度value")
    private JSONObject tagValue;

    @ApiModelProperty(value = "字段")
    private JSONObject fields;

    @ApiModelProperty(value = "是否启用")
    private Boolean isOpen;

    @ApiModelProperty(value = "指标类型", notes = "标识数据来源(原始采集/衣生计算/预测/合成等)")
    private MetricTypeEnum metricType;

    @ApiModelProperty(value = "指标来源", notes = "预置/自定义/第三方平台等")
    private MetricSourceEnum metricSource;

    @ApiModelProperty(value = "基本判断", notes = "高好/低好等指标值的判断方向")
    private BasicJudgmentEnum basicJudgment;

    @ApiModelProperty(value = "聚合方式", notes = "平均，求和，最大值，75分位等")
    private Collection<AggregationMethodEnum> aggregationMethod;

    @ApiModelProperty(value = "是否内置指标", notes = "内置指标不需要用户手动创建，由系统自动创建")
    public Boolean builtin;

    // 添加getter方法，提供默认值
    public MetricTypeEnum getMetricType() {
        return metricType != null ? metricType : MetricTypeEnum.ORIGINAL;
    }

    public MetricSourceEnum getMetricSource() {
        return metricSource != null ? metricSource : MetricSourceEnum.PRESET;
    }

    public BasicJudgmentEnum getBasicJudgment() {
        return basicJudgment != null ? basicJudgment : BasicJudgmentEnum.HIGH_GOOD;
    }


    public static MetricsCore build(String metric) {
        if (metric == null || !metric.contains(".")) {
            throw new IllegalArgumentException("无效的指标格式");
        }

        // metric 是一个字符串，格式为：database.measurement.field
        final String[] parts = metric.split("\\.");
        final String database = parts[0];
//        final String field = parts[parts.length - 1];

        // measurement 是中间所有字符串
        final String measurement = Arrays.stream(parts, 1, parts.length - 1)
                .collect(Collectors.joining("."));

        return MetricsCore.builder()
                .database(database)
                .measurement(measurement)
//                .fields(field)
                .build();
    }

    public static MetricsCore build(String metric, String type1, String type2, String type3) {
        final MetricsCore metricsCore = MetricsCore.builder()
                .type1(type1)
                .type2(type2)
                .type3(type3)
                .build();
        if (metric == null || !metric.contains(".")) {
            return metricsCore;
        } else {
            // metric 是一个字符串，格式为：database.measurement.field
            final String[] parts = metric.split("\\.");
            final String database = parts[0];

            // measurement 是中间所有字符串
            final String measurement = Arrays.stream(parts, 1, parts.length - 1)
                    .collect(Collectors.joining("."));
            metricsCore.setDatabase(database);
            metricsCore.setMeasurement(measurement);
            return metricsCore;
        }
    }

    public Map<String, MetricsQuery> toMetricsQueryMap() {
        Map<String, MetricsQuery> metricsQueryMap = new HashMap<>();
        if (this.hasNext()) {
            metricsQueryMap.putAll(this.getNext().toMetricsQueryMap());
        }
        JSONObject fields = this.getFields();
        String measurement = this.getMeasurement();
        for (Map.Entry<String, Object> objectEntry : fields.entrySet()) {
            final String field = objectEntry.getKey();
            // 处理值可能是JSONObject或Map的情况
            JSONObject fieldInfo;
            Object value = objectEntry.getValue();
            if (value instanceof JSONObject) {
                fieldInfo = (JSONObject) value;
            } else if (value instanceof Map) {
                // 如果是Map类型，转换为JSONObject
                fieldInfo = new JSONObject((Map<String, Object>) value);
            } else {
                continue; // 其他类型则跳过
            }
            if (fieldInfo == null) {
                continue;
            }
            // 安全地获取字段值，处理可能的空值
            String unit = getStringValue(fieldInfo, "unit");
            String unit_cn = getStringValue(fieldInfo, "unit_cn");
            String describe = getStringValue(fieldInfo, "describe");
            String aggregatorType = getStringValue(fieldInfo, "aggregatorType");
            String metricCn = getStringValue(fieldInfo, "metric_cn");
            Double autoFill = getDoubleValue(fieldInfo, "autoFill");

            if (aggregatorType == null) {
                aggregatorType = "avg";
            }

            final String identifier = measurement + "." + field;

            MetricsQuery metricsQuery = new MetricsQuery();
            metricsQuery.setId(this.id);
            metricsQuery.setIdentifier(identifier);
            metricsQuery.setType1(this.type1);
            metricsQuery.setType2(this.type2);
            metricsQuery.setType3(this.type3);
            metricsQuery.setUnit(unit);
            metricsQuery.setUnitCn(unit_cn);
            metricsQuery.setDesc(describe);
            metricsQuery.setApp(this.getApp());
            metricsQuery.setDatabase(this.getDatabase());
            metricsQuery.setTagKey(this.getTagKey());
            metricsQuery.setTagValue(this.getTagValue());
            // 处理fieldValue可能是JSONObject或Map的情况
            Object fieldValue = fieldInfo.get("fieldValue");
            if (fieldValue instanceof JSONObject) {
                metricsQuery.setFieldValue((JSONObject) fieldValue);
            } else if (fieldValue instanceof Map) {
                metricsQuery.setFieldValue(new JSONObject((Map<String, Object>) fieldValue));
            }
            metricsQuery.setMeasurement(measurement);
            metricsQuery.setField(field);
//            metricsQuery.setIsOpen(this.getIsOpen());
            metricsQuery.setAggregatorType(aggregatorType);
            metricsQuery.setMetricCn(metricCn);
            metricsQuery.setCore(true);
            metricsQuery.setBuiltin(this.builtin);
            metricsQuery.setAutoFill(autoFill);

            // 设置新增的属性
            metricsQuery.setMetricType(this.getMetricType());
            metricsQuery.setMetricSource(this.getMetricSource());
            metricsQuery.setBasicJudgment(this.getBasicJudgment());
            metricsQuery.setAggregationMethod(this.aggregationMethod);

            // 代码兼容
            switch (aggregatorType) {
                case "avg":
                case "gauge":
                    metricsQuery.setFormula(String.format("%s(%s)", "avg", "\"" + field + "\""));
                    break;
                default:
                    metricsQuery.setFormula(String.format("%s(%s)", aggregatorType, "\"" + field + "\""));
                    break;
            }

            metricsQueryMap.put(identifier, metricsQuery);
        }

        final JSONObject cnt = fields.getJSONObject("cnt");
        final JSONObject error = fields.getJSONObject("error");
        final JSONObject slow = fields.getJSONObject("slow");
        final JSONObject sumDuration = fields.getJSONObject("sumDuration");

        if (cnt != null) {
            if (sumDuration != null) {
                metricsQueryMap.put(measurement + ".avgDuration", createMetricsQuery("avgDuration", sumDuration.getString("unit"), sumDuration.getString("unit_cn"), "平均耗时", "sum(sumDuration)/sum(cnt)"));
            }
            if (error != null) {
                metricsQueryMap.put(measurement + ".error.pct", createMetricsQuery("error.pct", "percent", "%", "错误率", "(sum(error)/sum(cnt)) * 100"));
                metricsQueryMap.put(measurement + ".success.pct", createMetricsQuery("success.pct", "percent", "%", "成功率", "(1-sum(error)/sum(cnt)) * 100"));
            }
            if (slow != null) {
                metricsQueryMap.put(measurement + ".slow.pct", createMetricsQuery("slow.pct", "percent", "%", "慢比率", "(sum(slow)/sum(cnt)) * 100"));
            }
        }


        return metricsQueryMap;
    }

    private MetricsQuery createMetricsQuery(String identifier, String unit, String unitCn, String desc, String formula) {
        MetricsQuery metricsQuery = new MetricsQuery();
        metricsQuery.setIdentifier(measurement + "." + identifier);
        metricsQuery.setType1(this.type1);
        metricsQuery.setType2(this.type2);
        metricsQuery.setType3(this.type3);
        metricsQuery.setUnit(unit);
        metricsQuery.setUnitCn(unitCn);
        metricsQuery.setDesc(desc);
        metricsQuery.setApp(this.getApp());
        metricsQuery.setDatabase(this.getDatabase());
        metricsQuery.setTagKey(this.getTagKey());
        metricsQuery.setTagValue(this.getTagValue());
        metricsQuery.setMeasurement(measurement);
//        metricsQuery.setIsOpen(this.getIsOpen());
        metricsQuery.setFormula(formula);

        // 设置新增的属性
        metricsQuery.setMetricType(this.getMetricType());
        metricsQuery.setMetricSource(this.getMetricSource());
        metricsQuery.setBasicJudgment(this.getBasicJudgment());
        metricsQuery.setAggregationMethod(this.aggregationMethod);
        return metricsQuery;
    }

    /**
     * 安全地从 JSONObject 中获取字符串值
     *
     * @param json JSONObject对象
     * @param key 要获取的键
     * @return 字符串值，如果键不存在或值为空则返回null
     */
    private String getStringValue(JSONObject json, String key) {
        try {
            return json.getString(key);
        } catch (Exception e) {
            // 如果键不存在或值不是字符串，返回null
            Object value = json.get(key);
            return value != null ? value.toString() : null;
        }
    }

    /**
     * 安全地从 JSONObject 中获取浮点数值
     *
     * @param json JSONObject对象
     * @param key 要获取的键
     * @return 浮点数值，如果键不存在或值不是数字则返回null
     */
    private Double getDoubleValue(JSONObject json, String key) {
        try {
            return json.getDouble(key);
        } catch (Exception e) {
            // 如果键不存在或值不是数字，返回null
            Object value = json.get(key);
            if (value == null) {
                return null;
            }
            try {
                if (value instanceof Number) {
                    return ((Number) value).doubleValue();
                } else {
                    return Double.parseDouble(value.toString());
                }
            } catch (NumberFormatException nfe) {
                return null;
            }
        }
    }

}