package com.databuff.entity;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 指标查询参数
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class SearchMetricsParamsV2 {
    @ApiModelProperty(value = "类型关键字")
    private String typeKey;

    @ApiModelProperty(value = "搜索关键字")
    private String metricKey;
}