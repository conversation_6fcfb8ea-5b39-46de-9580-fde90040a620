package com.databuff.entity;

import lombok.Data;

import java.util.Date;

/**
 * @author:TianMing
 * @date: 2023/5/6
 * @time: 13:56
 */
@Data
public class CmdbEntity {
    private String name;
    private String nameEn;
    private String ips;
    private String fqdn;
    //类型，0微服务,1虚拟机,2中间件,3数据库
    private Integer type;
    //环境类型，生产环境，预演环境，测试环境
    private String env;
    private Date updateTime;


    public CmdbEntity(){

    }
    public CmdbEntity(String name, String nameEn, String ips, String fqdn, Integer type,String env, Date updateTime) {
        this.name = name;
        this.nameEn = nameEn;
        this.ips = ips;
        this.fqdn = fqdn;
        this.type = type;
        this.env = env;
        this.updateTime = updateTime;
    }
}
