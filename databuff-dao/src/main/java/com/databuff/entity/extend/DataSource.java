package com.databuff.entity.extend;

import java.io.Serializable;
import java.util.Date;

/**
 * @description 对应表data_source 
 * <AUTHOR>
 * @date 2021-11-08 
 */
public class DataSource implements Serializable {
	private static final long serialVersionUID =  597494418693497761L;

	/**
	 * 对应表字段 id
	 */
	private Long id;

	/**
	 * 对应表字段 org_id
	 */
	private Long orgId;

	/**
	 * 对应表字段 version
	 */
	private Long version = 0L;

	/**
	 * 对应表字段 type
	 */
	private String type = "elasticsearch";

	/**
	 * 对应表字段 name
	 */
	private String name = "Elasticsearch";

	/**
	 * 对应表字段 access
	 */
	private String access = "proxy";

	/**
	 * 对应表字段 url
	 */
	private String url;

	/**
	 * 对应表字段 password
	 */
	private String password;

	/**
	 * 对应表字段 user
	 */
	private String user;

	/**
	 * 对应表字段 database
	 */
	private String database;

	/**
	 * 对应表字段 basic_auth
	 */
	private Integer basicAuth = 0;

	/**
	 * 对应表字段 basic_auth_user
	 */
	private String basicAuthUser;

	/**
	 * 对应表字段 basic_auth_password
	 */
	private String basicAuthPassword;

	/**
	 * 对应表字段 is_default
	 */
	private Integer isDefault = 0;

	/**
	 * 对应表字段 json_data
	 */
	private String jsonData;

	/**
	 * 对应表字段 created
	 */
	private Date created;

	/**
	 * 对应表字段 updated
	 */
	private Date updated;

	/**
	 * 对应表字段 with_credentials
	 */
	private Integer withCredentials = 0;

	/**
	 * 对应表字段 secure_json_data
	 */
	private String secureJsonData;

	/**
	 * 对应表字段 read_only
	 */
	private Integer readOnly;

	/**
	 * 对应表字段 uid
	 */
	private String uid;

	/**
	 * 首页仪表盘id
	 */
	private String dashboardUId;

	public void setDashboardUId(String dashboardUId) {
		this.dashboardUId = dashboardUId;
	}

	public String getDashboardUId() {
		return dashboardUId;
	}

	public Long getId() {
		return this.id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public Long getOrgId() {
		return this.orgId;
	}

	public void setOrgId(Long orgId) {
		this.orgId = orgId;
	}

	public Long getVersion() {
		return this.version;
	}

	public void setVersion(Long version) {
		this.version = version;
	}

	public String getType() {
		return this.type;
	}

	public void setType(String type) {
		this.type = type;
	}

	public String getName() {
		return this.name;
	}

	public void setName(String name) {
		this.name = name;
	}

	public String getAccess() {
		return this.access;
	}

	public void setAccess(String access) {
		this.access = access;
	}

	public String getUrl() {
		return this.url;
	}

	public void setUrl(String url) {
		this.url = url;
	}

	public String getPassword() {
		return this.password;
	}

	public void setPassword(String password) {
		this.password = password;
	}

	public String getUser() {
		return this.user;
	}

	public void setUser(String user) {
		this.user = user;
	}

	public String getDatabase() {
		return this.database;
	}

	public void setDatabase(String database) {
		this.database = database;
	}

	public Integer getBasicAuth() {
		return this.basicAuth;
	}

	public void setBasicAuth(Integer basicAuth) {
		this.basicAuth = basicAuth;
	}

	public String getBasicAuthUser() {
		return this.basicAuthUser;
	}

	public void setBasicAuthUser(String basicAuthUser) {
		this.basicAuthUser = basicAuthUser;
	}

	public String getBasicAuthPassword() {
		return this.basicAuthPassword;
	}

	public void setBasicAuthPassword(String basicAuthPassword) {
		this.basicAuthPassword = basicAuthPassword;
	}

	public Integer getIsDefault() {
		return this.isDefault;
	}

	public void setIsDefault(Integer isDefault) {
		this.isDefault = isDefault;
	}

	public String getJsonData() {
		return this.jsonData;
	}

	public void setJsonData(String jsonData) {
		this.jsonData = jsonData;
	}

	public Date getCreated() {
		return this.created;
	}

	public void setCreated(Date created) {
		this.created = created;
	}

	public Date getUpdated() {
		return this.updated;
	}

	public void setUpdated(Date updated) {
		this.updated = updated;
	}

	public Integer getWithCredentials() {
		return this.withCredentials;
	}

	public void setWithCredentials(Integer withCredentials) {
		this.withCredentials = withCredentials;
	}

	public String getSecureJsonData() {
		return this.secureJsonData;
	}

	public void setSecureJsonData(String secureJsonData) {
		this.secureJsonData = secureJsonData;
	}

	public Integer getReadOnly() {
		return this.readOnly;
	}

	public void setReadOnly(Integer readOnly) {
		this.readOnly = readOnly;
	}

	public String getUid() {
		return this.uid;
	}

	public void setUid(String uid) {
		this.uid = uid;
	}

}
