package com.databuff.entity;

import lombok.Builder;
import lombok.Data;

import java.util.List;

@Data
@Builder
public class RoleGroup {

    /**
     * 角色id
     */
    private Integer roleId;
    /**
     * 角色名称
     */
    private String roleName;
    /**
     * 子角色
     */
    private List<RoleGroup> children;

    private List<RoleGroupRelationBase> roleGroupRelations;

    public RoleGroup() {
    }

    public RoleGroup(Integer roleId, String roleName, List<RoleGroup> children, List<RoleGroupRelationBase> roleGroupRelations) {
        this.roleId = roleId;
        this.roleName = roleName;
        this.children = children;
        this.roleGroupRelations = roleGroupRelations;
    }
}
