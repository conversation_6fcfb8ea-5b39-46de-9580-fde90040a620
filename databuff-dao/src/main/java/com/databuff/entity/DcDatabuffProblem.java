package com.databuff.entity;

import com.alibaba.fastjson.JSONArray;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.*;
import java.util.Date;

@Entity
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Table(name = "dc_databuff_problem")
public class DcDatabuffProblem {

    @Id
    private String id;

    @Column(name = "gid")
    private String gid;

    @Column(name = "problemShowId")
    private String problemShowId;

    @Column(name = "problemService")
    private String problemService;

    @Column(name = "problemServiceType")
    private String problemServiceType;

    @Column(name = "problemCauseType")
    private String problemCauseType;

    @Column(name = "problemDesc")
    private String problemDesc;

    @Column(name = "problemStartTime")
    @Temporal(TemporalType.TIMESTAMP)
    private Date problemStartTime;

    @Column(name = "problemEndTime")
    @Temporal(TemporalType.TIMESTAMP)
    private Date problemEndTime;

    @Column(name = "analyseStartTime")
    @Temporal(TemporalType.TIMESTAMP)
    private Date analyseStartTime;

    @Column(name = "analyseEndTime")
    @Temporal(TemporalType.TIMESTAMP)
    private Date analyseEndTime;

    @Column(name = "influence")
    private JSONArray influence;

    @Column(name = "influenceServiceCount")
    private int influenceServiceCount;

    @Column(name = "influenceAlarmCount")
    private int influenceAlarmCount;

    @Column(name = "status")
    private String status;

    @Column(name = "isRoot")
    private boolean isRoot;

    @Column(name = "beginToActionTime")
    @Temporal(TemporalType.TIMESTAMP)
    private Date beginToActionTime;

    @Column(name = "suggestStatus")
    private int suggestStatus;

    @Column(name = "suggest")
    private String suggest;

}
