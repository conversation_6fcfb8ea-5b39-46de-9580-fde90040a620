package com.databuff.entity;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.annotation.*;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.Date;

@Setter
@Getter
@ToString
@TableName(value = "dc_sys_meta_config")
public class MetaConfigEntity<T> {

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @TableField(value = "code")
    private String code;

    @TableField(value = "describe")
    private String describe;

    @TableField(value = "params")
    private String params;

    @TableField(value = "enabled")
    private Boolean enabled;

    @TableField(value = "api_key")
    private String apiKey;

    @TableField(value = "update_time")
    private Date updateTime;

    public T getConfig(Class<T> clazz) {
        return JSON.parseObject(params, clazz);
    }

    public void setConfig(T t) {
        params = JSON.toJSONString(t);
    }

}
