package com.databuff.entity;

import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;

/**
 * @author:TianMing
 * @date: 2024/2/21
 * @time: 11:23
 */
@Data
@Entity
@Table(name = "dc_latest_all_process")
public class ProcessEntity {

    @Column(name = "spuid", nullable = false)
    private String spuid;

    @Column(name = "pname", nullable = false)
    private String pname;

    @Column(name = "pid", nullable = false)
    private Integer pid;

    private Integer ppid;
    private Boolean busProcess;
    private Double processState;
    private String user;


    @Column(name = "command.args", nullable = false)
    private String commandArgs;

    @Column(name = "containerId")
    private String containerId;

    @Column(name = "hostName")
    private String hostName;

    @Column(name = "processType")
    private String processType;

    @Column(name = "cpu.totalPct")
    private Double cpuTotalPct;

    @Column(name = "memory.rss")
    private Double memoryRss;

    @Column(name = "writeRate")
    private Double writeRate;

    @Column(name = "readRate")
    private Double readRate;

    @Column(name = "df-api-key", nullable = false)
    private String dfApiKey;

    @Column(name = "timestamp", nullable = false)
    private Long timestamp;
    private Long start;
    private Long end;
    @Column(name = "tags")
    private String tags;

    @Column(name = "data")
    private String data;

    private String hostOs;

}
