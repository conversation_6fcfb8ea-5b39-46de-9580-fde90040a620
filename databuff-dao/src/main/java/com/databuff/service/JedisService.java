package com.databuff.service;

import com.databuff.common.constants.Constant;
import com.databuff.common.constants.MetricName;
import com.databuff.common.exception.CustomException;
import com.databuff.common.utils.OtelMetricUtil;
import com.databuff.common.utils.SerializableUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import redis.clients.jedis.*;

import javax.annotation.Resource;
import java.util.*;

/**
 * JedisUtil(推荐存Byte数组，存Json字符串效率更慢)
 *
 * <AUTHOR>
 * @date 2019/3/16 15:45
 */
@Service
@Slf4j
public class JedisService {

    @Resource
    private JedisPool jedisPool;


    public Jedis getJedis() {
        return jedisPool.getResource();
    }

    /**
     * 获取redis键值-object
     *
     * @param key
     * @return java.lang.Object
     * <AUTHOR>
     * @date 2019/3/15 15:47
     */
    public Object getObject(String key) {
        long start = System.currentTimeMillis();
        try (Jedis jedis = jedisPool.getResource()) {
            byte[] bytes = jedis.get(key.getBytes());
            if (bytes != null && bytes.length > 0) {
                return SerializableUtil.unserializable(bytes);
            }
        } catch (Exception e) {
            OtelMetricUtil.logException("JedisUtil.getObject", e);
            throw new CustomException("获取Redis键值getObject方法异常:key=" + key + " cause=" + e.getMessage());
        } finally {
            long cost = System.currentTimeMillis() - start;
            OtelMetricUtil.logHistogram(MetricName.REDIS_COMMAND, cost, "get");
        }
        return null;
    }

    /**
     * 设置redis键值-object
     *
     * @param key
     * @param value
     * @return java.lang.String
     * <AUTHOR>
     * @date 2019/3/14 15:49
     */
    public String setObject(String key, Object value) {
        long start = System.currentTimeMillis();
        try (Jedis jedis = jedisPool.getResource()) {
            return jedis.set(key.getBytes(), SerializableUtil.serializable(value));
        } catch (Exception e) {
            OtelMetricUtil.logException("JedisUtil.setObject", e);
            throw new CustomException("设置Redis键值setObject方法异常:key=" + key + " value=" + value + " cause=" + e.getMessage());
        } finally {
            long cost = System.currentTimeMillis() - start;
            OtelMetricUtil.logHistogram(MetricName.REDIS_COMMAND, cost, "set");
        }
    }

    public String generateId(String key) {
        long start = System.currentTimeMillis();
        try (Jedis jedis = jedisPool.getResource()) {
            long id = jedis.incr(key);
            return String.format("%08d", id);
        } catch (Exception e) {
            OtelMetricUtil.logException("JedisUtil.generateId", e);
            throw new CustomException("设置Redis键值generateId方法异常:key=" + key + " cause=" + e.getMessage());
        } finally {
            long cost = System.currentTimeMillis() - start;
            OtelMetricUtil.logHistogram(MetricName.REDIS_COMMAND, cost, "generateId");
        }
    }

    public Long incr(String key) {
        long start = System.currentTimeMillis();
        try (Jedis jedis = jedisPool.getResource()) {
            return jedis.incr(key);
        } catch (Exception e) {
            OtelMetricUtil.logException("JedisUtil.incr", e);
            throw new CustomException("设置Redis键值incr方法异常:key=" + key + " cause=" + e.getMessage());
        } finally {
            long cost = System.currentTimeMillis() - start;
            OtelMetricUtil.logHistogram(MetricName.REDIS_COMMAND, cost, "incr");
        }
    }

    /**
     * 设置redis键值-object-expiretime
     *
     * @param key
     * @param value
     * @param expiretime
     * @return java.lang.String
     * <AUTHOR>
     * @date 2019/3/14 15:50
     */
    public String setObject(String key, Object value, Integer expiretime) {
        long start = System.currentTimeMillis();
        String result;
        try (Jedis jedis = jedisPool.getResource()) {
            result = jedis.set(key.getBytes(), SerializableUtil.serializable(value));
            if (Constant.OK.equals(result)) {
                jedis.expire(key.getBytes(), expiretime);
            }
            return result;
        } catch (Exception e) {
            OtelMetricUtil.logException("JedisUtil.setObject", e);
            throw new CustomException("设置Redis键值setObject方法异常:key=" + key + " value=" + value + " cause=" + e.getMessage());
        } finally {
            long cost = System.currentTimeMillis() - start;
            OtelMetricUtil.logHistogram(MetricName.REDIS_COMMAND, cost, "set");
        }
    }

    /**
     * 获取redis键值-Json
     *
     * @param key
     * @return java.lang.Object
     * <AUTHOR>
     * @date 2019/3/14 15:47
     */
    public String getJson(String key) {
        long start = System.currentTimeMillis();
        try (Jedis jedis = jedisPool.getResource()) {
            return jedis.get(key);
        } catch (Exception e) {
            OtelMetricUtil.logException("JedisUtil.getJson", e);
            throw new CustomException("获取Redis键值getJson方法异常:key=" + key + " cause=" + e.getMessage());
        } finally {
            long cost = System.currentTimeMillis() - start;
            OtelMetricUtil.logHistogram(MetricName.REDIS_COMMAND, cost, "get");
        }
    }

    /**
     * 设置redis键值-Json
     *
     * @param key
     * @param value
     * @return java.lang.String
     * <AUTHOR>
     * @date 2019/3/14 15:49
     */
    public String setJson(String key, String value) {
        long start = System.currentTimeMillis();
        try (Jedis jedis = jedisPool.getResource()) {
            return jedis.set(key, value);
        } catch (Exception e) {
            OtelMetricUtil.logException("JedisUtil.setJson", e);
            throw new CustomException("设置Redis键值setJson方法异常:key=" + key + " value=" + value + " cause=" + e.getMessage());
        } finally {
            long cost = System.currentTimeMillis() - start;
            OtelMetricUtil.logHistogram(MetricName.REDIS_COMMAND, cost, "set");
        }
    }

    /**
     * 获取hash中所有健值对
     *
     * @param key
     * @param <T>
     * @return
     */
    public <T> Map<String, String> getHashMap(String key) {
        long start = System.currentTimeMillis();
        try (Jedis jedis = jedisPool.getResource()) {
            if (jedis == null) {
                return null;
            }
            if (!jedis.exists(key)) {
                return null;
            }
            return jedis.hgetAll(key);
        } catch (Exception e) {
            OtelMetricUtil.logException("JedisUtil.getHashMap", e);
            log.warn("getHashMap error : {}", e);
            return null;
        } finally {
            long cost = System.currentTimeMillis() - start;
            OtelMetricUtil.logHistogram(MetricName.REDIS_COMMAND, cost, "hgetAll");
        }
    }

    /**
     * 设置hash中所有健值对
     *
     * @param key
     * @param <T>
     * @return
     */
    public <T> Map<String, String> setHashMap(String key,Map<String, String> map, Integer expiretime) {
        long start = System.currentTimeMillis();
        try (Jedis jedis = jedisPool.getResource()) {
            if (jedis == null) {
                return null;
            }
            jedis.hmset(key, map);
            jedis.expire(key, expiretime);
            return map;
        } catch (Exception e) {
            OtelMetricUtil.logException("JedisUtil.setHashMap", e);
            log.warn("setHashMap error : {}", e);
            return null;
        } finally {
            long cost = System.currentTimeMillis() - start;
            OtelMetricUtil.logHistogram(MetricName.REDIS_COMMAND, cost, "hmset");
        }

    }

    /**
     * 设置redis键值-Json-expiretime
     *
     * @param key
     * @param value
     * @param expiretime
     * @return java.lang.String
     * <AUTHOR>
     * @date 2019/3/14 15:50
     */
    public String setJson(String key, String value, Integer expiretime) {
        long start = System.currentTimeMillis();
        String result;
        try (Jedis jedis = jedisPool.getResource()) {
            result = jedis.set(key, value);
            if (Constant.OK.equals(result)) {
                jedis.expire(key, expiretime);
            }
            return result;
        } catch (Exception e) {
            OtelMetricUtil.logException("JedisUtil.setJson", e);
            throw new CustomException("设置Redis键值setJson方法异常:key=" + key + " value=" + value + " cause=" + e.getMessage());
        } finally {
            long cost = System.currentTimeMillis() - start;
            OtelMetricUtil.logHistogram(MetricName.REDIS_COMMAND, cost, "setJson");
        }
    }

    /**
     * 删除key
     *
     * @param key
     * @return java.lang.Long
     * <AUTHOR>
     * @date 2019/3/14 15:50
     */
    public Long delKey(String key) {
        long start = System.currentTimeMillis();
        try (Jedis jedis = jedisPool.getResource()) {
            return jedis.del(key.getBytes());
        } catch (Exception e) {
            OtelMetricUtil.logException("JedisUtil.delKey", e);
            throw new CustomException("删除Redis的键delKey方法异常:key=" + key + " cause=" + e.getMessage());
        } finally {
            long cost = System.currentTimeMillis() - start;
            OtelMetricUtil.logHistogram(MetricName.REDIS_COMMAND, cost, "del");
        }
    }

    /**
     * key是否存在
     *
     * @param key
     * @return java.lang.Boolean
     * <AUTHOR>
     * @date 2019/3/14 15:51
     */
    public Boolean exists(String key) {
        long start = System.currentTimeMillis();
        try (Jedis jedis = jedisPool.getResource()) {
            return jedis.exists(key.getBytes());
        } catch (Exception e) {
            OtelMetricUtil.logException("JedisUtil.exists", e);
            throw new CustomException("查询Redis的键是否存在exists方法异常:key=" + key + " cause=" + e.getMessage());
        } finally {
            long cost = System.currentTimeMillis() - start;
            OtelMetricUtil.logHistogram(MetricName.REDIS_COMMAND, cost, "exists");
        }
    }

    /**
     * 模糊查询获取key集合(keys的速度非常快，但在一个大的数据库中使用它仍然可能造成性能问题，生产不推荐使用)
     *
     * @param key
     * @return java.util.Set<java.lang.String>
     * <AUTHOR>
     * @date 2019/3/16 9:43
     */
    public Set<String> keysS(String key) {
        long start = System.currentTimeMillis();
        try (Jedis jedis = jedisPool.getResource()) {
            return jedis.keys(key + "*");
        } catch (Exception e) {
            OtelMetricUtil.logException("JedisUtil.keysS", e);
            throw new CustomException("模糊查询Redis的键集合keysS方法异常:key=" + key + " cause=" + e.getMessage());
        } finally {
            long cost = System.currentTimeMillis() - start;
            OtelMetricUtil.logHistogram(MetricName.REDIS_COMMAND, cost, "keys");
        }
    }

    /**
     * 模糊查询获取key集合(keys的速度非常快，但在一个大的数据库中使用它仍然可能造成性能问题，生产不推荐使用)
     *
     * @param key
     * @return java.util.Set<java.lang.String>
     * <AUTHOR>
     * @date 2019/3/16 9:43
     */
    public Set<byte[]> keysB(String key) {
        long start = System.currentTimeMillis();
        try (Jedis jedis = jedisPool.getResource()) {
            return jedis.keys(key.getBytes());
        } catch (Exception e) {
            OtelMetricUtil.logException("JedisUtil.keysB", e);
            throw new CustomException("模糊查询Redis的键集合keysB方法异常:key=" + key + " cause=" + e.getMessage());
        } finally {
            long cost = System.currentTimeMillis() - start;
            OtelMetricUtil.logHistogram(MetricName.REDIS_COMMAND, cost, "keys");
        }
    }

    /**
     * 批量设置在线账户过期时间
     *
     * @param keyword
     * @param expiretime
     * @return
     */
    public boolean updateKeysTtl(String keyword, Integer expiretime) {
        long start = System.currentTimeMillis();
        try (Jedis jedis = jedisPool.getResource()) {
            Set<String> keys = jedis.keys(keyword);
            for (String key : keys) {
                jedis.expire(key, expiretime);
            }
            return true;
        } catch (Exception e) {
            OtelMetricUtil.logException("JedisUtil.updateKeysTtl", e);
            throw new CustomException("设置账户过期时间异常:cause=" + e.getMessage());
        } finally {
            long cost = System.currentTimeMillis() - start;
            OtelMetricUtil.logHistogram(MetricName.REDIS_COMMAND, cost, "updateKeysTtl");
        }
    }

    public void setExpire(String key, String value, Integer expiretime) {
        long start = System.currentTimeMillis();
        try (Jedis jedis = jedisPool.getResource()) {
            String result = jedis.set(key, value);
            if ("OK".equals(result)) {
                jedis.expire(key, expiretime);
            }
        } catch (Exception e) {
            OtelMetricUtil.logException("JedisUtil.setExpire", e);
            throw new CustomException("设置Redis键TTL setExpire方法异常:key=" + key + " cause=" + e.getMessage());
        } finally {
            long cost = System.currentTimeMillis() - start;
            OtelMetricUtil.logHistogram(MetricName.REDIS_COMMAND, cost, "setExpire");
        }
    }

    /**
     * 批量保存string数据
     *
     * @param keys
     */
    public void setBatchStr(List<String> keys, String val) {
        long start = System.currentTimeMillis();
        try (Jedis jedis = jedisPool.getResource()) {
            //开启redis管道
            Pipeline pipeline = jedis.pipelined();
            for (String key : keys) {
                pipeline.set(key, val);
            }
            //管道提交数据
            pipeline.sync();
        } catch (Exception e) {
            OtelMetricUtil.logException("JedisUtil.setBatchStr", e);
            log.error("批量保存数据setBatchStr方法异常: cause=" + e.getMessage());
            throw new CustomException("批量保存数据失败！:{}" + e.getMessage());
        } finally {
            long cost = System.currentTimeMillis() - start;
            OtelMetricUtil.logHistogram(MetricName.REDIS_COMMAND, cost, "setBatchStr");
        }
    }

    /**
     * 批量删除键值
     *
     * @param keyWord
     */
    public void delBatchKey(String keyWord) {
        long start = System.currentTimeMillis();
        try (Jedis jedis = jedisPool.getResource()) {
            Set<String> keys = jedis.keys(keyWord);
            for (String key : keys) {
                jedis.del(key.getBytes());
            }
        } catch (Exception e) {
            OtelMetricUtil.logException("JedisUtil.delBatchKey", e);
            log.error("删除Redis的键delBatchKey方法异常:keyWord=" + keyWord + " cause=" + e.getMessage());
            throw new CustomException("删除Redis的键delBatchKey方法异常:keyWord=" + keyWord + " cause=" + e.getMessage());
        } finally {
            long cost = System.currentTimeMillis() - start;
            OtelMetricUtil.logHistogram(MetricName.REDIS_COMMAND, cost, "delBatchKey");
        }
    }

    /**
     * 设置过期时间
     *
     * @param key
     * @param expiretime
     */
    public void setExpire(String key, int expiretime) {
        long start = System.currentTimeMillis();
        try (Jedis jedis = jedisPool.getResource()) {
            if (jedis.exists(key)) {
                jedis.expire(key, expiretime);
            }
        } catch (Exception e) {
            OtelMetricUtil.logException("JedisUtil.setExpireStr", e);
            log.error("设置Redis键TTL setExpireStr:key=" + key + " cause=" + e.getMessage());
            throw new CustomException("组件R异常");
        } finally {
            long cost = System.currentTimeMillis() - start;
            OtelMetricUtil.logHistogram(MetricName.REDIS_COMMAND, cost, "setExpire");
        }
    }

    /**
     * 删除键值
     *
     * @param key
     */
    public void delKeyStr(String key) {
        long start = System.currentTimeMillis();
        try (Jedis jedis = jedisPool.getResource()) {
            jedis.del(key);
        } catch (Exception e) {
            OtelMetricUtil.logException("JedisUtil.delKeyStr", e);
            log.error("删除Redis的键delBatchKey方法异常:keyWord=" + key + " cause=" + e.getMessage());
            throw new CustomException("删除Redis的键delBatchKey方法异常:keyWord=" + key + " cause=" + e.getMessage());
        } finally {
            long cost = System.currentTimeMillis() - start;
            OtelMetricUtil.logHistogram(MetricName.REDIS_COMMAND, cost, "del");
        }
    }

    /**
     * 删除键值
     *
     * @param keys
     */
    public void delKeyStrs(Set<String> keys) {
        long start = System.currentTimeMillis();
        try (Jedis jedis = jedisPool.getResource()) {
            for (String key : keys) {
                if (jedis.exists(key)) {
                    jedis.del(key);
                }
            }
        } catch (Exception e) {
            OtelMetricUtil.logException("JedisUtil.delKeyStrs", e);
            log.error("删除Redis的键delBatchKey方法异常:keyWord=" + keys + " cause=" + e.getMessage());
            throw new CustomException("删除Redis的键delBatchKey方法异常:keyWord=" + keys + " cause=" + e.getMessage());
        } finally {
            long cost = System.currentTimeMillis() - start;
            OtelMetricUtil.logHistogram(MetricName.REDIS_COMMAND, cost, "delKeysStrs");
        }
    }

    // 返回值单位为"秒"；返回-2表示 过期；-1表示 没有时间限制
    public Long getTTL(String key) {
        long start = System.currentTimeMillis();
        Long result = -2L;
        try (Jedis redis = jedisPool.getResource()) {
            return redis.ttl(key);
        } catch (Exception e) {
            OtelMetricUtil.logException("JedisUtil.getTTL", e);
            log.error("redis getTTL error：{}", e);
            return result;
        } finally {
            long cost = System.currentTimeMillis() - start;
            OtelMetricUtil.logHistogram(MetricName.REDIS_COMMAND, cost, "getTtl");
        }
    }

    public Long setnx(String key, String value) {
        long start = System.currentTimeMillis();
        try (Jedis jedis = jedisPool.getResource()) {
            return jedis.setnx(key, value);
        } catch (Exception e) {
            OtelMetricUtil.logException("JedisUtil.setnx", e);
            log.error("redis setnx error：{}", e);
        } finally {
            long cost = System.currentTimeMillis() - start;
            OtelMetricUtil.logHistogram(MetricName.REDIS_COMMAND, cost, "setnx");
        }
        return 0L;
    }

    public boolean acquireLock(String lockKey, int expiretime) {
        long result = this.setnx(lockKey, "1");
        if (result == 1) { // lock acquired
            this.setExpire(lockKey, expiretime);
            return true;
        }
        return false;
    }

    public void executeIfLockAcquired(String key, int expiretime, Runnable task) {
        final boolean acquireLock = this.acquireLock(key, expiretime);
        if (Boolean.TRUE.equals(acquireLock)) {
            try {
                task.run();
            } finally {
                this.releaseLock(key); // 释放锁
            }
        }
    }

    public void releaseLock(String lockKey) {
        this.delKey(lockKey);
    }


    public Set<String> hkeys(String key) {
        long start = System.currentTimeMillis();
        try (Jedis jedis = jedisPool.getResource()) {
            return jedis.hkeys(key);
        } catch (Exception e) {
            OtelMetricUtil.logException("JedisUtil.hkeys", e);
            throw new CustomException("Redis map值hkeys方法异常:key=" + key + " cause=" + e.getMessage());
        } finally {
            long cost = System.currentTimeMillis() - start;
            OtelMetricUtil.logHistogram(MetricName.REDIS_COMMAND, cost, "kKeys");
        }
    }

    public void hset(String key, String field, String value, Integer expiretime) {
        long start = System.currentTimeMillis();
        try (Jedis jedis = jedisPool.getResource()) {
            jedis.hset(key, field, value);
            jedis.expire(key, expiretime);
        } catch (Exception e) {
            OtelMetricUtil.logException("JedisUtil.hset", e);
            throw new CustomException("设置Redis map值hsetnx方法异常:key=" + key + " field=" + field + " value=" + value + " cause=" + e.getMessage());
        } finally {
            long cost = System.currentTimeMillis() - start;
            OtelMetricUtil.logHistogram(MetricName.REDIS_COMMAND, cost, "hset");
        }
    }

    public Long hdel(String key, String field) {
        long start = System.currentTimeMillis();
        try (Jedis jedis = jedisPool.getResource()) {
            return jedis.hdel(key, field);
        } catch (Exception e) {
            OtelMetricUtil.logException("JedisUtil.hdel", e);
            throw new CustomException("删除Redis map值hdel方法异常:key=" + key + " field=" + field + " cause=" + e.getMessage());
        } finally {
            long cost = System.currentTimeMillis() - start;
            OtelMetricUtil.logHistogram(MetricName.REDIS_COMMAND, cost, "hdel");
        }
    }

    public Boolean sismember(String key, String member) {
        long start = System.currentTimeMillis();
        try (Jedis jedis = jedisPool.getResource()) {
            return jedis.sismember(key, member);
        } catch (Exception e) {
            OtelMetricUtil.logException("JedisUtil.sismember", e);
            throw new CustomException("Redis sismember异常:key=" + key + " member=" + member + " cause=" + e.getMessage());
        } finally {
            long cost = System.currentTimeMillis() - start;
            OtelMetricUtil.logHistogram(MetricName.REDIS_COMMAND, cost, "sismember");
        }
    }

    public Long sadd(String key, String member) {
        long start = System.currentTimeMillis();
        try (Jedis jedis = jedisPool.getResource()) {
            return jedis.sadd(key, member);
        } catch (Exception e) {
            OtelMetricUtil.logException("JedisUtil.sadd", e);
            throw new CustomException("Redis sadd异常:key=" + key + " member=" + member + " cause=" + e.getMessage());
        } finally {
            long cost = System.currentTimeMillis() - start;
            OtelMetricUtil.logHistogram(MetricName.REDIS_COMMAND, cost, "sadd");
        }
    }

    public Long scard(String key) {
        long start = System.currentTimeMillis();
        try (Jedis jedis = jedisPool.getResource()) {
            return jedis.scard(key);
        } catch (Exception e) {
            OtelMetricUtil.logException("JedisService.scard", e);
            throw new CustomException("Redis scard异常:key=" + key + " cause=" + e.getMessage());
        } finally {
            long cost = System.currentTimeMillis() - start;
            OtelMetricUtil.logHistogram(MetricName.REDIS_COMMAND, cost, "scard");
        }
    }

    public String scriptLoad(String script) {
        long start = System.currentTimeMillis();
        try (Jedis jedis = jedisPool.getResource()) {
            return jedis.scriptLoad(script);
        } catch (Exception e) {
            OtelMetricUtil.logException("JedisService.script", e);
            throw new CustomException("Redis script异常:key=" + script + " cause=" + e.getMessage());
        } finally {
            long cost = System.currentTimeMillis() - start;
            OtelMetricUtil.logHistogram(MetricName.REDIS_COMMAND, cost, "script");
        }
    }

    public Object eval(String script, List<String> keys, List<String> args) {
        long start = System.currentTimeMillis();
        try (Jedis jedis = jedisPool.getResource()) {
            return jedis.eval(script, keys, args);
        } catch (Exception e) {
            OtelMetricUtil.logException("JedisService.eval", e);
            throw new CustomException("Redis eval异常:key=" + keys + " ,args, " + args + "cause=" + e.getMessage());
        } finally {
            long cost = System.currentTimeMillis() - start;
            OtelMetricUtil.logHistogram(MetricName.REDIS_COMMAND, cost, "eval");
        }
    }

    public Object evalsha(String sha1, List<String> keys, List<String> args) {
        long start = System.currentTimeMillis();
        try (Jedis jedis = jedisPool.getResource()) {
            return jedis.evalsha(sha1, keys, args);
        } catch (Exception e) {
            OtelMetricUtil.logException("JedisService.evalsha", e);
            throw new CustomException("Redis evalsha异常:key=" + keys + " ,args, " + args + "cause=" + e.getMessage());
        } finally {
            long cost = System.currentTimeMillis() - start;
            OtelMetricUtil.logHistogram(MetricName.REDIS_COMMAND, cost, "evalsha");
        }
    }


    /**
     * 使用指定的游标和参数扫描Redis中的键
     *
     * @param cursor 扫描迭代游标（初始值为"0"，当返回结果为"0"时表示迭代结束）
     * @param params 扫描参数对象，包含匹配模式、数量限制等扫描选项
     * @return 包含下一次迭代游标和匹配键列表的扫描结果对象
     * 当返回结果的cursor字段为"0"时表示迭代完成
     * <p>
     * 实现说明：
     * - 通过Jedis连接池获取临时连接资源
     * - 使用try-with-resources语法确保连接自动归还连接池
     */
    public ScanResult<String> scan(String cursor, ScanParams params) {
        try (Jedis jedis = jedisPool.getResource()) {
            return jedis.scan(cursor, params);
        }
    }


    /**
     * 扫描匹配指定模式的键集合（使用游标迭代方式避免阻塞）
     *
     * @param pattern 键匹配模式（支持Redis通配符格式）
     * @return 包含所有匹配键的不可变集合（可能包含部分结果当发生异常时）
     */
    public Set<String> scanKeys(String pattern) {
        // 初始化扫描参数和结果容器
        Set<String> keys = new HashSet<>();
        String cursor = ScanParams.SCAN_POINTER_START;
        ScanParams params = new ScanParams().match(pattern).count(500);

        // 安全扫描控制参数：最大迭代1万次（防止大key集合导致无限循环）
        int maxIterations = 10000;
        int iteration = 0;

        try {
            // 使用游标进行分批次扫描，直到返回起始游标
            do {
                ScanResult<String> scanResult = scan(cursor, params);
                keys.addAll(scanResult.getResult());
                cursor = String.valueOf(scanResult.getCursor());

                // 安全机制：当迭代次数超过阈值时强制终止
                if (iteration++ > maxIterations) {
                    log.warn("SCAN操作超过安全迭代次数 pattern:{}", pattern);
                    break;
                }

            } while (!ScanParams.SCAN_POINTER_START.equals(cursor));

        } catch (Exception e) {
            // 异常处理：记录错误日志但保留已扫描结果
            log.error("SCAN操作异常终止 pattern:{}", pattern);
        }
        return Collections.unmodifiableSet(keys);
    }
}
