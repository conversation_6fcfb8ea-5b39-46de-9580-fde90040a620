package com.databuff.interceptor;

import com.databuff.common.utils.OtelMetricUtil;
import com.databuff.util.OlapUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.executor.statement.StatementHandler;
import org.apache.ibatis.mapping.BoundSql;
import org.apache.ibatis.plugin.*;
import org.apache.ibatis.session.ResultHandler;

import java.sql.Statement;
import java.util.HashMap;
import java.util.Map;
import java.util.Properties;

import static com.databuff.common.constants.MetricName.SQL_COST;

@Intercepts({@Signature(type = StatementHandler.class, method = "query", args = {Statement.class, ResultHandler.class}),
        @Signature(type = StatementHandler.class, method = "update", args = {Statement.class}),
        @Signature(type = StatementHandler.class, method = "batch", args = {Statement.class})})
@Slf4j
public class SqlExecutionTimeInterceptor implements Interceptor {

    private String dbType;

    public SqlExecutionTimeInterceptor(String dbType) {
        this.dbType = dbType;
    }

    @Override
    public Object intercept(Invocation invocation) throws Throwable {
        long start = System.currentTimeMillis();
        boolean error = false;
        try {
            return invocation.proceed();
        } catch (Throwable e) {
            error = true;
            throw e;
        } finally {
            long end = System.currentTimeMillis();
            long time = end - start;

            // 获取拦截到的 StatementHandler 对象
            StatementHandler statementHandler = (StatementHandler) invocation.getTarget();
            // 获取 BoundSql 对象，包含了完整的 SQL 语句
            BoundSql boundSql = statementHandler.getBoundSql();
            String sql = boundSql.getSql();
            String tableName = OlapUtil.extractTableName(sql);

            Map<String, String> tags = new HashMap<>();
            tags.put("dbType", dbType);
            tags.put("table", tableName);
            tags.put("sql", sql);
            tags.put("error", error ? "1" : "0");
            OtelMetricUtil.logHistogram(SQL_COST, tags, time);
        }
    }

    @Override
    public Object plugin(Object target) {
        return Plugin.wrap(target, this);
    }

    @Override
    public void setProperties(Properties properties) {
        // 这里可以设置一些插件的属性，但在本例中不需要
    }


}
