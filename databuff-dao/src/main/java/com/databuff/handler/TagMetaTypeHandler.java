package com.databuff.handler;

import com.alibaba.fastjson.JSONArray;
import com.baomidou.mybatisplus.extension.handlers.AbstractJsonTypeHandler;
import com.databuff.entity.DBMeta;
import org.apache.ibatis.type.JdbcType;
import org.apache.ibatis.type.MappedJdbcTypes;
import org.apache.ibatis.type.MappedTypes;

import java.util.Collection;

/**
 * <AUTHOR>
 * @version 1.0
 * @since 2.7.1
 */
@MappedJdbcTypes({JdbcType.VARCHAR})
@MappedTypes({DBMeta.TagMeta.class})
public class TagMetaTypeHandler extends AbstractJsonTypeHandler<Collection<DBMeta.TagMeta>> {
    /**
     * @param json
     * @return
     */
    @Override
    protected Collection<DBMeta.TagMeta> parse(String json) {
        return JSONArray.parseArray(json, DBMeta.TagMeta.class);
    }

    /**
     * @param obj
     * @return
     */
    @Override
    protected String toJson(Collection<DBMeta.TagMeta> obj) {
        return JSONArray.toJSONString(obj);
    }
}
