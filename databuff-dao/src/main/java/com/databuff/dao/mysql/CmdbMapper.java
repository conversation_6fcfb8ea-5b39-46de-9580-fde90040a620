package com.databuff.dao.mysql;

import com.databuff.entity.CmdbEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * @author:TianMing
 * @date: 2023/5/6
 * @time: 17:45
 */
@Mapper
@Repository
public interface CmdbMapper {

    int deleteCmdb();

    int bachInsertCmdb(@Param("entiyList") List<CmdbEntity> entiyList);

    List<CmdbEntity> listCmdb(@Param("type") Integer type);
    List<CmdbEntity> listHostCmdb();



}
