package com.databuff.dao.mysql;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.databuff.entity.AIConfig;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;
import org.springframework.stereotype.Repository;

import java.util.List;

@Mapper
@Repository
public interface AIConfigMapper extends BaseMapper {

    @Insert("insert into dc_databuff_ai_config(url, api_key, model, open) values(#{url}, #{apiKey}, #{model}, #{open})")
    void saveAIConfig(AIConfig aiConfig);

    @Update("update dc_databuff_ai_config set url = #{url}, api_key = #{apiKey}, model = #{model}, open = #{open}")
    void updateAIConfig(AIConfig aiConfig);

    @Select("select * from dc_databuff_ai_config")
    List<AIConfig> getAIConfig();
}
