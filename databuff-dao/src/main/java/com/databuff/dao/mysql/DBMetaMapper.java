package com.databuff.dao.mysql;


import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.databuff.entity.DBMeta;
import com.databuff.handler.FieldMetaTypeHandler;
import com.databuff.handler.TagMetaTypeHandler;
import org.apache.ibatis.annotations.*;
import org.springframework.stereotype.Repository;

import java.util.List;


@Mapper
@Repository
public interface DBMetaMapper extends BaseMapper<DBMeta> {

    @Select("select * from dc_databuff_db_measure_meta where `db`=#{db} and `table`=#{tb} and `api_key`=#{apiKey} limit 1")
    @Results({
            @Result(column = "tags", property = "tags", typeHandler = TagMetaTypeHandler.class),
            @Result(column = "fields", property = "fields", typeHandler = FieldMetaTypeHandler.class)
    })
    DBMeta getDBMeta(@Param("db") String db, @Param("tb") String tb, @Param("apiKey") String apiKey);

    @Select("select * from dc_databuff_db_measure_meta")
    @Results({
            @Result(column = "tags", property = "tags", typeHandler = TagMetaTypeHandler.class),
            @Result(column = "fields", property = "fields", typeHandler = FieldMetaTypeHandler.class)
    })
    List<DBMeta> AllDBMeta();

}
