package com.databuff.metric.impl.metric;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson2.JSONB;
import com.databuff.common.metric.WrapData;
import com.databuff.common.threadLocal.NowTimeThreadLocal;
import com.databuff.common.tsdb.TSDBOperateUtil;
import com.databuff.common.tsdb.builder.QueryBuilderX;
import com.databuff.common.tsdb.dto.explore.QueryRequest;
import com.databuff.common.tsdb.dto.preview.RuleDTO;
import com.databuff.common.tsdb.model.*;
import com.databuff.common.utils.CalculatorUtil;
import com.databuff.config.DAORefreshScopeConfig;
import com.databuff.entity.EventEntity;
import com.databuff.entity.MetricsQuery;
import com.databuff.entity.dto.DatabuffMonitorView;
import com.databuff.metric.MetricAggregator;
import com.databuff.metric.MetricPreviewer;
import com.databuff.metric.builder.QueryRequestConverterFactory;
import com.databuff.metric.dto.MetricDTO;
import com.databuff.metric.moredb.SQLParser;
import com.databuff.service.MetricsQueryService;
import com.databuff.tsdb.metric.TSDBMetricOperator;
import com.google.common.cache.Cache;
import com.google.common.cache.CacheBuilder;
import com.google.common.collect.Lists;
import com.sun.istack.NotNull;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.TimeUnit;

import static com.databuff.common.constants.Constant.DF_API_KEY;
import static com.databuff.common.utils.StringUtil.parseUpperCase;
import static com.databuff.common.utils.TimeUtil.ONE_MIN_MS_LONG;
import static com.databuff.common.utils.TimeUtil.OUT_DAY_MS_LONG;


/**
 * 指标聚合器
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2.7.0.a
 */
@Component("defSingleMetricAggregator")
@Slf4j
public class DefSingleMetricAggregator implements MetricAggregator {

    private final static String SEPARATOR = ":";
    private final static String REDIS_PREFIX = "monitor:metric:";
    public static final String EXPIRED_AT = "expiredAt";
    public static final String LAST_UPDATE_TIME = "lastUpdateTime";
    public static final String TRIGGERS = "triggers";

    @Autowired
    private DAORefreshScopeConfig refreshScopeConfig;

    @Autowired
    @Qualifier("baselineMetricPreviewer")
    private MetricPreviewer baselineMetricPreviewer;

    @Autowired
    @Qualifier("changePointMetricPreviewer")
    private MetricPreviewer changePointMetricPreviewer;

    @Autowired
    private QueryRequestConverterFactory queryRequestConverterFactory;

    @Autowired
    protected TSDBOperateUtil tsdbOperateUtil;

    @Autowired
    private MetricAggregator metricAggregator;

    private final Cache<Long, JSONObject> historyTriggerCache = CacheBuilder.newBuilder()
            .expireAfterWrite(1, TimeUnit.DAYS)
            .build();

    @Resource(name = "TSDBMetricMMT")
    protected TSDBMetricOperator tsdbMetricOperator;

    @Autowired
    private QueryRequestConverterFactory converterFactory;

    @Autowired
    private MetricsQueryService metricsQueryService;

    /**
     * 根据查询结果构建日期和结果的映射
     *
     * @param queryResult 查询结果，键为序列号，值为QueryResult.Series对象
     * @return 一个有序的Map，键为时间戳和标签的拼接字符串，值为对应的数值
     * <p>
     * - 输入：queryResult = {0: new Series(Map.of("city", "Beijing"), List.of(List.of(1634726400.0, 10.0), List.of(1634730000.0, 12.0))), 1: new Series(Map.of("city", "Shanghai"), List.of(List.of(1634726400.0, 8.0), List.of(1634730000.0, 9.0)))}
     * - 输出：dateResMap = {1634726400¥{"city":"Beijing"}: 10.0, 1634730000¥{"city":"Beijing"}: 12.0, 1634726400¥{"city":"Shanghai"}: 8.0, 1634730000¥{"city":"Shanghai"}: 9.0}
     * <p>
     * - 输入：queryResult = {2: new Series(Map.of("country", "USA", "state", "California"), List.of(List.of(1634740800.0, 15.0), List.of(1634744400.0, 16.5))), 3: new Series(Map.of("country", "USA", "state", "New York"), List.of(List.of(1634740800.0, 13.5), List.of(1634744400.0, 14.0)))}
     * - 输出：dateResMap = {1634740800¥{"country":"USA","state":"California"}: 15.0, 1634744400¥{"country":"USA","state":"California"}: 16.5, 1634740800¥{"country":"USA","state":"New York"}: 13.5, 1634744400¥{"country":"USA","state":"New York"}: 14.0}
     * <p>
     * - 输入：queryResult = null
     * - 输出：dateResMap = {}
     */
    @NotNull
    private static Map<Object, Double> buildDateResMap(Map<Map<String, String>, TSDBSeries> queryResult) {
        Map<Object, Double> dateResMap = new LinkedHashMap<>(16);
        if (CollectionUtils.isEmpty(queryResult)) {
            return dateResMap;
        }
        for (Map.Entry<Map<String, String>, TSDBSeries> entry : queryResult.entrySet()) {
            if (entry == null) {
                continue;
            }
            TSDBSeries series = entry.getValue();
            JSONObject tags = new JSONObject((Map) entry.getKey());
            final List<List<Object>> values = series.getValues();
            for (List<Object> v : values) {
                if (v == null) {
                    continue;
                }
                if (!(v.get(0) instanceof Number)) {
                    continue;
                }
                long time = ((Number) v.get(0)).longValue();

                for (int i = 1; i < v.size(); i++) {
                    if (!(v.get(i) instanceof Number)) {
                        continue;
                    }
                    final Number number = ((Number) v.get(i));
                    if (number != null) {
                        dateResMap.put(i + time + "¥" + tags, number.doubleValue());
                    } else {
                        dateResMap.put(i + time + "¥" + tags, null);
                    }
                }
            }
        }
        return dateResMap;
    }

    /**
     * 指标查询聚合器
     *
     * @param expr     聚合表达式: A+(B-C); A*(B-C)
     * @param queries  查询表达式
     * @param interval 聚合粒度（单位秒）
     * @return 聚合以后的结果
     */
    @Override
    public Map<Map, Map<Object, Double>> aggResult(String expr, List<JSONObject> queries, final Long interval) {
        final Map<String, Map<Object, Double>> result = new HashMap<>();
        if (CollectionUtils.isEmpty(queries)) {
            return new HashMap<>();
        }

        if (expr == null) {
            expr = "A";
        }

        // 要求表达式expressionArr和queries的遍历顺序是一致的，否则计算会出错
        final List<String> expressionArr = parseUpperCase(expr);
        for (int i = 0, queriesSize = queries.size(); i < queriesSize; i++) {
            // 防止数组越界异常
            if (i > expressionArr.size() - 1) {
                continue;
            }
            JSONObject query = queries.get(i);
            if (query == null) {
                continue;
            }

            final Map<Object, Double> dateResMap = buildDateResMap(this.seriesResult(query, interval));
            result.put(expressionArr.get(i), dateResMap);
        }

        final Map<Object, Double> doubleMap = CalculatorUtil.executeExprOfHistogram(expr, result);

        return CalculatorUtil.group(doubleMap);
    }

    /**
     * 聚合查询结果并执行表达式计算
     *
     * @param expr            待解析的表达式字符串，格式为大写字母序列（如"AB"）
     * @param queryRequestMap 查询请求映射，键为表达式标识符，值为对应的查询请求
     * @return 返回聚合后的结果Map，结构为{表达式标识符 -> {时间戳/键 -> 计算值}}
     */
    @Override
public Map<Map, Map<Object, Double>> aggResult(String expr, Map<String, QueryRequest> queryRequestMap) {
    final Map<String, Map<Object, Double>> result = new HashMap<>();
    // 检查查询请求映射是否为空，直接返回空结果
    if (CollectionUtils.isEmpty(queryRequestMap)) {
        return new HashMap<>();
    }

    // 设置默认表达式为"A"，若输入为空
    if (expr == null) {
        expr = "A";
    }

    // 解析并转换表达式为全大写字符数组
    final List<String> expressionArr = parseUpperCase(expr);
    if (expressionArr.isEmpty()) { // 确保expressionArr不为空
        throw new IllegalArgumentException("Parsed expression is empty");
    }
    // 遍历每个表达式标识符并构建对应的时间结果映射
    for (String exp : expressionArr) {
        final QueryRequest queryRequest = queryRequestMap.get(exp);
        if (queryRequest == null) {
            continue;
        }
        final Map<Object, Double> dateResMap = buildDateResMap(this.seriesResult(queryRequest));
        if (dateResMap == null) {
            continue;
        }
        result.put(exp, dateResMap);
    }

    // 执行表达式计算并分组最终结果
    final Map<Object, Double> doubleMap = CalculatorUtil.executeExprOfHistogram(expr, result);
    return CalculatorUtil.group(doubleMap);
}

    /**
     * 聚合查询结果并进行检测类型处理
     *
     * @param expr          聚合表达式，若为null时默认使用"A"
     * @param detectionType 检测类型，影响最终结果处理方式
     * @param rule
     * @param queries       查询请求映射表，key为查询标识
     * @return 包装后的数据集合，根据检测类型可能进行额外处理
     */
    @Override
    public List<WrapData> aggResultView(String expr, EventEntity.DetectionType detectionType, RuleDTO rule, Map<String, QueryRequest> queries) {
        // 处理空查询请求直接返回
        if (CollectionUtils.isEmpty(queries)) {
            return new ArrayList<>();
        }

        // 初始化表达式和单位字符串
        expr = expr == null ? "A" : expr;
        String unitStr = expr;

        // 获取时序数据并构建结果映射表
        final Map<String, Map<Map<String, String>, TSDBSeries>> seriesResult = this.seriesResult(queries);
        final Map<String, Map<Object, Double>> result = new HashMap<>();
        for (Map.Entry<String, Map<Map<String, String>, TSDBSeries>> entry : seriesResult.entrySet()) {
            result.put(entry.getKey(), buildDateResMap(entry.getValue()));
        }

        // 执行表达式计算并分组处理
        List<WrapData> wrapData = CalculatorUtil.groupV2(CalculatorUtil.executeExprOfHistogram(expr, result), expr, unitStr);

        // 根据检测类型进行结果后处理
        if (detectionType == null) {
            return wrapData;
        }
        switch (detectionType) {
            case threshold:
                break;
            case baseline:
                wrapData = baselineMetricPreviewer.preview(wrapData, rule, queries.values());
                break;
            case mutation:
                break;
            case stateThreshold:
                break;
            case changePoint:
                wrapData = changePointMetricPreviewer.preview(wrapData, rule, queries.values());
                break;
        }
        return wrapData;
    }


    /**
     * 根据查询条件获取时序数据库聚合结果集
     *
     * @param query    包含维度过滤条件的JSON查询对象，允许包含指标名称、标签过滤等参数。当interval参数有效时，
     *                 会自动添加时间聚合参数到查询条件中
     * @param interval 可选的时间聚合间隔（单位：毫秒），用于指定时序数据的时间窗口大小。
     *                 当值大于0时，会作为聚合参数添加到查询条件中
     * @return 返回包含时序数据系列的映射结果，外层Map的键是表示维度组合的标签键值对集合，
     * 内层TSDBSeries对应特定维度组合下的时序数据序列
     */
    @Deprecated
    @Override
    public Map<Map<String, String>, TSDBSeries> seriesResult(JSONObject query, Long interval) {
        if (interval != null && interval > 0) {
            query.put(SQLParser.INTERVAL, interval);
        }

        final QueryBuilderX queryBuilderX = tsdbMetricOperator.aggBuilder(query);
        return tsdbMetricOperator.queryResult(queryBuilderX);
    }

    /**
     * 执行时间序列查询并返回结果映射。
     *
     * @param queryRequest 包含查询参数的请求对象
     * @return 时间序列查询结果，键为标签元数据的Map结构，值为对应的TSDBSeries对象
     */
    @Override
    public Map<Map<String, String>, TSDBSeries> seriesResult(QueryRequest queryRequest) {
        final QueryBuilderX queryBuilderX = converterFactory.buildQueryBuilderX(queryRequest);
        if (queryBuilderX == null) {
            return new HashMap<>();
        }
        return tsdbMetricOperator.queryResult(queryBuilderX);
    }


    /**
     * 获取指定指标的标签值结果
     *
     * @param apiKey     API密钥（可选，若为空则使用默认值）
     * @param metricCode 指标代码（必填）
     * @param wheres     查询过滤条件集合
     * @return 返回标签键到其对应值集合的映射，若指标不存在则返回空Map
     */
    @Override
    public Map<String, Set<String>> showTagValuesResult(String apiKey, String metricCode, List<Where> wheres) {
        return showTagValuesResult(apiKey, metricCode, wheres, null);
    }

    /**
     * 获取指定指标的标签值结果
     *
     * @param apiKey     API密钥（可选，若为空则使用默认值）
     * @param metricCode 指标代码（必填）
     * @param wheres     查询过滤条件集合
     * @param keys       需要获取的标签键列表
     * @return 返回标签键到其对应值集合的映射，若指标不存在则返回空Map
     */
    @Override
    public Map<String, Set<String>> showTagValuesResult(String apiKey, String metricCode, List<Where> wheres, Collection<String> keys) {
        return showTagValuesResult(apiKey, metricCode, wheres, keys, null, null);
    }

    /**
     * 获取指定指标的标签值结果
     *
     * @param apiKey     API密钥（可选，若为空则使用默认值）
     * @param metricCode 指标代码（必填）
     * @param wheres     查询过滤条件集合
     * @param keys       需要获取的标签键列表
     * @param start      查询起始时间戳（单位：毫秒）
     * @param end        查询结束时间戳（单位：毫秒）
     * @return 返回标签键到其对应值集合的映射，若指标不存在则返回空Map
     */
    @Override
    public Map<String, Set<String>> showTagValuesResult(String apiKey, String metricCode, List<Where> wheres, Collection<String> keys, Long start, Long end) {
        final Map<String, Set<String>> result = new HashMap<>();

        // 验证指标代码参数有效性
        if (metricCode == null) {
            throw new IllegalArgumentException("Metric code cannot be null");
        }

        // 设置默认API密钥
        if (apiKey == null) {
            apiKey = DF_API_KEY;
        }

        // 获取指标查询配置
        final MetricsQuery metricsQuery = metricsQueryService.findOpenByIdentifier(metricCode);
        if (metricsQuery == null) {
            return result; // 若指标配置不存在则直接返回空结果
        }

        // 构建完整的数据库名称
        final String database = apiKey + "_" + metricsQuery.getDatabase();

        // 执行标签值查询操作
        return tsdbOperateUtil.showTagValues(metricsQuery.getField(), database, metricsQuery.getMeasurement(), wheres, keys, start, end);
    }

    @Override
    public Map<String, Set<String>> showTagValuesResult(String apiKey, String metricCode, List<Where> wheres, Collection<String> keys, Integer period) {
        final Map<String, Set<String>> result = new HashMap<>();

        // 验证指标代码参数有效性
        if (metricCode == null) {
            throw new IllegalArgumentException("Metric code cannot be null");
        }

        // 设置默认API密钥
        if (apiKey == null) {
            apiKey = DF_API_KEY;
        }

        // 获取指标查询配置
        final MetricsQuery metricsQuery = metricsQueryService.findOpenByIdentifier(metricCode);
        if (metricsQuery == null) {
            return result;
        }

        // 构建完整的数据库名称
        final String database = apiKey + "_" + metricsQuery.getDatabase();

        // 执行标签值查询操作
        return tsdbOperateUtil.showPeriodTagValues(metricsQuery.getField(), database, metricsQuery.getMeasurement(), wheres, keys, period);
    }


    /**
     * 执行时间序列查询并返回结果映射。
     *
     * @param queryBuilder 包含查询参数的请求对象
     * @return 时间序列查询结果，键为标签元数据的Map结构，值为对应的TSDBSeries对象
     */
    @Override
    public Map<Map<String, String>, TSDBSeries> seriesResult(QueryBuilderX queryBuilder) {
        return tsdbMetricOperator.queryResult(queryBuilder);
    }


    /**
     * 将多个查询请求转换为对应的TSDB系列结果集
     *
     * @param queryRequestMap 查询请求映射，键为查询标识，值为具体的查询请求对象
     * @return 结果映射结构，外层键为查询标识，内层键为系列标签映射，值为TSDB系列对象
     */
    @Override
    public Map<String, Map<Map<String, String>, TSDBSeries>> seriesResult(Map<String, QueryRequest> queryRequestMap) {
        Map<String, Map<Map<String, String>, TSDBSeries>> result = new HashMap<>();
        if (queryRequestMap == null) {
            return result;
        }

        // 遍历所有查询请求，将每个查询请求转换为对应的系列结果并存入结果集
        for (Map.Entry<String, QueryRequest> entry : queryRequestMap.entrySet()) {
            result.put(entry.getKey(), this.seriesResult(entry.getValue()));
        }
        return result;
    }


    @SneakyThrows
    @Override
    public Map<Map<String, String>, JSONObject> baselineResult(QueryRequest query, String comparison, Double baselineScope) {
        /**
         * 设置基线范围默认值为1.0
         */
        if (baselineScope <= 0) {
            baselineScope = 1.0;
        }
        Map<Map<String, String>, JSONObject> result = new HashMap<>();

        final String metricCode = query.getMetric();

        // 查询最近一周的数据
        final Long now = NowTimeThreadLocal.getNowTime();
        query.setStart(now - OUT_DAY_MS_LONG);
        query.setEnd(now);
        query.setInterval(60);

        /**
         * 将查询请求转换为查询构建器对象
         */
        final QueryBuilderX builderX = queryRequestConverterFactory.buildQueryBuilderX(query);
        if (builderX == null) {
            return new HashMap<>();
        }

        /**
         * 查询指标配置信息，若未找到则记录日志并返回空结果
         */
        final MetricsQuery metricsQuery = metricsQueryService.findOpenByIdentifier(query.getMetric());
        if (metricsQuery == null) {
            log.warn("metricsQuery: {} not found", metricCode);
            return null;
        }

        /**
         * 根据比较符选择基线计算公式
         */
        String formula = "(percentile(%s, 99) + (percentile(%s, 75)-percentile(%s, 25))) * " + baselineScope;
        if (comparison != null && (comparison.equalsIgnoreCase("<") || comparison.equalsIgnoreCase("<="))) {
            formula = "(percentile(%s, 99) - (percentile(%s, 75)-percentile(%s, 25))) * " + baselineScope;
        }

        /**
         * 处理聚合类型配置，使用指标配置中的默认值或"avg"作为最终聚合方式
         */
        String aggregatorType = query.getAggs();
        if (aggregatorType == null) {
            aggregatorType = metricsQuery.getAggregatorType();
        }
        if (aggregatorType == null || "mean".equals(aggregatorType)) {
            aggregatorType = "avg";
        }

        /**
         * 构建聚合表达式，优先使用指标字段配置，否则使用自定义公式
         */
        final String field = metricsQuery.getField();
        final String aggExpr = field != null ? String.format("%s(\"%s\")", aggregatorType, field) : metricsQuery.getFormula();
        formula = String.format(formula, aggExpr, aggExpr, aggExpr);

        /**
         * 构建最终查询语句并添加聚合计算
         */
        final String selectStmt = String.format("count(%s) as num, %s as baseline", aggExpr, formula);
        builderX.setAggregations(Lists.newArrayList(new Aggregation(selectStmt)));

        final Map<Map<String, String>, TSDBSeries> queryResult = metricAggregator.seriesResult(builderX);
        /**
         * 执行指标查询并处理结果
         */
        if (queryResult == null) {
            return result;
        }

        /**
         * 将查询结果转换为目标格式，处理负数基线值为0
         */
        for (Map.Entry<Map<String, String>, TSDBSeries> entry : queryResult.entrySet()) {
            Map<String, String> key = entry.getKey();
            final TSDBSeries value = entry.getValue();
            final List<Object> jsonArray = value.getValues().get(0);
            JSONObject baseline = new JSONObject();
            baseline.put("time", System.currentTimeMillis());
            baseline.put("num", jsonArray.get(1));
            double doubleValue = (double) jsonArray.get(2);
            if (doubleValue < 0) {
                doubleValue = 0;
            }
            baseline.put("baseline", doubleValue);
            result.put(key, baseline);
        }
        return result;
    }


    /**
     * 此方法用于从给定查询中检索指标结果列表。
     * 查询是一个 JSONObject，表示查询参数。
     * 该方法返回一个映射，其中键是标签映射，值是 MetricDTO 对象的集合。
     *
     * @param query 表示查询参数的 JSONObject。
     * @return 一个映射，其中键是标签映射，值是 MetricDTO 对象的集合。
     */
    @Override
    public Map<Map<String, String>, Collection<MetricDTO>> listResult(QueryRequest query) {
        query.setInterval(null);
        Map<Map<String, String>, TSDBSeries> seriesMap = this.seriesResult(query);
        return processSeriesMap(seriesMap);
    }

    @Override
    public Map<Map<String, String>, Collection<MetricDTO>> listResult(QueryBuilderX queryBuilder) {
        queryBuilder.setInterval(null);
        Map<Map<String, String>, TSDBSeries> seriesMap = this.seriesResult(queryBuilder);
        return processSeriesMap(seriesMap);
    }

    @SneakyThrows
    @Override
    public TSDBResultSet executeQuery(QueryBuilderX builder, AggFun tsAgg, AggFun valAgg, AggFun topAgg, JSONObject otherParam) {
        return tsdbOperateUtil.executeQuery(builder, tsAgg, valAgg, topAgg, otherParam);
    }

    /**
     * 处理TSDB系列数据，将每个系列转换为MetricDTO集合。
     *
     * @param seriesMap 需要处理的原始TSDB系列数据，键为标签键值对，值为TSDBSeries对象
     * @return 转换后的MetricDTO集合，键为标签键值对，值为对应的指标数据列表
     */
    private Map<Map<String, String>, Collection<MetricDTO>> processSeriesMap(Map<Map<String, String>, TSDBSeries> seriesMap) {
        Map<Map<String, String>, Collection<MetricDTO>> result = new HashMap<>();
        if (seriesMap == null) {
            // 若输入seriesMap为空则直接返回空结果
            return result;
        }
        // 遍历所有TSDB系列数据并执行转换处理
        for (Map.Entry<Map<String, String>, TSDBSeries> entry : seriesMap.entrySet()) {
            Map<String, String> tag = entry.getKey();
            TSDBSeries value = entry.getValue();
            if (value == null) {
                continue;
            }
            List<MetricDTO> metricDTOList = processSeries(value, tag);
            if (!metricDTOList.isEmpty()) {
                // 仅当转换结果非空时才将数据存入结果集
                result.put(tag, metricDTOList);
            }
        }
        return result;
    }


    /**
     * 处理TSDB时间序列数据，将数据转换为MetricDTO列表。
     *
     * @param series 时间序列数据源，包含列名、单位、描述和数据值
     * @param tag    标签映射，用于附加到生成的MetricDTO对象的tag属性
     * @return 符合条件的指标数据列表，每个元素包含指标名称、值、单位、中文描述及时间戳
     * <AUTHOR>
     */
    private List<MetricDTO> processSeries(TSDBSeries series, Map<String, String> tag) {
        final List<MetricDTO> metricDTOList = new ArrayList<>();
        final List<String> columns = series.getColumns();
        final List<String> units = series.getUnits();
        final List<String> describeCns = series.getDescribeCns();
        final List<List<Object>> values = series.getValues();

        // 验证数据源完整性：检查列或数据值是否为空或结构不一致
        if (CollectionUtils.isEmpty(columns) || CollectionUtils.isEmpty(values)
                || columns.size() != units.size() || columns.size() != describeCns.size()) {
            return metricDTOList;
        }

        // 预处理metricsQuery缓存：将列名与对应的MetricsQuery对象建立映射关系
        Map<String, MetricsQuery> queryMap = new HashMap<>();
        for (int j = 1; j < columns.size(); j++) { // 跳过时间戳列（索引0）
            String column = columns.get(j);
            MetricsQuery mq = metricsQueryService.findOpenByIdentifier(column);
            queryMap.put(column, mq);
        }

        for (Object value : values) {
            if (!(value instanceof List)) {
                continue;
            }
            List<?> row = (List<?>) value;
            if (row.size() != columns.size() || !(row.get(0) instanceof Number)) {
                continue;
            }

            // 预分配容量，避免扩容
            List<Number> serieValues = (List<Number>) row;
            Number timestampNumber = serieValues.get(0);
            if (timestampNumber == null) {
                continue;
            }
            long timestamp = timestampNumber.longValue();

            // 处理指标数据：遍历除时间戳外的每个指标列生成MetricDTO
            for (int j = 1; j < columns.size(); j++) {
                Number serieValueI = serieValues.get(j);
                if (serieValueI == null) {
                    continue;
                }
                String column = columns.get(j);
                String unit = units.get(j);
                String describeCn = describeCns.get(j);
                if (column == null || unit == null || describeCn == null) {
                    continue;
                }
                MetricsQuery mq = queryMap.get(column);
                if (mq == null) {
                    continue;
                }
                MetricDTO metricDTO = MetricDTO.builder()
                        .metric(column)
                        .metricsVal(serieValueI.doubleValue())
                        .unit(unit)
                        .name(describeCn)
                        .timestamp(timestamp)
                        .tag(tag)
                        .build();
                metricDTOList.add(metricDTO);
            }
        }
        return metricDTOList;
    }


    @SneakyThrows
    @Override
    public Set<Map<String, String>> noDataResult(DatabuffMonitorView monitor, Collection<QueryRequest> queries) {
        /*
         * 检查参数有效性。当监控对象或查询请求为空时直接返回空结果
         */
        if (monitor == null || CollectionUtils.isEmpty(queries)) {
            return new HashSet<>();
        }
        final long now = System.currentTimeMillis();
        final long lastUpdateTime = monitor.getUpdateTime().getTime();

        final Long monitorId = monitor.getId();
        /*
         * 从缓存中获取或计算触发器结果。若缓存不存在则调用noData7DayResult方法计算
         */
        JSONObject triggerWrapper = historyTriggerCache.get(monitorId, () -> noData7DayResult(queries, now, lastUpdateTime));

        if (triggerWrapper == null) {
            return new HashSet<>();
        }

        /*
         * 判断缓存是否过期的逻辑：缓存过期时间已到 或 监控对象最后更新时间与缓存记录不一致
         */
        final long expiredAt = triggerWrapper.getLongValue(EXPIRED_AT);
        final boolean expired = expiredAt < now || lastUpdateTime != triggerWrapper.getLongValue(LAST_UPDATE_TIME);
        if (expired) {
            // 清除已过期缓存并重新计算结果
            historyTriggerCache.invalidate(monitorId);
            triggerWrapper = noData7DayResult(queries, now, lastUpdateTime);
            historyTriggerCache.put(monitorId, triggerWrapper);
        }
        if (triggerWrapper == null) {
            return new HashSet<>();
        }

        /*
         * 获取历史触发器记录，若为空则返回空结果
         */
        final Set<Map<String, String>> historyTriggers = triggerWrapper.getObject(TRIGGERS, Set.class);
        if (historyTriggers == null) {
            return new HashSet<>();
        }

        /*
         * 获取当前无数据结果，若为空则返回空结果
         */
        final Set<Map<String, String>> noDataResult = this.noDataResult(queries);
        if (noDataResult == null) {
            return new HashSet<>();
        }

        /*
         * 计算最终结果集：筛选出历史记录中存在但当前无数据结果中不存在的触发器项
         */
        Set<Map<String, String>> resultSet = new HashSet<>(historyTriggers);
        resultSet.removeAll(noDataResult);
        return resultSet;
    }


    @SneakyThrows
    @Override
    public Set<Map<String, String>> delayResult(DatabuffMonitorView monitor, Collection<QueryRequest> queries, Integer evaluationDelay) {
        /**
         * 检查参数有效性，若无效则直接返回空集合
         */
        if (monitor == null || CollectionUtils.isEmpty(queries)) {
            return new HashSet<>();
        }
        final long now = System.currentTimeMillis();
        final long lastUpdateTime = monitor.getUpdateTime().getTime();

        final Long monitorId = monitor.getId();
        JSONObject triggerWrapper = historyTriggerCache.get(monitorId, () -> noData7DayResult(queries, now, lastUpdateTime));

        /**
         * 若缓存未命中直接返回空集合
         */
        if (triggerWrapper == null) {
            return new HashSet<>();
        }

        // 从缓存中获取过期时间
        final long expiredAt = triggerWrapper.getLongValue(EXPIRED_AT);
        // 5分钟后或者最后更新时间不一致，判定为过期
        final boolean expired = expiredAt < now || lastUpdateTime != triggerWrapper.getLongValue(LAST_UPDATE_TIME);
        if (expired) {
            // 缓存过期，清除缓存并重新计算结果
            historyTriggerCache.invalidate(monitorId);
            triggerWrapper = noData7DayResult(queries, now, lastUpdateTime);
            historyTriggerCache.put(monitorId, triggerWrapper);
        }
        if (triggerWrapper == null) {
            return new HashSet<>();
        }
        final Set<Map<String, String>> historyTriggers = triggerWrapper.getObject(TRIGGERS, Set.class);
        if (historyTriggers == null) {
            return new HashSet<>();
        }
        final Set<Map<String, String>> delayResult = this.delayResult(evaluationDelay, queries);
        if (delayResult == null) {
            return new HashSet<>();
        }

        /**
         * 计算历史触发器与延迟结果的差异集合
         */
        Set<Map<String, String>> resultSet = new HashSet<>(historyTriggers);
        delayResult.removeAll(resultSet);
        return delayResult;
    }


    /**
     * 这个方法用于查询在指定检测范围内没有数据，但在默认3小时内有数据的对象。
     *
     * @param queries 一个包含查询条件的 JSON 对象列表，每个对象表示一个指标的查询
     * @return 一个 Map，键是对象的属性，值是对象的数据序列
     */
//    @Override
    public Set<Map<String, String>> noDataResult(Collection<QueryRequest> queries) {
        final Set<Map<String, String>> result = new HashSet<>();
        if (CollectionUtils.isEmpty(queries)) {
            return result;
        }

        // 要求表达式expressionArr和queries的遍历顺序是一致的，否则计算会出错
        for (QueryRequest query : queries) {
            // 使用克隆对对象做深拷贝，防止数据被串改
            query = JSONB.copy(query);
            if (query == null) {
                continue;
            }

            // 优化性能，强制改为求last
            query.setAggs("last");
            // 不需要根据时间分组
            query.setInterval(null);
            final Map<Map<String, String>, TSDBSeries> queryResult = this.seriesResult(query);
            result.addAll(queryResult.keySet());
        }
        return result;
    }

    public Set<Map<String, String>> delayResult(Integer evaluationDelay, Collection<QueryRequest> queries) {
        final Set<Map<String, String>> result = new HashSet<>();
        if (CollectionUtils.isEmpty(queries)) {
            return result;
        }

        // 要求表达式expressionArr和queries的遍历顺序是一致的，否则计算会出错
        for (QueryRequest query : queries) {
            // 使用克隆对对象做深拷贝，防止数据被串改
            query = JSONB.copy(query);
            if (query == null) {
                continue;
            }

            // 优化性能，强制改为求last
            query.setAggs("last");

            // 时间范围
            query.setPeriod(evaluationDelay);
            query.setStart(null);
            query.setEnd(null);

            // 不需要根据时间分组
            query.setInterval(null);
            final Map<Map<String, String>, TSDBSeries> queryResult = this.seriesResult(query);
            result.addAll(queryResult.keySet());
        }
        return result;
    }

    /**
     * 处理7天内无数据的查询结果，并生成包含过期时间、最后更新时间和触发器信息的JSON对象。
     *
     * @param queries        需要查询的请求集合
     * @param now            当前时间戳（毫秒）
     * @param lastUpdateTime 上次数据更新时间戳（毫秒）
     * @return 包含过期时间、最后更新时间和触发器集合的JSON对象；若无查询请求则返回null
     */
    @SneakyThrows
    public JSONObject noData7DayResult(Collection<QueryRequest> queries, Long now, Long lastUpdateTime) {


        // 计算结果过期时间（设置为当前时间+5分钟）
        long expiredAt = now + 5 * ONE_MIN_MS_LONG;

        // 若查询请求集合为空则直接返回null
        if (CollectionUtils.isEmpty(queries)) {
            return null;
        }

        // 存储触发器信息的集合
        Set<Map<String, String>> triggers = new HashSet<>();

        // 遍历每个查询请求并处理
        for (QueryRequest queryRequest : queries) {
            // 跳过空请求
            if (queryRequest == null) {
                continue;
            }
            // 深拷贝查询请求对象以避免数据被意外修改
            queryRequest = JSONB.copy(queryRequest);

            // 设置聚合方式为"last"以优化性能
            queryRequest.setAggs("last");
            // 清除时间分组设置，避免按时间间隔分组
            queryRequest.setInterval(null);
            // 设置查询时间范围为配置的noDataPeriod（默认3小时前数据）
            queryRequest.setPeriod(refreshScopeConfig.getNoDataPeriod());

            // 执行查询并获取结果
            final Map<Map<String, String>, TSDBSeries> query7DayMap = this.seriesResult(queryRequest);

            // 若查询结果为空则跳过当前请求
            if (MapUtils.isEmpty(query7DayMap)) {
                continue;
            }
            // 将查询结果中的触发器信息添加到集合
            for (Map<String, String> trigger : query7DayMap.keySet()) {
                if (trigger == null) {
                    continue;
                }
                triggers.add(trigger);
            }
        }
        return new JSONObject()
                .fluentPut(EXPIRED_AT, expiredAt)
                .fluentPut(LAST_UPDATE_TIME, lastUpdateTime)
                .fluentPut(TRIGGERS, triggers);
    }


}
