package com.databuff.util;

import com.github.pagehelper.PageInfo;

import java.util.List;

public class PaginationUtil {

    public static <T> PageInfo<T> paginate(List<T> list, int pageNum, int pageSize) {
        if (pageSize <= 0) {
            pageSize = 10;
        }
        int total = list.size();
        int pages = (int) Math.ceil((double) total / pageSize);
        int start = (pageNum - 1) * pageSize;
        int end = Math.min(start + pageSize, total);

        List<T> paginatedList = list.subList(start, end);

        PageInfo<T> pageInfo = PageInfo.of(paginatedList, 1);
        pageInfo.setTotal(total);
        pageInfo.setPageNum(pageNum);
        pageInfo.setPageSize(pageSize);
        pageInfo.setPages(pages);
        pageInfo.setList(paginatedList);

        return pageInfo;
    }

    public static <T> PageInfo<T> paginate(List<T> list, int pageNum, int pageSize, int total) {
        if (pageSize <= 0) {
            pageSize = 10;
        }
        int pages = (int) Math.ceil((double) total / pageSize);
        int start = (pageNum - 1) * pageSize;
        int end = Math.min(start + pageSize, total);

        if (start >= total) {
            return new PageInfo<>();
        }
        if (end > total) {
            end = total;
        }
        if (start < 0) {
            start = 0;
        }
        if (list == null || list.size() == 0) {
            return new PageInfo<>();
        }

        List<T> paginatedList;
        if (list.size() < end || list.size() < start) {
            paginatedList = list;
        } else {
            paginatedList = list.subList(start, end);
        }

        PageInfo<T> pageInfo = PageInfo.of(paginatedList, 1);
        pageInfo.setTotal(total);
        pageInfo.setPageNum(pageNum);
        pageInfo.setPageSize(pageSize);
        pageInfo.setPages(pages);
        pageInfo.setList(paginatedList);

        return pageInfo;
    }
}