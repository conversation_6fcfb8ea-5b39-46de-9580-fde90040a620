package com.databuff.util;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.databuff.entity.MetricsInsert;
import com.databuff.entity.MetricsQuery;
import lombok.extern.slf4j.Slf4j;

import java.util.*;

/**
 * 指标转换工具类
 * 提供指标格式转换和处理的静态方法
 */
@Slf4j
public class MetricsTransformUtil {

    /**
     * core指标转换成insert指标，供agent上报数据
     *
     * @param metricsInserts 指标插入列表
     * @param openMetricJson 开放的指标JSON
     * @return 转换后的JSON对象
     */
    public static JSONObject core2Insert(List<MetricsInsert> metricsInserts, JSONObject openMetricJson) {
        if (openMetricJson == null) {
            return new JSONObject();
        }

        if (metricsInserts == null) {
            return openMetricJson;
        }

        Map<String, List<String>> core2InsertMetrics = new HashMap<>();
        metricsInserts.forEach(m -> {
            String coreMetric = m.getMeasurement() + "." + m.getField();
            if (core2InsertMetrics.containsKey(coreMetric)) {
                core2InsertMetrics.get(coreMetric).add(m.getIdentifier());
            } else {
                List<String> list = new ArrayList<>();
                list.add(m.getIdentifier());
                core2InsertMetrics.put(coreMetric, list);
            }
        });

        JSONObject resultJson = openMetricJson.clone();
        for (Map.Entry<String, Object> openMjobj : resultJson.entrySet()) {
            JSONObject openMj = resultJson.getJSONObject(openMjobj.getKey());
            for (Map.Entry<String, Object> v : openMj.entrySet()) {
                if (!(v.getValue() instanceof JSONArray)) {
                    continue;
                }
                List<String> metrics = JSONObject.parseArray(v.getValue().toString(), String.class);
                List<String> newMetrics = new ArrayList<>();
                for (String m : metrics) {
                    if (core2InsertMetrics.containsKey(m)) {
                        newMetrics.addAll(core2InsertMetrics.get(m));
                    } else {
                        log.info("metric:{} not found in core metrics", m);
                        newMetrics.add(m);
                    }
                }
                openMj.put(v.getKey(), newMetrics);
            }
            resultJson.put(openMjobj.getKey(), openMj);
        }
        return resultJson;
    }

    /**
     * 从 openMetricJson 中删除指定的 metricsInserts
     *
     * @param metricsInserts 要删除的指标插入列表
     * @param openMetricJson 开放的指标JSON
     * @return 删除指定指标后的JSON对象
     */
    public static JSONObject core2remove(List<MetricsInsert> metricsInserts, JSONObject openMetricJson) {
        if (openMetricJson == null || metricsInserts == null || metricsInserts.isEmpty()) {
            return openMetricJson != null ? openMetricJson.clone() : new JSONObject();
        }

        // 收集要删除的指标标识符
        Set<String> metricsToRemove = new HashSet<>();
        Map<String, Set<String>> coreMetricsToRemove = new HashMap<>();

        // 收集要删除的core指标和对应的insert指标
        metricsInserts.forEach(m -> {
            String coreMetric = m.getMeasurement() + "." + m.getField();
            String identifier = m.getIdentifier();
            metricsToRemove.add(identifier);

            // 将每个core指标对应的insert指标收集起来
            if (!coreMetricsToRemove.containsKey(coreMetric)) {
                coreMetricsToRemove.put(coreMetric, new HashSet<>());
            }
            coreMetricsToRemove.get(coreMetric).add(identifier);
        });

        // 克隆原始JSON以避免修改原始对象
        JSONObject resultJson = openMetricJson.clone();

        // 遍历openMetricJson并删除指定的指标
        for (Map.Entry<String, Object> openMjobj : resultJson.entrySet()) {
            JSONObject openMj = resultJson.getJSONObject(openMjobj.getKey());
            for (Map.Entry<String, Object> v : openMj.entrySet()) {
                if (!(v.getValue() instanceof Collection)) {
                    continue;
                }

                Collection<String> metrics = (Collection<String>) v.getValue();
                List<String> updatedMetrics = new ArrayList<>();

                for (String metric : metrics) {
                    // 如果是core指标格式（measurement.field）
                    if (coreMetricsToRemove.containsKey(metric)) {
                        // 如果这个core指标需要删除，则跳过不添加到新列表
                        log.debug("Removing core metric: {}", metric);
                        continue;
                    } else if (metricsToRemove.contains(metric)) {
                        // 如果这个指标标识符需要删除，则跳过
                        log.debug("Removing metric identifier: {}", metric);
                        continue;
                    } else {
                        // 否则保留这个指标
                        updatedMetrics.add(metric);
                    }
                }

                // 更新指标列表
                openMj.put(v.getKey(), updatedMetrics);
            }
            resultJson.put(openMjobj.getKey(), openMj);
        }

        return resultJson;
    }


    public static JSONObject query2Insert(List<MetricsQuery> metricsInserts, JSONObject openMetricJson) {
        if (openMetricJson == null) {
            return new JSONObject();
        }

        if (metricsInserts == null) {
            return openMetricJson;
        }

        Map<String, List<String>> core2InsertMetrics = new HashMap<>();
        metricsInserts.forEach(m -> {
            String coreMetric = m.getMeasurement() + "." + m.getField();
            if (core2InsertMetrics.containsKey(coreMetric)) {
                core2InsertMetrics.get(coreMetric).add(m.getIdentifier());
            } else {
                List<String> list = new ArrayList<>();
                list.add(m.getIdentifier());
                core2InsertMetrics.put(coreMetric, list);
            }
        });

        JSONObject resultJson = openMetricJson.clone();
        for (Map.Entry<String, Object> openMjobj : resultJson.entrySet()) {
            JSONObject openMj = resultJson.getJSONObject(openMjobj.getKey());
            for (Map.Entry<String, Object> v : openMj.entrySet()) {
                if (!(v.getValue() instanceof JSONArray)) {
                    continue;
                }
                List<String> metrics = JSONObject.parseArray(v.getValue().toString(), String.class);
                List<String> newMetrics = new ArrayList<>();
                for (String m : metrics) {
                    if (core2InsertMetrics.containsKey(m)) {
                        newMetrics.addAll(core2InsertMetrics.get(m));
                    } else {
                        log.info("metric:{} not found in core metrics", m);
                        newMetrics.add(m);
                    }
                }
                newMetrics.addAll(core2InsertMetrics.keySet());
                openMj.put(v.getKey(), newMetrics);
            }
            resultJson.put(openMjobj.getKey(), openMj);
        }
        return resultJson;
    }

    /**
     * 从 openMetricJson 中删除指定的 metricsInserts
     *
     * @param metricsInserts 要删除的指标插入列表
     * @param openMetricJson 开放的指标JSON
     * @return 删除指定指标后的JSON对象
     */
    public static JSONObject query2remove(List<MetricsQuery> metricsInserts, JSONObject openMetricJson) {
        if (openMetricJson == null || metricsInserts == null || metricsInserts.isEmpty()) {
            return openMetricJson != null ? openMetricJson.clone() : new JSONObject();
        }

        // 收集要删除的指标标识符
        Set<String> metricsToRemove = new HashSet<>();
        Map<String, Set<String>> coreMetricsToRemove = new HashMap<>();

        // 收集要删除的core指标和对应的insert指标
        metricsInserts.forEach(m -> {
            String coreMetric = m.getMeasurement() + "." + m.getField();
            String identifier = m.getIdentifier();
            metricsToRemove.add(identifier);

            // 将每个core指标对应的insert指标收集起来
            if (!coreMetricsToRemove.containsKey(coreMetric)) {
                coreMetricsToRemove.put(coreMetric, new HashSet<>());
            }
            coreMetricsToRemove.get(coreMetric).add(identifier);
        });

        // 克隆原始JSON以避免修改原始对象
        JSONObject resultJson = openMetricJson.clone();

        // 遍历openMetricJson并删除指定的指标
        for (Map.Entry<String, Object> openMjobj : resultJson.entrySet()) {
            JSONObject openMj = resultJson.getJSONObject(openMjobj.getKey());
            for (Map.Entry<String, Object> v : openMj.entrySet()) {
                if (!(v.getValue() instanceof Collection)) {
                    continue;
                }

                Collection<String> metrics = (Collection<String>) v.getValue();
                List<String> updatedMetrics = new ArrayList<>();

                for (String metric : metrics) {
                    // 如果是core指标格式（measurement.field）
                    if (coreMetricsToRemove.containsKey(metric)) {
                        // 如果这个core指标需要删除，则跳过不添加到新列表
                        log.debug("Removing core metric: {}", metric);
                        continue;
                    } else if (metricsToRemove.contains(metric)) {
                        // 如果这个指标标识符需要删除，则跳过
                        log.debug("Removing metric identifier: {}", metric);
                        continue;
                    } else {
                        // 否则保留这个指标
                        updatedMetrics.add(metric);
                    }
                }

                // 更新指标列表
                openMj.put(v.getKey(), updatedMetrics);
            }
            resultJson.put(openMjobj.getKey(), openMj);
        }

        return resultJson;
    }
}
