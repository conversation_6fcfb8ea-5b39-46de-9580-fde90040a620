package com.databuff.processor.apm.impl.out;

import com.databuff.common.constants.Constant;

import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

import static com.databuff.common.constants.Constant.*;

public class ESOutProcessor extends OutProcessor {

    private static final Map<String, String> FILTERED_COLUMNS = new ConcurrentHashMap<>();

    static {
        FILTERED_COLUMNS.put(Constant.COLUMN_SRC_SERVICE_INSTANCE, Constant.PARAMS_SRC_SERVICE_INSTANCE);
        FILTERED_COLUMNS.put(Constant.COLUMN_SERVICE_INSTANCE, Constant.PARAMS_SERVICE_INSTANCE);
        FILTERED_COLUMNS.put(Constant.COLUMN_RESOURCE, Constant.PARAMS_RESOURCE);
        FILTERED_COLUMNS.put(Constant.COLUMN_ROOT_RESOURCE, Constant.PARAMS_ROOT_RESOURCE_QUERY);
    }

    @Override
    public List<String> getCaseType() {
        return Arrays.asList(CASE_TYPE_ES_CLIENT);
    }

    @Override
    public Map<String, String> getFilteredColumns() {
        return FILTERED_COLUMNS;
    }

    @Override
    public String getComponentType() {
        return TYPE_SERVICE_ES;
    }

    @Override
    public String getSt() {
        return ST_DB;
    }
}
