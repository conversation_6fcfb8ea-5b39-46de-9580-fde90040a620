package com.databuff.processor.apm.impl.out;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.databuff.common.constants.Constant;
import com.databuff.entity.TraceServiceEntity;
import com.databuff.processor.apm.Processor;
import com.databuff.service.ServiceSyncService;

public abstract class OutProcessor extends Processor {

    public abstract String getComponentType();

    public abstract String getSt();

    @Override
    public String processRootUrl(String apiKey, String service, String serviceId, long fromTime, long toTime, long abnormalFromTime, long abnormalToTime, ServiceSyncService serviceSyncService, JSONObject rootJSONObject) {
        JSONArray rootsJSONArray = rootJSONObject.getJSONArray(Constant.ROOTS);
        JSONObject rootItem = rootsJSONArray.getJSONObject(0);
        String outService = rootItem.getString(Constant.Trace.SERVICE);
        TraceServiceEntity outServiceEntity = serviceSyncService.getTraceServiceEntityByChineseName(outService);
        if (outServiceEntity == null) {
            return null;
        }
        StringBuilder urlBuilder = new StringBuilder(Constant.PATH_SERVICE_CALL);
        urlBuilder.append(Constant.PARAMS_COMPONENT_TYPE).append(getComponentType()).append(Constant.PARAMS_JOIN)
                .append(Constant.PARAMS_SRC_SID).append(serviceId).append(Constant.PARAMS_JOIN)
                .append(Constant.PARAMS_SRC_SN).append(service).append(Constant.PARAMS_JOIN)
                .append(Constant.PARAMS_SRC_ST).append(Constant.ST_WEB).append(Constant.PARAMS_JOIN)
                .append(Constant.PARAMS_SID).append(outServiceEntity.getId()).append(Constant.PARAMS_JOIN)
                .append(Constant.PARAMS_SN).append(outService).append(Constant.PARAMS_JOIN)
                .append(Constant.PARAMS_ST).append(getSt()).append(Constant.PARAMS_JOIN)
                .append(Constant.PARAMS_FROM_TIME).append(fromTime).append(Constant.PARAMS_JOIN)
                .append(Constant.PARAMS_TO_TIME).append(toTime);
        addParams(urlBuilder);
        String rootUrl = urlBuilder.toString();
        rootJSONObject.put(Constant.URL, rootUrl);
        return rootUrl;
    }

    protected void addParams(StringBuilder urlBuilder) {

    }
}
