package com.databuff.processor.apm.impl.in;

import com.databuff.common.constants.Constant;

import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

public class MQServerProcessor extends InProcessor {
    private static final Map<String, String> FILTERED_COLUMNS = new ConcurrentHashMap<>();

    static {
        FILTERED_COLUMNS.put(Constant.COLUMN_SERVICE_INSTANCE, Constant.PARAMS_SI);
        FILTERED_COLUMNS.put(Constant.COLUMN_RESOURCE, Constant.PARAMS_RESOURCE);
    }

    @Override
    public List<String> getCaseType() {
        return Arrays.asList(Constant.CASE_TYPE_MQ_SERVER);
    }

    @Override
    public Map<String, String> getFilteredColumns() {
        return FILTERED_COLUMNS;
    }

    @Override
    public String getComponentType() {
        return Constant.TYPE_SERVICE_MQ;
    }
}
