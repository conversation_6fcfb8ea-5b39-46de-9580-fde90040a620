package com.databuff.util;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.curator.framework.CuratorFramework;
import org.apache.curator.framework.recipes.cache.CuratorCache;
import org.apache.curator.framework.recipes.cache.CuratorCacheListener;
import org.apache.curator.framework.recipes.cache.CuratorCacheListenerBuilder;

import java.util.concurrent.ConcurrentHashMap;

/**
 * 分布式配置管理器
 */
@Api(value = "DConfigRegisterManager", tags = "分布式配置管理器")
public class DConfigRegisterManager implements IDConfigRegister {

    protected final ConcurrentHashMap<String, CuratorCache> cacheMap = new ConcurrentHashMap<>();
    protected final ConcurrentHashMap<String, CuratorCacheListener> listenerMap = new ConcurrentHashMap<>();
    protected CuratorFramework curatorFramework;

    public DConfigRegisterManager(CuratorFramework curatorFramework) {
        this.curatorFramework = curatorFramework;
    }

    /**
     * 给指定路径的节点添加监听器
     */
    @ApiOperation(value = "订阅节点变化", notes = "给指定路径的节点添加监听器")
    @Override
    public void subscribeNodeChange(String path, CuratorCacheListenerBuilder builder) {
        if (path == null || builder == null) {
            throw new IllegalArgumentException("Path and builder cannot be null");
        }
        // 同一个path只能订阅一次
        cacheMap.computeIfAbsent(path, p -> {
            final CuratorCache cache = CuratorCache.build(curatorFramework, p);
            listenerMap.computeIfAbsent(path, l -> {
                final CuratorCacheListener listener = builder.build();
                if (cache != null) {
                    cache.listenable().addListener(listener);
                    cache.start();
                }
                return listener;
            });
            return cache;
        });
    }

    /**
     * 移除指定路径节点的监听器
     */
    @ApiOperation(value = "取消订阅节点变化", notes = "移除指定路径节点的监听器")
    @Override
    public void unsubscribeNodeChange(String path) {
        CuratorCache cache = cacheMap.get(path);
        if (cache != null) {
            CuratorCacheListener listener = listenerMap.get(path);
            if (listener != null) {
                cache.listenable().removeListener(listener);
            }
            cache.close();
        }
        listenerMap.remove(path);
        cacheMap.remove(path);
    }
}
