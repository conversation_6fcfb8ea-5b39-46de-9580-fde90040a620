/*
package com.databuff.webapp.configcenter.mapper;


import com.databuff.webapp.configcenter.model.ConfigInfo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Repository;

import java.util.List;

@Mapper
@Repository
public interface NacosMapper {


//    @Select("select id, data_id, group_id, content, md5, gmt_create, gmt_modified, src_user, src_ip, app_name, tenant_id, c_desc," +
//    "c_use, effect, `type`, c_schema from nacos.config_info where group_id=#{groupId} and tenant_id=#{tenantId}")

    @Select("select * from config_info where group_id=#{groupId} and tenant_id=#{tenantId}")
    List<ConfigInfo> getConfigByGroupIdAndNamespace(@Param("groupId") String groupId, @Param("tenantId") String tenantId);


    @Select("select * from config_info where tenant_id=#{tenantId}")
    List<ConfigInfo> getConfigsByNamespace(@Param("tenantId") String tenantId);

}
*/
