/*
package com.databuff.webapp.configcenter.service;

import com.alibaba.nacos.api.config.ConfigService;
import com.databuff.webapp.configcenter.model.ConfigInfo;

import java.util.List;
import java.util.Map;

public interface NacosService {

    boolean publishConfig(String groupId, String dataId, String content);

    List<ConfigInfo> publishDefaultConfig(String groupId, String namespace, ConfigService configService);

    Map<String, String> getConfig(String groupId, boolean fetchUpdate) throws Exception;

    String getConfig(String groupId, String dataId) throws Exception;

    Map<String, String> getConfigItems(String configFile, List<String> items);

    boolean updateConfig(String key, String value, String filePath);

    boolean updateConfigs(List<String> keys, List<String> values, String filePath);
}
*/
