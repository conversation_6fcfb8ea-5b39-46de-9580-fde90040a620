package com.databuff.webapp.util;

import com.hankcs.algorithm.AhoCorasickDoubleArrayTrie;
import com.hankcs.algorithm.AhoCorasickDoubleArrayTrie.Hit;

import java.util.Arrays;
import java.util.Comparator;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Map;
import java.util.Set;

/**
 * AndroidStackClassifier 使用 AhoCorasickDoubleArrayTrie 实现快速前缀匹配，
 * 判断给定的类名是否属于“系统代码”。
 * <p>
 * 定义：
 * - “系统代码” 指操作系统核心、厂商预装的系统应用和服务。
 * - 如果厂商开源组件虽然使用了与系统组件相同的前缀，但实际不属于系统代码，
 *   则应在 NON_SYSTEM_EXCLUSIONS 集合中列出其全限定类名进行排除。
 *
 * 本实现仅维护 SYSTEM_PREFIXES_ARRAY 与 NON_SYSTEM_EXCLUSIONS，
 * 通过前缀匹配实现对堆栈中系统代码与非系统代码的区分。
 */
public class AndroidStackClassifier {

    /**
     * 系统包名前缀数组（全部小写），用于匹配所有系统代码。
     * 注意：部分前缀如 "androidx." 根据业务需求可视为系统级，
     * 若你认为 AndroidX 属于应用代码，则可从此数组中移除 "androidx."。
     */
    private static final String[] SYSTEM_PREFIXES_ARRAY = new String[] {
            // ======== AOSP / Google / Android 基础 ========
            "android.",
            "androidx.",            // Android Jetpack 库 (根据需求可视为系统级)
            "com.android.",
            "com.google.android.",
            "dalvik.",
            "java.",
            "javax.",
            "kotlin.",
            "sun.",

            // ======== 小米/Redmi/Poco ========
            "miui.",
            "com.miui.system.",
            "com.xiaomi.system.",
            "com.xiaomi.push.",
            "com.xiaomi.analytics.",
            "com.xiaomi.appmanager.",

            // ======== 华为/Honor ========
            "huawei.android.",
            "com.huawei.android.",
            "com.huawei.system.",
            "com.huawei.hiai.",
            "com.huawei.security.",
            "hw.",
            "android.os.HwBinder",
            "android.app.HwActivityThread",

            // ======== OPPO/Realme/OnePlus ========
            "coloros.",
            "com.coloros.system.",
            "com.oplus.system.",
            "com.oppo.system.",
            "com.heytap.system.",
            "com.nearme.system.",

            // ======== vivo/iQOO ========
            "funtouch.",
            "com.vivo.android.",
            "com.vivo.system.",
            "com.bbk.system.",
            "com.iqoo.engine.",

            // ======== 魅族/Meizu ========
            "flyme.",
            "com.meizu.android.",
            "com.meizu.system.",

            // ======== 努比亚/Nubia ========
            "nubia.",
            "cn.nubia.android.",
            "cn.nubia.system.",

            // ======== 联想/Lenovo、摩托罗拉/Motorola ========
            "lenovo.",
            "com.lenovo.android.",
            "com.motorola.android.",

            // ======== 中兴/ZTE ========
            "zte.",
            "com.zte.android.",

            // ======== 荣耀/Honor ========
            "roco.",
            "com.hihonor.",
            "com.honor.android.",

            // ======== 三星/Samsung ========
            "samsung.",
            "com.samsung.android.",
            "com.sec.android.",
            "android.app.SamsungActivityThread",

            // ======== 索尼/Sony ========
            "sony.",
            "com.sonymobile.android.",

            // ======== LG ========
            "lge.",
            "com.lge.android.",
            "com.lge.system.",

            // ======== HTC ========
            "htc.",
            "com.htc.android.",

            // ======== 华硕/ASUS ========
            "asus.",
            "com.asus.android.",

            // ======== Tecno/Infinix/Itel (传音控股) ========
            "tecno.",
            "infinix.",
            "itel.",
            "com.transsion.",
            "com.transsion.system.",

            // ======== 夏普/Sharp ========
            "sharp.",
            "com.sharp.android.",

            // ======== 诺基亚/Nokia ========
            "nokia.",
            "com.nokia.android.",

            // ======== 芯片厂商相关 ========
            "qualcomm.",
            "qti.",
            "com.qualcomm.",
            "mediatek.",
            "com.mediatek.",
            "unisoc.",
            "com.unisoc.",

            // ======== 其他系统级组件 ========
            "com.systemui.",
            "android.net.",
            "android.webkit.",
            "com.android.webview.",
            "org.chromium.android_webview.",
            "com.lbe.security.",
            "com.qihoo360.security."
    };

    /**
     * 非系统排除集合：
     * 该集合列出了那些虽然使用系统前缀，但实际上不属于系统代码（如厂商开放的开源组件）的全限定类名。
     * 根据实际情况添加需要排除的类名。
     */
    private static final Set<String> NON_SYSTEM_EXCLUSIONS = new HashSet<>(Arrays.asList(
            // 示例：假设 "com.huawei.opensdk.SomeComponent" 属于华为开放SDK，而非系统预装组件
            "com.huawei.opensdk.SomeComponent"
            // 可根据需要继续添加其他排除项
    ));

    // 使用 AhoCorasickDoubleArrayTrie 实现快速前缀匹配
    private static final AhoCorasickDoubleArrayTrie<String> SYSTEM_PREFIX_TRIE;

    static {
        // 构造映射，键和值均为前缀字符串
        Map<String, String> prefixMap = new HashMap<>();
        // 对于加速匹配，可先对数组按逆序（字符串长的优先匹配）排序
        String[] prefixes = SYSTEM_PREFIXES_ARRAY.clone();
        Arrays.sort(prefixes, Comparator.reverseOrder());
        for (String prefix : prefixes) {
            prefixMap.put(prefix, prefix);
        }
        SYSTEM_PREFIX_TRIE = new AhoCorasickDoubleArrayTrie<>();
        SYSTEM_PREFIX_TRIE.build(prefixMap);
    }

    /**
     * 判断传入的类名是否属于系统代码。
     *
     * <p>判断流程：
     * <ol>
     *   <li>如果类名为 null，则返回 false。</li>
     *   <li>如果类名在 NON_SYSTEM_EXCLUSIONS 集合中，则返回 false。</li>
     *   <li>否则，使用 Trie 前缀匹配，若匹配到起始位置为 0 的前缀，则返回 true；否则返回 false。</li>
     * </ol>
     *
     * @param className 反混淆后堆栈行提取出的类名 (例如 "android.app.ActivityThread")
     * @return true 表示该类名归为系统代码；false 表示归为非系统代码（用户或第三方代码）
     */
    public static boolean isSystemClass(String className) {
        if (className == null) {
            return false;
        }
        // 如果在非系统排除列表中，则直接返回 false
        if (NON_SYSTEM_EXCLUSIONS.contains(className)) {
            return false;
        }
        // 获取匹配结果列表
        // 注意：AhoCorasickDoubleArrayTrie 的 parseText 方法返回所有匹配，检查是否有匹配起始位置为0的
        for (Hit<String> hit : SYSTEM_PREFIX_TRIE.parseText(className)) {
            if (hit.begin == 0) {
                return true;
            }
        }
        return false;
    }
}
