package com.databuff.webapp.task;

import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.SchedulingConfigurer;
import org.springframework.scheduling.config.ScheduledTaskRegistrar;

import java.util.concurrent.Executors;

/**
 * @author:TianMing
 * @date: 2021/11/26
 * @time: 16:41
 */
@Configuration
public class ScheduleConfig implements SchedulingConfigurer {
    @Override
    public void configureTasks(ScheduledTaskRegistrar taskRegistrar) {
        //根据业务需求设定线程池大小
        taskRegistrar.setScheduler(Executors.newScheduledThreadPool(10));
    }
}