package com.databuff.webapp.admin.model.system;

import lombok.Data;

/**
 * @ClassName SystemParamEntry
 * <AUTHOR>
 * @Date 2020/3/17 14:40
 * @Version 1.0
 */
@Data
public class SystemParamEntry {
    /** 操作系统. */
    private String osName;

    /** 总的物理内存. */
    private String totalMemorySize;
    /** 剩余的物理内存. */
    private String freePhysicalMemorySize;
    /** 已用的物理内存. */
    private String usedPhysicalMemorySize;
    /** 物理内存使用率. */
    private String memoryRatio;

    /** 线程总数. */
    private int totalThread;

    /** cpu使用率. */
    private String cpuRatio;
    /** 总的磁盘大小. */
    private String totalDask;
    /** 已使用的磁盘大小. */
    private String usedDask;
    /** 剩余的磁盘大小. */
    private String availDask;
    /** 磁盘使用率. */
    private String daskRate;

    /** 知微运行状态 */
    private String serviceStatus;
    /** 系统状态 */
    private String systemStatus;
}
