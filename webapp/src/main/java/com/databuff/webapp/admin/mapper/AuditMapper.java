package com.databuff.webapp.admin.mapper;


import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.databuff.webapp.admin.model.system.Audit;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Repository;


import java.util.List;

/**
 * @package com.databuff.mapper
 * @company: dacheng
 * @author: zlh
 * @createDate: 2021/5/8
 */
@Mapper
@Repository
public interface AuditMapper extends BaseMapper<Audit> {
    /**
     * 审计页面
     * @param audit
     * @return
     */
    @Select("<script>\n " +
            "select ds.id,ds.account as account,ds.create_time as createTime,ds.action as action,ds.ip as ip,du.responsible,du.mobile as mobile\n" +
            "        from dc_syslog ds\n" +
            "        LEFT JOIN dc_user du on  ds.account=du.account\n" +
            "        <where>\n" +
            "            <if test=\"query != null\">\n" +
            "              (\n" +
            "                ds.account like binary CONCAT('%',#{query},'%')\n" +
            "                or du.mobile like binary CONCAT('%',#{query},'%')\n" +
            "                or ds.action like binary CONCAT('%',#{query},'%')\n" +
            "                or du.responsible like binary CONCAT('%',#{query},'%')\n" +
            "              )\n" +
            "            </if>\n" +
            "            <if test=\"startTime != null\">\n" +
            "                and date(ds.create_time) between date(#{startTime}) and date(#{endTime})\n" +
            "            </if>\n" +
            "        </where>\n" +
            "            ORDER BY ds.create_time desc " +
            "</script>")
    List<Audit> selectAudit(Audit audit);

    /**
     * 批量删除日志
     * @param audits
     */
    @Delete("<script>\n" +
            "delete from dc_syslog where 1>2\n" +
            " or id in \n" +
            "<foreach collection=\"list\"  item=\"item\" open=\"(\" separator=\",\" close=\")\"  >\n" +
            "#{item.id}\n" +
            "</foreach>\n" +
            "</script>")
    void delLogsByIds(@Param(value = "list") List<Audit> audits);

    /**
     * 清空日志
     */
    @Delete("delete from dc_syslog")
    void truncateLogs();
}
