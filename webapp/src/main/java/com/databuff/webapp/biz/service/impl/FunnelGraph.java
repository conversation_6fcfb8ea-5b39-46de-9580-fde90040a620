package com.databuff.webapp.biz.service.impl;

// 使用您项目中的 V2 实体类

import com.databuff.entity.BizScenarioEdgeV2;
import com.databuff.entity.BizScenarioNodeV2;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

/**
 * 表示业务场景的有向图（通常是 DAG - 有向无环图）结构。
 * 此类使用 V2 版本的实体类 ({@link BizScenarioNodeV2}, {@link BizScenarioEdgeV2}) 作为输入来构建图。
 * <p>
 * 主要功能包括：
 * <ul>
 * <li>解析节点和边列表，构建图的内部表示（邻接表形式的父子关系）。</li>
 * <li>自动识别图的根节点（没有入度的节点）。</li>
 * <li>根据给定的目标节点 ID (targetNodeId)，重建从某个根节点出发，经过该目标节点，
 * 并按默认规则 (最深分支优先，ID 字母序 ASC 平局) 延伸至最终叶子节点的完整线性路径。</li>
 * <li>计算从图中任一节点出发可到达的最大深度（边的数量），用于分支选择。</li>
 * <li>查找整个图中最深的路径，并返回其完整的节点列表，作为默认展示路径。</li>
 * </ul>
 * <p>
 * 该类设计为在处理单个漏斗分析请求的生命周期内实例化和使用。
 * 深度计算包含简单的内存缓存 (ConcurrentHashMap) 以提高重复计算效率，并包含基本的循环检测。
 * </p>
 */
public class FunnelGraph {

    private static final Logger log = LoggerFactory.getLogger(FunnelGraph.class);

    /**
     * 存储所有节点对象 (V2)，通过节点 ID (String) 快速访问。
     */
    private final Map<String, BizScenarioNodeV2> nodesMap;

    /**
     * 存储父节点关系: Key=子节点ID (targetId), Value=父节点ID列表 (List<sourceId>)。
     */
    private final Map<String, List<String>> parentsMap;

    /**
     * 存储子节点关系: Key=父节点ID (sourceId), Value=子节点ID列表 (List<targetId>)。
     */
    private final Map<String, List<String>> childrenMap;

    /**
     * 存储识别出的所有根节点（没有入度的节点）的 ID 列表。
     */
    private final List<String> rootNodeIds;

    /**
     * 缓存从某个节点出发能到达的最大深度，避免重复计算。Key=节点ID, Value=最大深度。
     */
    private final Map<String, Integer> depthCache = new ConcurrentHashMap<>();

    /**
     * 构造函数，根据 V2 版本的节点和边列表初始化图结构。
     * 会执行预处理，构建父子关系邻接表，并识别根节点。
     *
     * @param nodes 业务场景中的所有 V2 节点列表 ({@link BizScenarioNodeV2})。不能为空。
     * @param edges 连接节点的 V2 边列表 ({@link BizScenarioEdgeV2})。不能为空。
     * @throws IllegalArgumentException 如果 nodes 或 edges 为 null。
     */
    public FunnelGraph(List<BizScenarioNodeV2> nodes, List<BizScenarioEdgeV2> edges) {
        if (nodes == null || edges == null) {
            throw new IllegalArgumentException("初始化 FunnelGraph 失败：节点列表和边列表不能为空。");
        }

        // 初始化内部存储结构
        nodesMap = new HashMap<>(nodes.size());
        parentsMap = new HashMap<>(nodes.size());
        childrenMap = new HashMap<>(nodes.size());
        Set<String> possibleRoots = new HashSet<>(); // 使用 Set 临时存储可能的根节点

        // 1. 处理节点列表，填充 nodesMap 并初始化 parent/child Map, 假定所有节点都可能是根
        log.debug("开始构建图，处理 {} 个节点...", nodes.size());
        for (BizScenarioNodeV2 node : nodes) {
            if (node == null || StringUtils.isBlank(node.getId())) {
                log.warn("发现无效的节点数据（null 或 ID 为空），已跳过。");
                continue;
            }
            String nodeId = node.getId();
            if (nodesMap.containsKey(nodeId)) {
                log.warn("发现重复的节点 ID: {}，将使用第一个遇到的节点。", nodeId);
                continue;
            }
            nodesMap.put(nodeId, node);
            parentsMap.put(nodeId, new ArrayList<>()); // 确保每个节点都有父节点列表（可能为空）
            childrenMap.put(nodeId, new ArrayList<>()); // 确保每个节点都有子节点列表（可能为空）
            possibleRoots.add(nodeId); // 初始假设所有节点都是根
        }
        log.debug("节点处理完成，共 {} 个有效节点。", nodesMap.size());


        // 2. 处理边列表，构建父子关系，并从 possibleRoots 中移除有入度的节点
        log.debug("处理 {} 条边...", edges.size());
        for (BizScenarioEdgeV2 edge : edges) {
            if (edge == null || StringUtils.isBlank(edge.getSource()) || StringUtils.isBlank(edge.getTarget())) {
                log.warn("发现无效的边数据（null 或 source/target 为空），已跳过: {}", edge);
                continue;
            }
            String sourceId = edge.getSource();
            String targetId = edge.getTarget();

            // 校验边连接的节点是否存在于 nodesMap 中，防止悬挂边
            if (!nodesMap.containsKey(sourceId) || !nodesMap.containsKey(targetId)) {
                log.warn("发现连接无效或不存在节点的边，已跳过: {} -> {}", sourceId, targetId);
                continue;
            }

            // 添加子节点关系 (source -> target)
            childrenMap.computeIfAbsent(sourceId, k -> new ArrayList<>()).add(targetId);
            // 添加父节点关系 (target -> source)
            parentsMap.computeIfAbsent(targetId, k -> new ArrayList<>()).add(sourceId);
            possibleRoots.remove(targetId);
        }
        log.debug("边处理完成。");


        // 3. 确定最终的根节点列表
        this.rootNodeIds = new ArrayList<>(possibleRoots);
        if (this.rootNodeIds.isEmpty() && !nodesMap.isEmpty()) {
            // 如果节点不为空但找不到根节点，说明图可能存在循环或者所有节点都有入度
            log.error("构建图警告：未检测到任何根节点！请检查图配置是否存在循环或所有节点都有入度。");
            // 这种情况可能需要抛出异常，或者根据业务定义处理
            // throw new IllegalStateException("图结构错误：找不到根节点。");
        } else {
            // 对根节点排序，确保后续处理（如查找默认路径）时的确定性
            Collections.sort(this.rootNodeIds);
            log.info("图构建完成。找到 {} 个根节点: {}", this.rootNodeIds.size(), this.rootNodeIds);
        }
    }

    // --- Public Accessor Methods (公共访问方法) ---

    /**
     * 获取图的所有根节点 ID 列表。
     * 根节点定义为没有入度的节点。
     *
     * @return 不可修改的根节点 ID 列表 (List<String>)。
     */
    public List<String> getRootNodeIds() {
        return Collections.unmodifiableList(rootNodeIds);
    }

    /**
     * 根据节点 ID 获取节点对象。
     *
     * @param nodeId 要查找的节点 ID。
     * @return 对应的 {@link BizScenarioNodeV2} 对象，如果图中不存在该 ID 则返回 null。
     */
    public BizScenarioNodeV2 getNode(String nodeId) {
        return nodesMap.get(nodeId);
    }

    /**
     * 获取指定节点的直接子节点 ID 列表。
     *
     * @param nodeId 父节点 ID。
     * @return 直接子节点的 ID 列表 (List<String>)。如果节点不存在或没有子节点则返回空列表。
     */
    public List<String> getChildren(String nodeId) {
        return childrenMap.getOrDefault(nodeId, Collections.emptyList());
    }

    /**
     * 获取指定节点的直接父节点 ID 列表。
     *
     * @param nodeId 子节点 ID。
     * @return 直接父节点的 ID 列表 (List<String>)。如果节点不存在或没有父节点则返回空列表。
     */
    public List<String> getParents(String nodeId) {
        return parentsMap.getOrDefault(nodeId, Collections.emptyList());
    }


    // --- Core Path Finding Logic ---

    /**
     * 查找从根节点出发，必须经过指定的 `targetNodeId`，并按默认规则延伸至最终叶子节点的完整路径。
     * 这是获取漏斗图展示路径（特别是切换分支后）的核心方法。
     *
     * @param targetNodeId 路径中必须包含的目标节点 ID。此节点必须存在于图中。
     * @return 包含路径上所有 {@link BizScenarioNodeV2} 对象的有序列表。如果无法构建路径（例如目标节点无效或无法回溯到根），则返回空列表。
     */
    public List<BizScenarioNodeV2> findFullPath(String targetNodeId) {
        log.debug("开始查找包含目标节点 '{}' 的完整路径...", targetNodeId);
        // 校验目标节点
        if (targetNodeId == null || !nodesMap.containsKey(targetNodeId)) {
            log.error("无法查找路径：目标节点 ID '{}' 无效或不存在于图中。", targetNodeId);
            return Collections.emptyList();
        }

        // 1. 从目标节点反向追溯到根节点
        LinkedList<BizScenarioNodeV2> pathFromRoot = traceBackToRoot(targetNodeId);
        if (pathFromRoot.isEmpty()) {
            log.error("无法从目标节点 '{}' 反向追溯到根节点。", targetNodeId);
            return Collections.emptyList(); // 如果无法回溯到根，则路径无效
        }
        log.debug("反向追溯完成，路径段节点数: {}", pathFromRoot.size());

        // 2. 从目标节点正向追溯到最终叶子节点（应用默认分支策略）
        List<BizScenarioNodeV2> pathToEnd = traceForwardToLeaf(targetNodeId);
        log.debug("正向追溯完成，路径段节点数: {}", pathToEnd.size());

        // 3. 合并路径
        // pathToEnd 的第一个元素是 targetNodeId，它已在 pathFromRoot 的末尾
        // 将 pathToEnd 中 targetNodeId 之后的部分追加到 pathFromRoot
        if (pathToEnd.size() > 1) { // 只有当正向追踪有多于1个节点时才需要合并
            pathFromRoot.addAll(pathToEnd.subList(1, pathToEnd.size()));
            log.debug("合并路径完成，最终路径节点数: {}", pathFromRoot.size());
        } else {
            log.debug("目标节点 '{}' 已是最终叶子节点或正向追踪无后续，无需合并。", targetNodeId);
        }

        return pathFromRoot; // 返回完整的路径列表
    }

    /**
     * 从指定节点 ID 开始，反向追溯其父节点，直至找到已知的根节点。
     * 用于构建从根到目标节点的路径段。
     * 注意：当前简化实现处理合并点（节点有多个父节点）时，默认只选择找到的第一个父节点。
     *
     * @param startNodeId 开始反向追溯的节点 ID。
     * @return 从根节点到 startNodeId 的节点列表 (LinkedList<BizScenarioNodeV2>)，按路径顺序排列。
     * 如果 startNodeId 无效，或无法追溯到已知根节点，则返回空列表。
     */
    private LinkedList<BizScenarioNodeV2> traceBackToRoot(String startNodeId) {
        LinkedList<BizScenarioNodeV2> path = new LinkedList<>();
        Set<String> visited = new HashSet<>(); // 本次追溯的访问记录，用于检测循环
        String currentNodeId = startNodeId;

        log.trace("开始反向追溯，起点: {}", startNodeId);
        while (currentNodeId != null) {
            // 1. 循环检测
            if (!visited.add(currentNodeId)) {
                log.error("反向追溯时检测到循环！节点: {} 已被访问过。路径追溯失败。", currentNodeId);
                return new LinkedList<>(); // 返回空路径表示失败
            }

            // 2. 获取节点对象
            BizScenarioNodeV2 node = nodesMap.get(currentNodeId);
            if (node == null) {
                log.error("反向追溯中断：找不到节点对象，ID: {}", currentNodeId);
                return new LinkedList<>(); // 节点丢失，路径无效
            }
            path.addFirst(node); // 加到路径链表的最前面
            log.trace("反向追溯：添加节点 {} [{}] 到路径前端。", node.getId(), node.getName());

            // 3. 检查是否到达根节点
            if (rootNodeIds.contains(currentNodeId)) {
                log.trace("反向追溯：到达根节点 {}，追溯结束。", currentNodeId);
                break; // 成功找到根
            }

            // 4. 获取父节点
            List<String> parentIds = parentsMap.getOrDefault(currentNodeId, Collections.emptyList());

            // 5. 检查是否是“孤儿”节点（非根但无父）
            if (parentIds.isEmpty()) {
                log.warn("反向追溯：节点 {} 不是已知根节点，但没有找到父节点。追溯可能不完整。", currentNodeId);
                // 根据业务规则，这里可能应该返回失败（空列表），或者接受这个部分路径
                // 当前实现是中断，然后依赖最后的根节点校验
                break;
            }

            // 6. 选择下一个父节点 (简化：取第一个)
            // TODO: 对于需要精确处理合并点逻辑的场景，需要修改此处的父节点选择策略
            String nextParentId = parentIds.get(0);
            log.trace("反向追溯：节点 {} 的父节点有 {} 个，选择第一个: {}", currentNodeId, parentIds.size(), nextParentId);
            currentNodeId = nextParentId;
        }

        // 7. 最终校验：路径是否有效且以根节点开始
        if (path.isEmpty() || !rootNodeIds.contains(path.getFirst().getId())) {
            log.error("反向追溯最终失败：未能从 {} 成功追溯到任何一个已知的根节点 ({})。", startNodeId, rootNodeIds);
            return new LinkedList<>(); // 确认返回空列表表示失败
        }

        log.debug("反向追溯成功，路径: {}", path.stream().map(BizScenarioNodeV2::getId).collect(Collectors.toList()));
        return path;
    }

    /**
     * 从指定节点 ID 开始，正向追踪子节点，直至到达最终叶子节点。
     * 应用默认的分支选择策略：最深优先，ID 字母序 ASC 平局。
     *
     * @param startNodeId 开始正向追踪的节点 ID。
     * @return 从 startNodeId 到按默认规则选择的最终叶子节点的节点列表 (List<BizScenarioNodeV2>)。
     */
    private List<BizScenarioNodeV2> traceForwardToLeaf(String startNodeId) {
        List<BizScenarioNodeV2> path = new ArrayList<>();
        String currentNodeId = startNodeId;
        Set<String> visitedInTrace = new HashSet<>(); // 本次追踪的访问记录，用于检测循环

        log.trace("开始正向追溯，起点: {}", startNodeId);
        while (currentNodeId != null) {
            // 1. 循环检测
            if (!visitedInTrace.add(currentNodeId)) {
                log.error("正向追溯时检测到循环！节点: {} 已被访问过。停止路径追踪。", currentNodeId);
                break;
            }

            // 2. 获取节点对象
            BizScenarioNodeV2 node = nodesMap.get(currentNodeId);
            if (node == null) {
                log.error("正向追溯中断：找不到节点对象，ID: {}", currentNodeId);
                break;
            }
            path.add(node);
            log.trace("正向追溯：添加节点 {} [{}] 到路径末尾。", node.getId(), node.getName());

            // 3. 获取有效子节点
            List<String> childIds = childrenMap.getOrDefault(currentNodeId, Collections.emptyList());
            List<String> validChildren = childIds.stream()
                    .filter(nodesMap::containsKey) // 过滤掉图中不存在的子节点ID
                    .collect(Collectors.toList());

            // 4. 判断后续路径
            if (validChildren.isEmpty()) {
                // 到达叶子节点
                log.trace("正向追溯：节点 {} 是叶子节点，追踪结束。", currentNodeId);
                break;
            } else if (validChildren.size() == 1) {
                // 只有一个子节点，直接跟随
                String nextNodeId = validChildren.get(0);
                log.trace("正向追溯：节点 {} 只有一个子节点: {}", currentNodeId, nextNodeId);
                currentNodeId = nextNodeId;
            } else {
                // 多个子节点（分支点），应用选择策略
                int maxDepth = -1;
                String chosenChildId = null;
                // 按 ID 排序以处理深度平局的情况
                validChildren.sort(Comparator.naturalOrder());
                log.trace("正向追溯：节点 {} 是分支点，子节点: {}。开始选择默认分支(最深优先,ID ASC)...", currentNodeId, validChildren);

                for (String childId : validChildren) {
                    log.trace("  计算子节点 {} 的最大深度...", childId);
                    int currentChildDepth = calculateMaxDepth(childId); // 调用深度计算
                    log.trace("  子节点 {} 的最大深度为: {}", childId, currentChildDepth);

                    // 选择逻辑：选第一个，或者选更深的
                    if (chosenChildId == null || currentChildDepth > maxDepth) {
                        maxDepth = currentChildDepth;
                        chosenChildId = childId;
                        log.trace("  选择 {} 作为当前最优（深度 {}）", chosenChildId, maxDepth);
                    }
                    // 因为已经按 ID 排序，如果深度相同，第一个遇到的（ID 最小的）已被选中，无需处理
                }

                // 确认选出了下一跳
                if (chosenChildId != null) {
                    log.debug("正向追溯：在节点 {} 的分支中，选择子节点 {} (最大深度 {}) 继续追踪。", currentNodeId, chosenChildId, maxDepth);
                    currentNodeId = chosenChildId;
                } else {
                    // 正常情况下，只要有 validChildren，总会选出一个
                    log.error("正向追溯：在节点 {} 无法确定默认的下一跳子节点！停止追踪。", currentNodeId);
                    currentNodeId = null; // 无法确定，终止循环
                }
            }
        }
        log.debug("正向追溯成功，路径: {}", path.stream().map(BizScenarioNodeV2::getId).collect(Collectors.toList()));
        return path;
    }


    /**
     * 计算从指定节点出发能到达的最大深度（边的数量）。
     * 使用简单的内存缓存 (`depthCache`) 和基本的 DFS 循环检测来优化。
     * 叶子节点的深度定义为 0。
     *
     * @param startNodeId 开始计算深度的节点 ID。
     * @return 从该节点出发的最大深度。如果检测到循环或节点无效，可能返回 0 或缓存的旧值。
     */
    public int calculateMaxDepth(String startNodeId) {
        log.trace("请求计算最大深度，起点: {}", startNodeId);
        // 每次外部调用都启动一次新的深度计算，使用独立的 visited 集合
        return getMaxDepthFromNode(startNodeId, new HashSet<>());
    }

    /**
     * 递归辅助方法，使用深度优先搜索 (DFS) 计算节点的最大深度。
     *
     * @param nodeId       当前正在计算深度的节点 ID。
     * @param visitedInDFS 用于检测**当前递归路径**上是否有循环的集合。
     * @return 节点的最大深度。返回 0 表示叶子节点或检测到循环。返回 -1 表示节点不存在。
     */
    private int getMaxDepthFromNode(String nodeId, Set<String> visitedInDFS) {
        // 1. 检查缓存
        if (depthCache.containsKey(nodeId)) {
            // log.trace("深度缓存命中: Node {} -> Depth {}", nodeId, depthCache.get(nodeId));
            return depthCache.get(nodeId);
        }

        // 2. 循环检测
        if (!visitedInDFS.add(nodeId)) {
            log.warn("计算深度时检测到循环，涉及节点 {}。视其深度为 0。", nodeId);
            return 0; // 检测到循环，深度计为0
        }

        // 3. 节点存在性校验
        if (!nodesMap.containsKey(nodeId)) {
            log.warn("计算深度：节点 {} 不存在于图中。", nodeId);
            visitedInDFS.remove(nodeId); // 从访问集合中移除
            return -1; // 返回 -1 表示节点无效
        }

        // 4. 获取子节点
        List<String> childIds = childrenMap.getOrDefault(nodeId, Collections.emptyList());

        // 5. 处理叶子节点
        if (childIds.isEmpty()) {
            log.trace("节点 {} 是叶子节点，深度为 0。", nodeId);
            depthCache.put(nodeId, 0); // 缓存结果
            visitedInDFS.remove(nodeId); // 回溯前移除
            return 0;
        }

        // 6. 递归计算子节点的最大深度
        int maxChildDepth = -1; // 初始化为 -1
        log.trace("递归计算节点 {} 的子节点深度...", nodeId);
        for (String childId : childIds) {
            int childDepth = getMaxDepthFromNode(childId, visitedInDFS);
            // 如果子路径有效（深度 >= 0），才参与比较
            if (childDepth >= 0) {
                maxChildDepth = Math.max(maxChildDepth, childDepth);
            }
            // 如果子路径无效（例如子节点不存在或循环），则忽略其深度
        }

        // 7. 回溯：从当前 DFS 路径中移除本节点
        visitedInDFS.remove(nodeId);

        // 8. 计算并缓存当前节点的深度
        // 如果所有子节点都无效 (maxChildDepth 保持 -1)，则当前节点也视为深度 0？
        // 或者说，如果一个节点的所有出路都是循环或无效节点，它本身也算是一种“终点”，深度为0。
        int currentDepth = (maxChildDepth == -1) ? 0 : maxChildDepth + 1;
        depthCache.put(nodeId, currentDepth);
        log.trace("节点 {} 的最大深度计算结果: {}", nodeId, currentDepth);
        return currentDepth;
    }


    /**
     * 查找整个业务场景图中的默认路径，当前策略是找到“最深”的那条路径。
     * “最深”定义为从根节点到叶子节点包含最多节点（路径长度最长）的路径。
     * 如果有多条路径具有相同的最大深度，则选择**叶子节点 ID 字符串字母顺序最小 (ASC)** 的那条路径。
     * <p>
     * **实现算法:**
     * 1. 从所有根节点开始，进行深度优先搜索 (DFS) 遍历。
     * 2. 在 DFS 过程中记录到达当前节点的路径。
     * 3. 当到达叶子节点时，记录下完整的根到叶路径及其深度（节点数）。
     * 4. 遍历所有找到的根到叶路径。
     * 5. 找到具有最大深度的路径。
     * 6. 如果有多条路径具有最大深度，则根据叶子节点 ID 的字母顺序选择 ID 最小的那条路径。
     * 7. 返回选定路径的节点列表。
     * </p>
     *
     * @return 包含默认最深路径上所有 {@link BizScenarioNodeV2} 对象的有序列表。如果图为空、没有根节点或没有有效路径，则返回空列表。
     */
    public List<BizScenarioNodeV2> findDefaultFullPath() {
        log.info("开始查找默认最深路径 (使用 DFS)...");
        if (rootNodeIds.isEmpty()) {
            log.error("无法查找默认路径：图中未找到根节点。");
            return Collections.emptyList();
        }

        List<List<BizScenarioNodeV2>> allPaths = new ArrayList<>(); // 存储所有找到的根到叶路径

        // 对每个根节点启动 DFS 查找
        for (String rootId : rootNodeIds) {
            findPathsRecursive(rootId, new LinkedList<>(), new HashSet<>(), allPaths);
        }

        if (allPaths.isEmpty()) {
            log.warn("未能找到任何从根到叶的有效路径。");
            return Collections.emptyList();
        }

        // 寻找最深路径 (节点数最多)
        List<BizScenarioNodeV2> deepestPath = null;
        int maxDepth = -1; // 用节点数代表深度，所以初始为 -1

        log.debug("找到 {} 条根到叶的路径，开始比较深度...", allPaths.size());
        for (List<BizScenarioNodeV2> currentPath : allPaths) {
            int currentDepth = currentPath.size(); // 深度用节点数衡量
            BizScenarioNodeV2 currentLeaf = currentPath.get(currentPath.size() - 1); // 获取当前路径的叶子节点
            log.trace("评估路径 (深度 {}): {}", currentDepth, currentPath.stream().map(BizScenarioNodeV2::getId).collect(Collectors.toList()));


            boolean shouldUpdate = false;
            // 如果是第一条路径，或者当前路径更深
            if (deepestPath == null || currentDepth > maxDepth) {
                shouldUpdate = true;
                log.debug("找到新的最深路径：叶子节点={}, 深度(节点数)={}", currentLeaf.getId(), currentDepth);
            }
            // 如果深度相同，进行平局处理
            else if (currentDepth == maxDepth) {
                String currentDeepestLeafId = deepestPath.get(deepestPath.size() - 1).getId();
                // 选择叶子节点 ID 字母序小的
                if (currentLeaf.getId().compareTo(currentDeepestLeafId) < 0) {
                    shouldUpdate = true;
                    log.debug("发现相同最大深度的路径，但叶子节点 ID '{}' < '{}'，更新选择。", currentLeaf.getId(), currentDeepestLeafId);
                } else {
                    log.trace("发现相同最大深度的路径，但叶子节点 ID '{}' >= '{}'，保持原有选择。", currentLeaf.getId(), currentDeepestLeafId);
                }
            }

            if (shouldUpdate) {
                maxDepth = currentDepth;
                deepestPath = currentPath; // 直接存储这条路径
            }
        }

        if (deepestPath != null) {
            log.info("找到默认最深路径 ({} 节点，深度 {})，叶子节点: {}",
                    deepestPath.size(), maxDepth - 1, deepestPath.get(deepestPath.size() - 1).getId()); // 打印深度用边数
            return deepestPath; // 返回找到的最优路径
        } else {
            log.error("未能选择出有效的最深路径。");
            return Collections.emptyList();
        }
    }

    /**
     * 递归 DFS 辅助方法，用于查找所有从指定节点到叶子节点的路径。
     *
     * @param currentNodeId 当前正在访问的节点 ID。
     * @param currentPath   从根节点到当前节点的路径（节点列表，会被修改，传入前需复制）。
     * @param visitedInPath 用于检测当前 DFS 路径中循环的节点 ID 集合。
     * @param allPaths      用于收集所有找到的完整根到叶路径的列表。
     */
    private void findPathsRecursive(String currentNodeId,
                                    LinkedList<BizScenarioNodeV2> currentPath, // 使用 LinkedList 便于添加/移除
                                    Set<String> visitedInPath,
                                    List<List<BizScenarioNodeV2>> allPaths) {
        // 获取当前节点对象
        BizScenarioNodeV2 currentNode = nodesMap.get(currentNodeId);
        if (currentNode == null) {
            log.warn("DFS: 节点 {} 不存在，中止此路径。", currentNodeId);
            return; // 节点不存在，此路不通
        }

        // 将当前节点加入路径和访问集合
        currentPath.addLast(currentNode);
        visitedInPath.add(currentNodeId);
        log.trace("DFS: 进入节点 {}, 当前路径: {}", currentNodeId, currentPath.stream().map(BizScenarioNodeV2::getId).collect(Collectors.toList()));


        // 获取子节点
        List<String> children = getChildren(currentNodeId);

        // 判断是否是叶子节点
        if (children.isEmpty()) {
            log.trace("DFS: 到达叶子节点 {}，记录路径。", currentNodeId);
            // 到达叶子节点，将当前完整路径复制一份存入结果列表
            allPaths.add(new ArrayList<>(currentPath));
        } else {
            // 遍历子节点，继续 DFS
            for (String childId : children) {
                // 如果子节点在当前路径中已访问过，说明存在循环
                if (visitedInPath.contains(childId)) {
                    log.warn("DFS: 检测到循环！从 {} 到 {}，中止此分支。", currentNodeId, childId);
                    // 不再递归这个子节点，防止无限循环
                } else {
                    // 递归调用，注意传递 visitedInPath 的副本或确保正确回溯
                    // 这里选择不传递副本，依赖于递归返回后的移除操作
                    findPathsRecursive(childId, currentPath, visitedInPath, allPaths);
                }
            }
        }

        // 回溯：从当前 DFS 路径中移除本节点，以便其他分支可以访问它
        visitedInPath.remove(currentNodeId);
        currentPath.removeLast();
        log.trace("DFS: 离开节点 {}", currentNodeId);
    }


}