package com.databuff.webapp.apm.model;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class FindAllEdgeRequest {

    private String account;

    private String apiKey;

    @ApiModelProperty(value = "开始时间", example = "2021-07-15 00:00:00")
    private String fromTime;

    @ApiModelProperty(value = "结束时间", example = "2021-07-15 23:59:59")
    private String toTime;

    @ApiModelProperty(value = "移除所有不必要返回的信息")
    private boolean mini = false;

    private String type;

}
