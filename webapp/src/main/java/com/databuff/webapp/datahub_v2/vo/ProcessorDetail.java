package com.databuff.webapp.datahub_v2.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.Pattern;
import java.util.Map;

/**
 * Processor详情
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(description = "Processor详情")
public class ProcessorDetail {
    
    @ApiModelProperty(value = "Processor ID")
    private Integer processorId;
    
    @ApiModelProperty(value = "Processor名称")
    @Pattern(regexp = "^[a-zA-Z0-9_-]{1,64}$", message = "处理器的名称必须由1到64个字符组成，只能包含字母、数字、下划线或连字符")
    private String name;
    
    @ApiModelProperty(value = "Processor描述")
    private String description;
    
    @ApiModelProperty(value = "Processor类型")
    private String type;
    
    @ApiModelProperty(value = "Processor父类型")
    private String parentType;
    
    @ApiModelProperty(value = "Processor配置")
    private Map<String, Object> config;
    
    @ApiModelProperty(value = "是否收藏")
    private Boolean favorite;

    @ApiModelProperty(value = "引用次数")
    private Integer referenceCount;

    @ApiModelProperty(value = "接收 / 发送 / 处理 数据量, 根据算子父类型填补对应的含义，单位：个数")
    private Long dataMetric;
    
    @ApiModelProperty(value = "创建时间")
    private Long createAt;
    
    @ApiModelProperty(value = "更新时间")
    private Long updateAt;
} 