package com.databuff.webapp.datahub_v2.controller;

import com.databuff.webapp.datahub_v2.bo.runningconfig.DataHubRunnerConfig;
import com.databuff.webapp.datahub_v2.service.ManagerServiceInterface;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;

/**
 * DataHub管理控制器
 * 提供获取Pipeline运行时配置等功能
 */
@Api(value = "DataHub V1 动态获取配置管理接口")
@RequestMapping("/datahub/v1/manager")
@RestController
public class DataHubManagerController {
    
    @Autowired
    private ManagerServiceInterface managerService;
    
    /**
     * 获取运行中的Pipeline算子配置
     * 
     * @param request     HTTP请求对象
     * @param clusterName 集群名称
     * @return 运行时配置
     */
    @ApiOperation(value = "获取运行中的算子配置", notes = "参数：clusterName")
    @GetMapping(value = "/pipeline/run_config/{cluster_name}")
    public DataHubRunnerConfig getRunningPipelineConf(
            HttpServletRequest request, 
            @PathVariable("cluster_name") String clusterName) {
        
        // 注册集群节点
        Boolean registryResult = managerService.registryClusterNode(request, clusterName);
        
        // 集群注册成功，返回配置；否则返回空配置
        if (registryResult) {
            return managerService.getRunningPipelineConf(clusterName);
        }
        
        return new DataHubRunnerConfig();
    }
}
