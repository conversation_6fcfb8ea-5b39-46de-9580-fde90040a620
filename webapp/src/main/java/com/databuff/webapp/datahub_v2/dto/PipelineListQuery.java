package com.databuff.webapp.datahub_v2.dto;

import com.databuff.webapp.datahub_v2.common.DataTypeEnum;
import com.databuff.webapp.datahub_v2.common.StatusEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.Pattern;
import java.util.List;

/**
 * Pipeline列表查询参数
 */
@Data
@ApiModel(description = "Pipeline列表查询参数")
public class PipelineListQuery {
    
    @ApiModelProperty(value = "Pipeline名称，支持模糊查询", example = "数据处理")
    @Pattern(regexp = "^[a-zA-Z0-9_-]{1,64}$", message = "处理器的名称必须由1到64个字符组成，只能包含字母、数字、下划线或连字符")
    private String name;
    
    @ApiModelProperty(value = "Pipeline ID列表，支持精确查询", example = "[1, 2, 3]")
    private List<Integer> ids;

    @ApiModelProperty(value = "Pipeline状态，支持精确查询", example = "RUNNING/STOPPED")
    private StatusEnum status;
    
    @ApiModelProperty(value = "数据类型，支持范围查询", example = "logs/metrics/traces")
    private DataTypeEnum dataType;
    
    @ApiModelProperty(value = "处理器ID，查询包含该处理器的Pipeline", example = "1")
    private Integer processorId;

    @ApiModelProperty(value = "接收器名称，支持模糊查询", example = "数据接收器")
    private String receiverName;

    @ApiModelProperty(value = "接收器类型", example = "kafka")
    private String receiverType;

    @ApiModelProperty(value = "处理器名称，支持模糊查询", example = "数据处理")
    private String processorName;

    @ApiModelProperty(value = "导出器名称，支持模糊查询", example = "数据导出器")
    private String exporterName;

    @ApiModelProperty(value = "导出器类型", example = "kafka")
    private String exporterType;

    @ApiModelProperty(value = "排序字段，支持：createAt/updateAt/totalReceivers/lastUpdateTime", example = "createAt")
    private String orderBy = "createAt";

    @ApiModelProperty(value = "排序方式，支持：asc/desc", example = "desc")
    @Pattern(regexp = "^(asc|desc)?$", message = "排序方式必须为asc或desc")
    private String order = "desc";
    
    @ApiModelProperty(value = "页码，从1开始", example = "1")
    private Integer pageNum = 1;
    
    @ApiModelProperty(value = "每页记录数", example = "10")
    private Integer pageSize = 10;
}
