package com.databuff.webapp.datahub_v2.service;

import com.databuff.webapp.datahub_v2.vo.ProcessorDetail;

import java.util.List;
import java.util.Map;


/**
 * Processor服务接口
 */
public interface ProcessorServiceInterface {
    
    /**
     * 查询Processor列表
     * @param pipelineId Pipeline ID
     * @return Processor列表
     */
    List<ProcessorDetail> queryProcessorList(Integer pipelineId);
    
    /**
     * 获取Processor详情
     * @param processorId Processor ID
     * @return Processor详情
     */
    ProcessorDetail getProcessorDetail(Integer processorId);
    
    /**
     * 更新Processor收藏状态
     * @param processorId Processor ID
     * @param isFavorite 是否收藏
     * @return 更新后的Processor详情
     */
    ProcessorDetail updateFavoriteStatus(Integer processorId, Boolean isFavorite);
    
    /**
     * 查询收藏的Processor列表，并按parentType分组
     * @return 按parentType分组的Processor列表
     */
    Map<String, List<ProcessorDetail>> queryFavoriteProcessorsByParentType(String order, String parentType);
    
    /**
     * 创建Processor
     * @param processorDetail Processor详情
     * @return 创建后的Processor详情
     */
    ProcessorDetail createProcessor(ProcessorDetail processorDetail);
    
    /**
     * 更新Processor配置
     * @param processorId Processor ID
     * @param config 配置信息
     * @return 更新后的Processor详情
     */
    ProcessorDetail updateProcessorConfig(ProcessorDetail  processorDetail);
    
    /**
     * 批量删除Processor
     * @param processorIds Processor ID列表
     * @return 删除结果，包含成功删除的ID列表和失败删除的ID及原因
     */
    Map<String, Object> batchDeleteProcessors(List<Integer> processorIds);
}

