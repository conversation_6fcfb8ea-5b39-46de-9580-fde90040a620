package com.databuff.webapp.datahub_v2.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Map;

/**
 * 算子调试响应对象
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(description = "算子调试响应")
public class ProcessorDebugResponse {
    
    /**
     * 请求是否成功
     */
    @ApiModelProperty(value = "请求是否成功", example = "true")
    private Boolean success;
    
    /**
     * 错误消息（如果有）
     */
    @ApiModelProperty(value = "错误消息", example = "无法连接到集群")
    private String errorMessage;
    
    /**
     * 调试结果数据
     */
    @ApiModelProperty(value = "调试结果数据（JSON格式）")
    private List<Map<String, Object>> data;
} 