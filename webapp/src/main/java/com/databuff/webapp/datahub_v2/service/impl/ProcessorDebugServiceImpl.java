package com.databuff.webapp.datahub_v2.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.databuff.common.utils.HttpUtil;
import com.databuff.dao.mysql.datahub.DataHubClusterMapper;
import com.databuff.entity.datahubv2.DataHubClusterEntity;
import com.databuff.webapp.datahub_v2.dto.ProcessorDebugRequest;
import com.databuff.webapp.datahub_v2.service.ProcessorDebugService;
import com.databuff.webapp.datahub_v2.vo.ProcessorDebugResponse;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestClientException;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 算子调试服务实现类
 */
@Service
public class ProcessorDebugServiceImpl implements ProcessorDebugService {

    private static final Logger log = LoggerFactory.getLogger(ProcessorDebugServiceImpl.class);

    @Autowired
    private DataHubClusterMapper clusterMapper;

    @Override
    public ProcessorDebugResponse debugProcessor(ProcessorDebugRequest request) {
        ProcessorDebugResponse response = new ProcessorDebugResponse();

        try {
            // 1. 查询第一个可用的集群
            QueryWrapper<DataHubClusterEntity> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("is_delete", 0)
                    .eq("enabled", 1)
                    .orderByAsc("id")
                    .last("LIMIT 1");

            DataHubClusterEntity cluster = clusterMapper.selectOne(queryWrapper);
            if (cluster == null) {
                response.setSuccess(Boolean.FALSE);
                response.setErrorMessage("未找到可用的集群");
                return response;
            }

            // 2. 构建请求URL和请求体
            String clusterName = cluster.getClusterName();
            String url = "http://" + clusterName + ":55679/debug/debug_once";

            Map<String, Object> requestBody = new HashMap<>();
            String inputData = request.getInput();
            Map<String, Object> processorsConfig = request.getProcessors();
            if (inputData == null || inputData.isEmpty()) {
                inputData = "{}";
            }
            requestBody.put("input", inputData);
            requestBody.put("processors", processorsConfig);

            // 3. 发送请求并获取响应
            Map<String, String> headers = new HashMap<>();
            headers.put("Content-Type", "application/json");
            String responseBody = HttpUtil.postJson(url, headers, requestBody, 10000);

            // 4. 解析响应数据
            if (responseBody != null) {
                Map<String, Object> responseData;
                try {
                    responseData = JSON.parseObject(responseBody, Map.class);
                } catch (Exception e) {
                    // 如果解析失败，将原始响应作为字符串返回
                    log.error("解析响应数据失败: {}", e.getMessage());
                    responseData = new HashMap<>();
                    responseData.put("rawData", responseBody);
                }
                List<Map<String, Object>> results = new ArrayList<>();
                if (responseData.containsKey("result")) {
                    JSONArray  result = (JSONArray) responseData.get("result");
                    result.forEach(item -> {
                        if (item instanceof JSONObject) {
                           if( ((JSONObject) item).containsKey("logAttributes")){
                               results.add(((JSONObject) item).getJSONObject("logAttributes"));
                           } else{
                               results.add((JSONObject) item);
                           }
                        }
                    });
                }

                response.setSuccess(Boolean.TRUE);
                response.setData(results);
                return response;
            } else {
                response.setSuccess(Boolean.FALSE);
                response.setErrorMessage("调试算子失败 ");
                return response;
            }

        } catch (RestClientException e) {
            log.error("调用调试API出错: {}", e.getMessage());
            response.setSuccess(Boolean.FALSE);
            response.setErrorMessage("调用调试API出错: " + e.getMessage());
            return response;
        } catch (Exception e) {
            log.error("算子调试出错: {}", e.getMessage());
            response.setSuccess(Boolean.FALSE);
            response.setErrorMessage("算子调试出错: " + e.getMessage());
            return response;
        }
    }
} 