package com.databuff.webapp.datahub_v2.vo;

import com.databuff.webapp.datahub_v2.common.DataTypeEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.Pattern;
import java.util.List;

/**
 * 模板VO
 * 用于返回模板相关信息
 */
@Data
@ApiModel(description = "模板VO")
public class TemplateVO {
    
    @ApiModelProperty(value = "模板ID", example = "1")
    private Integer templateId;
    
    @ApiModelProperty(value = "模板名称", example = "数据处理模板")
    @Pattern(regexp = "^[a-zA-Z0-9_-]{1,64}$", message = "处理器的名称必须由1到64个字符组成，只能包含字母、数字、下划线或连字符")
    private String name;
    
    @ApiModelProperty(value = "模板描述", example = "用于数据处理的Pipeline模板")
    private String description;
    
    @ApiModelProperty(value = "数据类型", example = "PROMETHEUS")
    private DataTypeEnum dataType;
    
    @ApiModelProperty(value = "标签", example = "监控,数据处理")
    private List<String> tags;
    
    @ApiModelProperty(value = "状态", example = "ACTIVE")
    private String status;
    
    @ApiModelProperty(value = "创建者ID", example = "admin")
    private String createUserId;
    
    @ApiModelProperty(value = "创建时间戳", example = "")
    private Long createdAt;

} 