package com.databuff.webapp.datahub_v2.service;

import com.databuff.webapp.datahub_v2.bo.runningconfig.DataHubRunnerConfig;

import javax.servlet.http.HttpServletRequest;

/**
 * DataHub管理服务接口
 * 用于管理集群和Pipeline的运行时配置
 */
public interface ManagerServiceInterface {
    
    /**
     * 获取集群中Pipeline的运行时配置
     * 
     * @param clusterName 集群名称
     * @return 运行时配置
     */
    DataHubRunnerConfig getRunningPipelineConf(String clusterName);
    
    /**
     * 注册集群节点
     * 
     * @param request     HTTP请求对象，用于获取客户端信息
     * @param clusterName 集群名称
     * @return 注册是否成功
     */
    Boolean registryClusterNode(HttpServletRequest request, String clusterName);
}
