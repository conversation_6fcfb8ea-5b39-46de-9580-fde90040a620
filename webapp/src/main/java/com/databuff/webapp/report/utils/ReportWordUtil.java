package com.databuff.webapp.report.utils;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.databuff.common.utils.DateUtils;
import com.databuff.common.utils.UnitFormatUtil;
import com.databuff.metric.MetricAggregator;
import com.databuff.metric.moredb.SQLParser;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.util.Units;
import org.apache.poi.xddf.usermodel.chart.*;
import org.apache.poi.xwpf.usermodel.*;
import org.openxmlformats.schemas.drawingml.x2006.chart.CTPlotArea;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

import static com.databuff.common.constants.Constant.Trace.*;
import static com.databuff.common.utils.TimeUtil.ONE_DAY_S;
import static com.databuff.metric.moredb.SQLParser.*;
import static com.databuff.tsdb.metric.moredb.SQLParser.END;
import static com.databuff.tsdb.metric.moredb.SQLParser.START;

@Slf4j
@Component
public class ReportWordUtil {

    @Resource(name = "defSingleMetricAggregator")
    protected MetricAggregator metricAggregator;

    private XWPFDocument document;
    private JSONObject tempParams;

    // 1cm对应twips的数值
    private final Double perCmToTwips = 576.0;

    private final Integer defaultContentWidth = (int) Math.ceil(15.92 * perCmToTwips);


    private final Integer lineChartWidth = 15 * Units.EMU_PER_CENTIMETER;
    private final Integer lineChartHeight = 15 * Units.EMU_PER_CENTIMETER;

    private JSONObject commonArguments = new JSONObject();


    public void initDoc(JSONObject params) throws Exception {
        // 获取模板中的公共参数
        JSONObject template = params.getJSONObject("template");
        JSONObject dateObj = calcExecuteTime(template);
        String apiKey = template.getString("apiKey");
        String fromTime = dateObj.getString("fromTime");
        String toTime = dateObj.getString("toTime");
        Integer type = template.getInteger("type");
        commonArguments.put("fromTime", fromTime);
        commonArguments.put("toTime", toTime);
        commonArguments.put("apiKey", apiKey);
        commonArguments.put("type", type);
        this.tempParams = template;
        //
        document = new XWPFDocument();
    }

    /**
     * 根据配置生成报告内容及数据
     */
    public XWPFDocument generateReport() throws Exception {
        String contentStr = tempParams.getString("content");
        JSONArray content = JSON.parseArray(contentStr);
        // content是报告组件配置集合，需要遍历生成对应的内容
        // { type: "text/pie/line/bar/table", extra: "", text: "", query: "JSONString", title: "", limit: number, interval: 60/180/600/1800 }
        for (Object item : content) {
            JSONObject copyObj = JSONObject.parseObject(item.toString());
//            log.info("content item: {}", item);
            String type = ((JSONObject) item).getString("type");
            Integer limit = ((JSONObject) item).getInteger("limit");

            if (Objects.equals(type, "pie")) {
                Map<String, Object> val = getMetricValue((JSONObject) item);
                JSONObject result = getPieSource(val, limit);
                Boolean fullZero = result.getBoolean("fullZero");
                // 饼状图取最后一个值
                // 数据需要处理，取每个group的最后一个值
                // 判断每个group的最后一个值是否为0，如果全为0，则转换成table显示
                if (fullZero) {
                    JSONObject formatted = getTableSource(val, limit);
                    copyObj.put("formatted", formatted);
                    makeTable(copyObj);
                } else {
                    copyObj.put("formatted", result.getJSONObject("formatted"));
                    makePieChart(copyObj);
                }
            } else if (Objects.equals(type, "line")) {
                Map<String, Object> val = getMetricValue((JSONObject) item);
                JSONObject formatted = getDateAxisLabels(val, limit);
                copyObj.put("formatted", formatted);
                makeLineChart(copyObj);
            } else if (Objects.equals(type, "bar")) {
                Map<String, Object> val = getMetricValue((JSONObject) item);
                JSONObject formatted = getDateAxisLabels(val, limit);
                copyObj.put("formatted", formatted);
                makeBarChart(copyObj);
            } else if (Objects.equals(type, "table")) {
                Map<String, Object> val = getMetricValue((JSONObject) item);
                JSONObject formatted = getTableSource(val, limit);
                copyObj.put("formatted", formatted);
                makeTable(copyObj);
            } else if (Objects.equals(type, "text")) {
                makeParagraph((JSONObject) item);
            }
            makeBreak();
        }
        return document;
    }

    /**
     * 在document对象中创建折线图
     */
    private void makeLineChart(JSONObject lineConfig) throws Exception {
        if (document == null) {
//            initDoc();
        }
        // 获取折线图配置
        String title = lineConfig.getString("title");
        JSONObject formatted = (JSONObject) lineConfig.get("formatted");
        List<String> allTimeStampsList = (List<String>) formatted.get("allTimeStampsList");
        String unit = formatted.getString("unit");
        if (allTimeStampsList.size() == 0) {
            makeEmptyDataText(title);
            return;
        }
        Integer rangeType = commonArguments.getInteger("type");
        if (rangeType == 1) {
            // allTimeStampsList中每一项时间格式"MM-dd HH:mm"需替换成"HH:mm"
            allTimeStampsList = allTimeStampsList.stream().map(s -> s.substring(6)).collect(Collectors.toList());
        }
        String[] xAxisData = allTimeStampsList.toArray(new String[0]);
        Map<String, Map<String, Double>> groupMap = (Map<String, Map<String, Double>>) formatted.get("groupMap");
        // 处理数值
        JSONObject result = similarFormat(groupMap, unit);
        Map<String, Map<String, Double>> formatedGroupMap = (Map<String, Map<String, Double>>) result.get("groupMap");
        String formatedUnit = result.getString("unit");

        XWPFChart chart = document.createChart(lineChartWidth, lineChartHeight);
        // 图例不覆盖标题
        chart.setTitleOverlay(false);
        // 设置图表标题
//        chart.setTitleText(StringUtils.isBlank(title) ? "" : title);
        chart.setTitleText("");
        // 图例设置
        XDDFChartLegend legend = chart.getOrAddLegend();
        legend.setPosition(LegendPosition.BOTTOM);

        // x轴
        XDDFCategoryAxis xAxis = chart.createCategoryAxis(AxisPosition.BOTTOM);
        xAxis.setTitle("时间");
        // 根据指标及聚合条件获取实际数据
        XDDFCategoryDataSource xAxisSource = XDDFDataSourcesFactory.fromArray(xAxisData);

        // y轴
        XDDFValueAxis yAxis = chart.createValueAxis(AxisPosition.LEFT);
        yAxis.setTitle("指标值 / " + formatedUnit);

        // 创建折线图对象
        XDDFLineChartData lineChart = (XDDFLineChartData) chart.createData(ChartTypes.LINE, xAxis, yAxis);


        // 加载折线图数据
        for (String key : formatedGroupMap.keySet()) {
            Map<String, Double> group = formatedGroupMap.get(key);
            // 获取group的values
            Double[] groupData = group.values().toArray(new Double[0]);
            // groupData中的0.0转换成null
//            for (int i = 0; i < groupData.length; i++) {
//                if (groupData[i] == 0.0) {
//                    groupData[i] = null;
//                }
//            }
            XDDFNumericalDataSource<Double> groupSource = XDDFDataSourcesFactory.fromArray(groupData);
            XDDFLineChartData.Series series = (XDDFLineChartData.Series) lineChart.addSeries(xAxisSource, groupSource);
            series.setTitle(key, null);
            series.setSmooth(false);
            series.setMarkerStyle(MarkerStyle.CIRCLE);
            series.setMarkerSize((short) 2);
        }

        // 绘制折线图
        chart.plot(lineChart);
    }

    /**
     * 在document对象中创建柱状图
     */
    private void makeBarChart(JSONObject barConfig) throws Exception {
        if (document == null) {
//            initDoc();
        }
        // 获取折线图配置
        String title = barConfig.getString("title");
        JSONObject formatted = (JSONObject) barConfig.get("formatted");
        List<String> allTimeStampsList = (List<String>) formatted.get("allTimeStampsList");
        String unit = formatted.getString("unit");
        if (allTimeStampsList.size() == 0) {
            makeEmptyDataText(title);
            return;
        }
        Integer rangeType = commonArguments.getInteger("type");
        if (rangeType == 1) {
            // allTimeStampsList中每一项时间格式"MM-dd HH:mm"需替换成"HH:mm"
            allTimeStampsList = allTimeStampsList.stream().map(s -> s.substring(6)).collect(Collectors.toList());
        }
        String[] xAxisData = allTimeStampsList.toArray(new String[0]);
        Map<String, Map<String, Double>> groupMap = (Map<String, Map<String, Double>>) formatted.get("groupMap");
        // 处理数值
        JSONObject result = similarFormat(groupMap, unit);
        Map<String, Map<String, Double>> formatedGroupMap = (Map<String, Map<String, Double>>) result.get("groupMap");
        String formatedUnit = result.getString("unit");

        XWPFChart chart = document.createChart(lineChartWidth, lineChartHeight);
        // 图例不覆盖标题
        chart.setTitleOverlay(false);
        // 设置图表标题
//        chart.setTitleText(StringUtils.isBlank(title) ? "" : title);
        chart.setTitleText("");
        // 图例设置
        XDDFChartLegend legend = chart.getOrAddLegend();
        legend.setPosition(LegendPosition.BOTTOM);

        // x轴
        XDDFCategoryAxis xAxis = chart.createCategoryAxis(AxisPosition.BOTTOM);
        xAxis.setTitle("时间");
        // 设置柱状图的位置：BETWEEN居中
        // 根据指标及聚合条件获取实际数据
        XDDFCategoryDataSource xAxisSource = XDDFDataSourcesFactory.fromArray(xAxisData);

        // y轴
        XDDFValueAxis yAxis = chart.createValueAxis(AxisPosition.LEFT);
        yAxis.setTitle("指标值 / " + formatedUnit);
        yAxis.setCrossBetween(AxisCrossBetween.BETWEEN);
        // 创建折线图对象
        XDDFBarChartData barChart = (XDDFBarChartData) chart.createData(ChartTypes.BAR, xAxis, yAxis);
        barChart.setVaryColors(true);
        barChart.setBarDirection(BarDirection.COL);
        // 加载折线图数据
        // 分组情况下，可能有多个series，需要遍历添加

        for (String key : formatedGroupMap.keySet()) {
            // 根据指标及聚合条件获取实际数据
            Map<String, Double> group = formatedGroupMap.get(key);
            // 获取group的values
            Double[] groupData = group.values().toArray(new Double[0]);
            XDDFNumericalDataSource<Double> groupSource = XDDFDataSourcesFactory.fromArray(groupData);
            XDDFBarChartData.Series series = (XDDFBarChartData.Series) barChart.addSeries(xAxisSource, groupSource);
            series.setTitle(key, null);
        }
        if (formatedGroupMap.keySet().size() == 1) {
            chart.plot(barChart);
        } else {
            // 设置图表类型为堆叠柱状图
            CTPlotArea plotArea = chart.getCTChart().getPlotArea();
            // 绘制折线图
            chart.plot(barChart);
            barChart.setBarGrouping(BarGrouping.STACKED);
            // 修复堆叠错位
            plotArea.getBarChartArray(0).addNewOverlap().setVal((byte) 100);
        }
    }

    /**
     * 在document对象中创建饼状图
     */
    private void makePieChart(JSONObject pieConfig) throws Exception {
        if (document == null) {
//            initDoc();
        }
        // 获取折线图配置
        String title = pieConfig.getString("title");
        JSONObject formatted = (JSONObject) pieConfig.get("formatted");
        // 遍历formatted
        List<String> keys = new ArrayList<>();
        List<Double> values = new ArrayList<>();
        for (Map.Entry<String, Object> entry : formatted.entrySet()) {
            Double value = (Double) entry.getValue();
            String key = entry.getKey();
            keys.add(key);
            values.add(value);
        }
        if (keys.size() == 0) {
            makeEmptyDataText(title);
            return;
        }

        XWPFChart chart = document.createChart(lineChartWidth, lineChartHeight);
        // 图例不覆盖标题
        chart.setTitleOverlay(false);
        // 设置图表标题
//        chart.setTitleText(StringUtils.isBlank(title) ? "" : title);
        chart.setTitleText("");
        // 图例设置
        XDDFChartLegend legend = chart.getOrAddLegend();
        legend.setPosition(LegendPosition.BOTTOM);

        // 不需要实际的x轴 只需要数据集
        // 根据指标及聚合条件获取实际数据
        String[] xAxisData = keys.toArray(new String[0]);
        XDDFCategoryDataSource xAxisSource = XDDFDataSourcesFactory.fromArray(xAxisData);

        // 不需要实际的y轴 只需要数据集
        Double[] yAxisData = values.toArray(new Double[0]);
        XDDFNumericalDataSource<Double> yAxisSource = XDDFDataSourcesFactory.fromArray(yAxisData);

        // 创建折线图对象
        XDDFPieChartData pieChart = (XDDFPieChartData) chart.createData(ChartTypes.PIE, null, null);

        // 加载数据
        XDDFPieChartData.Series series = (XDDFPieChartData.Series) pieChart.addSeries(xAxisSource, yAxisSource);
        series.setTitle("指标", null);

        // 绘制折线图
        chart.plot(pieChart);
    }

    /**
     * 在document对象中创建表格
     */
    private void makeTable(JSONObject tableConfig) throws Exception {
        JSONObject formatted = (JSONObject) tableConfig.get("formatted");
        List<String> headerList = (List<String>) formatted.get("headerList");
        List<List<String>> dataList = (List<List<String>>) formatted.get("dataList");
        if (headerList.size() == 0) {
            String title = tableConfig.getString("title");
            makeEmptyDataText(StringUtils.isBlank(title) ? "" : title);
            return;
        }
        XWPFTable table = document.createTable();
        Integer avgCellWidth = (int) Math.ceil(defaultContentWidth / (headerList.size() + 1));
        // 创建表头
        XWPFTableRow headerRow = getTableRow(table, 0);
        for (int i = 0; i < headerList.size(); i++) {
            XWPFTableCell cell = getTableRowCell(headerRow, i);
            cell.setWidth(String.valueOf(avgCellWidth));
            cell.setText(headerList.get(i));
        }
        // 表格数据
        for (int i = 0; i < dataList.size(); i++) {
            XWPFTableRow row = getTableRow(table, i + 1);
            List<String> data = dataList.get(i);
            for (int j = 0; j < data.size(); j++) {
                XWPFTableCell cell = getTableRowCell(row, j);
                cell.setWidth(String.valueOf(avgCellWidth));
                cell.setText(data.get(j));
            }
        }
    }

    private XWPFTableCell getTableRowCell(XWPFTableRow row, int index) {
        XWPFTableCell cell = row.getCell(index);
        if (cell != null) {
            return cell;
        }
        return row.createCell();
    }

    private XWPFTableRow getTableRow(XWPFTable table, int index) {
        XWPFTableRow row = table.getRow(index);
        if (row != null) {
            return row;
        }
        return table.createRow();
    }

    /**
     * 在document对象中创建文本段落
     */
    private void makeParagraph(JSONObject paragraphConfig) throws Exception {
        String text = (String) paragraphConfig.get("text");
        Integer fontSize = (Integer) paragraphConfig.get("fontSize");
        XWPFParagraph paragraph = document.createParagraph();
        paragraph.setAlignment(ParagraphAlignment.LEFT);
        paragraph.setFirstLineIndent(2);
        XWPFRun run = paragraph.createRun();
        run.setFontFamily("宋体(中文)");
        run.setText(text);
        run.setFontSize(fontSize != null ? fontSize : 12);
        run.setColor("000000");
    }

    private void makeEmptyDataText(String message) throws Exception {
        JSONObject textObj = new JSONObject();
        textObj.put("text", message + " 无数据");
        textObj.put("fontSize", 12);
        makeParagraph(textObj);
    }

    /**
     * 在document对象中创建回车换行
     */
    private void makeBreak() throws Exception {
        XWPFParagraph paragraph = document.createParagraph();
        paragraph.setAlignment(ParagraphAlignment.LEFT);
        paragraph.setFirstLineIndent(2);
        XWPFRun run = paragraph.createRun();
        run.addBreak();
    }


    private Map<String, Object> getMetricValue(JSONObject queryJson) throws Exception {
        String query = queryJson.getString("query");
        Integer interval = queryJson.getInteger(SQLParser.INTERVAL);
        String unit = queryJson.getString("unit");
        String apiKey = commonArguments.getString("apiKey");
        Long fromTimeSec = commonArguments.getLong("fromTime");
        Long toTimeSec = commonArguments.getLong("toTime");
//        Map<String, Object> val = getMonitorGraphMap(query, apiKey, null, toTimeSec, fromTimeSec, interval);
        Map<String, Object> val = getMonitorGraphMapV2(query, apiKey, null, toTimeSec, fromTimeSec, interval, unit);
        return val;
    }

    private Map<String, Object> getMonitorGraphMapV2(String query, String apiKey, JSONArray groupSet, long toTimeSec, long fromTimeSec, int interval, String unit) throws Exception {
        JSONObject queryJson = JSON.parseObject(query);
        List<JSONObject> queries = new ArrayList<>();

        for (Object o : queryJson.values()) {
            if (!(o instanceof JSONObject)) {
                continue;
            }
            JSONObject json = (JSONObject) o;
            if (json.containsKey(METRIC)) {
                json.put(API_KEY, apiKey);
                json.put(START, fromTimeSec);
                json.put(END, toTimeSec);
                final JSONArray bys = json.getJSONArray(BY);
                if (bys != null) {
                    if (bys.contains(SERVICE)) {
                        bys.add(SERVICE_ID);
                    }

                    if (bys.contains(SRC_SERVICE)) {
                        bys.add(SRC_SERVICE_ID);
                    }
                }
                final JSONArray froms = json.getJSONArray(FROM);
                List<JSONObject> formatFroms = new ArrayList<>();
                if (froms != null) {
                    for (Object from : froms) {
                        JSONObject formatFrom = new JSONObject();
                        // from是“field:value”的格式，需要组装成{left: "field", operator: "=", right: "value", connector: "AND"}的格式
                        String[] split = from.toString().split(":");
                        formatFrom.put("left", split[0]);
                        formatFrom.put("operator", "=");
                        formatFrom.put("right", split[1]);
                        if (!formatFroms.isEmpty()) {
                            formatFrom.put("connector", "AND");
                        }
                        formatFroms.add(formatFrom);
                    }
                    json.remove(FROM);
                    json.put(FROM, formatFroms);
                }
                queries.add(json);
            }
        }
        final String expr = queryJson.getString("expr");
        final Map<Map, Map<Object, Double>> beforeAggTimeSeries = metricAggregator.aggResult(expr, queries, Long.parseLong(interval + ""));

        Map<String, Object> resMap = new HashMap<>(2);
        Map<String, Map<Object, Double>> groupMap = new HashMap<>(16);
        for (Map.Entry<Map, Map<Object, Double>> entry : beforeAggTimeSeries.entrySet()) {
            Map<Object, Double> value = entry.getValue();
            Map<Object, Double> treeMap = new TreeMap<>();
            for (Map.Entry<Object, Double> e : value.entrySet()) {
                treeMap.put(e.getKey(), e.getValue() == null ? 0.0 : e.getValue());
            }
            groupMap.put(entry.getKey().toString(), treeMap);
        }
        resMap.put("groupMap", groupMap);
        resMap.put("unit", unit != null ? unit : "");
        return resMap;
    }

    private JSONObject calcExecuteTime(JSONObject template) {
        Integer type = template.getInteger("type");
        JSONObject dateObj = new JSONObject();
        if (type == 1 || type == 2) {
            // 周期性的报告
            JSONObject cycleTime = JSON.parseObject(template.getString("cycleTime"));
            // range: 周期范围，1表示当天，-1表示前一天，7表示本周，-7表示前一周
            Integer range = cycleTime.getInteger("range");
            // 根据range的值计算与当前时刻的时间范围：fromTime, toTime 为秒级时间戳
            Long fromTime = null;
            Long toTime = null;
            String today = DateUtils.getDate();
            if (range == 1) {
                // 当天
                fromTime = DateUtils.parseDate(today + " 00:00:00").getTime() / 1000;
                toTime = new Date().getTime() / 1000;
            } else if (range == 7) {
                // 本周
                Calendar calendar = Calendar.getInstance();

                // 设置为本周周一日期
                calendar.set(Calendar.DAY_OF_WEEK, Calendar.MONDAY);
                calendar.set(Calendar.HOUR_OF_DAY, 0);
                calendar.set(Calendar.MINUTE, 0);
                calendar.set(Calendar.SECOND, 0);
                calendar.set(Calendar.MILLISECOND, 0);

                // 获取本周的周一秒级时间戳
                fromTime = calendar.getTime().getTime() / 1000;
                toTime = new Date().getTime() / 1000;
            } else if (range == -1) {
                // 前一天
                fromTime = DateUtils.parseDate(today + " 00:00:00").getTime() / 1000 - ONE_DAY_S;
                toTime = DateUtils.parseDate(today + " 00:00:00").getTime() / 1000 - 1;
            } else if (range == -7) {
                // 上一周
                Calendar calendar = Calendar.getInstance();

                // 设置为本周一日期
                calendar.set(Calendar.DAY_OF_WEEK, Calendar.MONDAY);
                calendar.set(Calendar.HOUR_OF_DAY, 0);
                calendar.set(Calendar.MINUTE, 0);
                calendar.set(Calendar.SECOND, 0);
                calendar.set(Calendar.MILLISECOND, 0);

                // 设置为上周周一日期
                fromTime = calendar.getTime().getTime() / 1000 - 7 * 24 * 60 * 60L;
                toTime = calendar.getTime().getTime() / 1000 - 1;
            }
            dateObj.put("fromTime", fromTime);
            dateObj.put("toTime", toTime);
        } else if (type == 3) {
            // 自定义时间的报告
            // execute: 'YYYY-MM-DD HH:mm:ss', fromTime: 'YYYY-MM-DD HH:mm:ss', toTime: 'YYYY-MM-DD HH:mm:ss'
            JSONObject customTime = JSON.parseObject(template.getString("customTime"));
            // fromTime, toTime 为秒级时间戳
            Long fromTime = DateUtils.parseDate(customTime.getString("fromTime")).getTime() / 1000;
            Long toTime = DateUtils.parseDate(customTime.getString("toTime")).getTime() / 1000;
            dateObj.put("fromTime", fromTime);
            dateObj.put("toTime", toTime);
        }
        if (dateObj.get("fromTime") == null || dateObj.get("toTime") == null) {
            // 设置为最近一小时
            Long toTime = new Date().getTime() / 1000;
            Long fromTime = toTime - 60 * 60;
            dateObj.put("fromTime", fromTime);
            dateObj.put("toTime", toTime);
        }
        return dateObj;
    }


    private JSONObject getDateAxisLabels(Map<String, Object> values, Integer limit) {
        Set<Long> allTimeStamps = new HashSet<>();
        JSONObject result = new JSONObject();
        SimpleDateFormat sdf = new SimpleDateFormat("MM-dd HH:mm");
        Integer maxLimit = limit == null ? 5 : limit;

        Map<String, Map<Object, Double>> groupMap = (Map<String, Map<Object, Double>>) values.get("groupMap");
        String unit = (String) values.get("unit");
        // 聚合全部分组的时间戳，group中会有不同的时间点
        for (String key : groupMap.keySet()) {
            if (allTimeStamps.size() == 0) {
                Map<Object, Double> group = groupMap.get(key);
                allTimeStamps.addAll(group.keySet().stream().map(e -> Long.parseLong(e.toString())).collect(Collectors.toSet()));
            }
        }
        if (allTimeStamps.size() > 0) {
            JSONObject resultGroupMap = new JSONObject();
            for (String key : groupMap.keySet()) {
                Map<Object, Double> subObject = groupMap.get(key);
                JSONObject subObjectWithDates = new JSONObject();
                for (Object timestamp : subObject.keySet()) {
                    subObjectWithDates.put(sdf.format(new Date(Long.parseLong(timestamp.toString()) * 1000)), subObject.get(timestamp));
                }
                // 处理下无分组是key=""的情况，改为"全部"或者"*"
                if (StringUtils.isBlank(key) && groupMap.size() == 1) {
                    resultGroupMap.put("全部", subObjectWithDates);
                } else {
                    resultGroupMap.put(key, subObjectWithDates);
                }
            }
            List<Long> allTimeStampsList = new ArrayList<>(allTimeStamps);
            // allTimeStampsList升序排序
            Collections.sort(allTimeStampsList);

            // 把allTimeStampsList转换成"yyyy-MM-dd HH:mm:ss"集合
            List<String> allTimeStampsListStr = allTimeStampsList.stream().map(e -> sdf.format(new Date(e * 1000))).collect(Collectors.toList());
            // 根据allTimeStampsListStr最后一个值的大小，取groupMap的Top5
            String lastTimeStampStr = allTimeStampsListStr.get(allTimeStampsListStr.size() - 1);
            Map<String, Double> toSortMap = new HashMap<>();
            for (String key : resultGroupMap.keySet()) {
                JSONObject subObject = resultGroupMap.getJSONObject(key);
                toSortMap.put(key, subObject.getDouble(lastTimeStampStr) == null ? 0.0 : subObject.getDouble(lastTimeStampStr));
            }

            JSONObject sortedGroupMap = new JSONObject();
            if (maxLimit < 0) {
                // 使用 Stream API 获取值最小的前 5 个键值对
                Map<String, Double> bottom5Map = toSortMap.entrySet().stream()
                        .sorted(Map.Entry.<String, Double>comparingByValue())
                        .limit(Math.abs(maxLimit))
                        .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue, (oldValue, newValue) -> oldValue, LinkedHashMap::new));
                bottom5Map.forEach((key, value) -> {
                    sortedGroupMap.put(key, resultGroupMap.getJSONObject(key));
                });
            } else {
                // 使用 Stream API 获取值最大的前 5 个键值对
                Map<String, Double> top5Map = toSortMap.entrySet().stream()
                        .sorted(Map.Entry.<String, Double>comparingByValue(Comparator.reverseOrder()))
                        .limit(Math.abs(maxLimit))
                        .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue, (oldValue, newValue) -> oldValue, LinkedHashMap::new));
                top5Map.forEach((key, value) -> {
                    sortedGroupMap.put(key, resultGroupMap.getJSONObject(key));
                });
            }

            result.put("allTimeStamp", allTimeStamps);
            result.put("allTimeStampsList", allTimeStampsListStr);
            result.put("groupMap", sortedGroupMap);
            result.put("unit", unit);
        } else {
            List<Long> allTimeStampsList = new ArrayList<>(allTimeStamps);
            // allTimeStampsList升序排序
            Collections.sort(allTimeStampsList);
            // 把allTimeStampsList转换成"yyyy-MM-dd HH:mm:ss"集合
            List<String> allTimeStampsListStr = allTimeStampsList.stream().map(e -> sdf.format(new Date(e * 1000))).collect(Collectors.toList());
            result.put("allTimeStamp", allTimeStamps);
            result.put("allTimeStampsList", allTimeStampsListStr);
            result.put("groupMap", new JSONObject());
            result.put("unit", unit);
        }
        return result;
    }


    private JSONObject getPieSource(Map<String, Object> values, Integer limit) {
        JSONObject formatted = getDateAxisLabels(values, limit);
        Map<String, Map<String, Double>> groupMap = (Map<String, Map<String, Double>>) formatted.get("groupMap");
        List<String> allTimeStampsList = (List<String>) formatted.get("allTimeStampsList");
        JSONObject result = new JSONObject();
        if (allTimeStampsList.size() > 0) {
            // 取allTimeStampsList最后一个值
            String lastTimeStampStr = allTimeStampsList.get(allTimeStampsList.size() - 1);
            int zeroCount = 0;
            JSONObject resultGroupMap = new JSONObject();
            for (String key : groupMap.keySet()) {
                Map<String, Double> group = groupMap.get(key);
                // 根据最后一个时间取group对应的值
                Double lastTimeStampValue = group.get(lastTimeStampStr);
                resultGroupMap.put(key, lastTimeStampValue);
                // 判断是否为0
                if (lastTimeStampValue == null || lastTimeStampValue.intValue() == 0) {
                    zeroCount++;
                }
            }

            if (zeroCount == groupMap.size() && zeroCount > 0) {
                result.put("fullZero", true);
            } else {
                result.put("fullZero", false);
            }
            result.put("formatted", resultGroupMap);
        } else {
            result.put("fullZero", true);
            result.put("formatted", new JSONObject());
        }

        return result;
    }

    private JSONObject getTableSource(Map<String, Object> values, Integer limit) {
        JSONObject formatted = getDateAxisLabels(values, limit);
        Map<String, Map<String, Double>> groupMap = (Map<String, Map<String, Double>>) formatted.get("groupMap");
        String unit = (String) values.get("unit");
        JSONObject result = new JSONObject();

        List<String> headerList = new ArrayList<>();
        List<List<String>> dataList = new ArrayList<>();
        List<String> allTimeStampsList = (List<String>) formatted.get("allTimeStampsList");

        if (allTimeStampsList.size() > 0) {
            // 取allTimeStampsList最后10个值
            List<String> last10TimeStampsList = allTimeStampsList.subList(allTimeStampsList.size() - 10, allTimeStampsList.size());

            headerList.add("");
            headerList.addAll(last10TimeStampsList);
            // headerList为groupMap的key集
            // dataList默认取每个group的后10个值
            for (String key : groupMap.keySet()) {
                Map<String, Double> group = groupMap.get(key);
                List<String> subList = new ArrayList<>();
                subList.add(key);
                for (String timeStamp : last10TimeStampsList) {
                    Double value = group.get(timeStamp);
                    // 根据unit对数据进行格式化
                    if (StringUtils.isBlank(value.toString())) {
                        subList.add("-");
                    } else {
                        subList.add(UnitFormatUtil.humanReadableFormat(value, unit));
//                        // 创建 DecimalFormat 对象，指定保留两位小数的格式
//                        DecimalFormat df = new DecimalFormat("#.##");
//                        // 格式化 Double 数值，保留两位小数
//                        String formattedNumber = df.format(value);
//                        subList.add(formattedNumber);
                    }
                }
                dataList.add(subList);
            }

        }
        result.put("headerList", headerList);
        result.put("dataList", dataList);

        return result;

    }

    private JSONObject getTableSourceReverse(Map<String, Object> values, Integer limit) {
        JSONObject formatted = getDateAxisLabels(values, limit);
        Map<String, Map<String, Double>> groupMap = (Map<String, Map<String, Double>>) formatted.get("groupMap");
        JSONObject result = new JSONObject();

        List<String> headerList = new ArrayList<>();
        List<List<String>> dataList = new ArrayList<>();
        List<String> allTimeStampsList = (List<String>) formatted.get("allTimeStampsList");
        // 取最后20个值
        List<String> last20TimeStampsList = allTimeStampsList.subList(allTimeStampsList.size() - 20, allTimeStampsList.size());


        if (allTimeStampsList.size() > 0) {
            List<String> keys = new ArrayList<>(groupMap.keySet());

            headerList.add("时间");
            headerList.addAll(keys);
            // headerList为groupMap的key集
            for (String timeStr : last20TimeStampsList) {
                List<String> subList = new ArrayList<>();
                subList.add(timeStr);
                for (String key : keys) {
                    subList.add(groupMap.get(key).get(timeStr).toString());
                }
                dataList.add(subList);
            }
        }
        result.put("headerList", headerList);
        result.put("dataList", dataList);

        return result;

    }

    private JSONObject similarFormat(Map<String, Map<String, Double>> groupMap, String unit) {
        // 获取groupMap中的最小值和最大值
        Double min = 0.0;
        Double max = 0.0;
        for (String key : groupMap.keySet()) {
            Map<String, Double> group = groupMap.get(key);
            List<Double> values = new ArrayList<>(group.values());
            Double groupMin = Collections.min(values);
            Double groupMax = Collections.max(values);
            if (groupMin < min) {
                min = groupMin;
            }
            if (groupMax > max) {
                max = groupMax;
            }
        }

        Double avg = (min + max) / 2;

        JSONObject result = ReportMetricReverseUtil.humanReadableFormat(avg, unit);
        String unitStr = result.getString("unit");
        Long scale = result.getLong("scale");
        if (scale < 0) {
            Double newScale = getDoubleScale(scale);
            Double extraScale = ReportMetricReverseUtil.isTimeFormat(unit) ? result.getDouble("extraScale") : 1.0;
            for (String key : groupMap.keySet()) {
                Map<String, Double> group = groupMap.get(key);
                for (String timeStamp : group.keySet()) {
                    Double value = group.get(timeStamp);
                    if (value != null) {
                        group.put(timeStamp, value * extraScale * newScale);
                    }
                }
            }
        } else {
            for (String key : groupMap.keySet()) {
                Map<String, Double> group = groupMap.get(key);
                Double extraScale = ReportMetricReverseUtil.isTimeFormat(unit) ? result.getDouble("extraScale") : 1.0;
                for (String timeStamp : group.keySet()) {
                    Double value = group.get(timeStamp);
                    if (value != null) {
                        group.put(timeStamp, value * extraScale / scale);
                    }
                }
            }
        }
        JSONObject result2 = new JSONObject();
        result2.put("unit", unitStr);
        result2.put("groupMap", groupMap);
        return result2;
    }

    private Double getDoubleScale(Long scale) {
        if (scale == -1000L) {
            return 0.001;
        } else if (scale == -1000000L) {
            return 0.000001;
        } else if (scale == -1000000000L) {
            return 0.000000001;
        } else {
            return 1.0;
        }
    }

}