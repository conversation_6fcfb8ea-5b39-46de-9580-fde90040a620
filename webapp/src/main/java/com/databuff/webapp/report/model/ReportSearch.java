package com.databuff.webapp.report.model;

import java.util.Date;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class ReportSearch {

    /**
     * 报告名称
     */
    @ApiModelProperty(value = "报告名称")
    private String name;

    /**
     * 报告类型
     */
    @ApiModelProperty(value = "报告类型")
    private Integer type;

    /**
     * 报告id
     */
    @ApiModelProperty(value = "报告id")
    private Integer id;

    /**
     * 开始时间
     */
    @ApiModelProperty(value = "开始时间")
    private String fromTime;

    /**
     * 结束时间
     */
    @ApiModelProperty(value = "结束时间")
    private String toTime;

    /**
     * 每页数量
     */
    @ApiModelProperty(value = "每页数量")
    protected Integer pageSize = 10;

    /**
     * 页数
     */
    @ApiModelProperty(value = "页数")
    protected Integer pageNum = 1;

    /**
     * apiKey
     */
    @ApiModelProperty(value = "apiKey")
    private String apiKey;

}
