package com.databuff.webapp.monitor.service;

import com.databuff.entity.BusinessSystemRule;
import com.databuff.webapp.monitor.model.AddSysWithRulesRequest;
import com.databuff.webapp.monitor.model.DeleteBusinessSystemAndRulesResponse;
import com.databuff.webapp.monitor.model.UpdateBusinessSystemRulesRequest;

import java.util.List;

public interface BusinessSystemService {
    /**
     * 新建业务系统与监控服务规则
     * @param request
     * @return int
     */
    int addSysWithRules(AddSysWithRulesRequest request);

    /**
     * 查询该业务系统下所有监控服务规则
     * @param sysId
     * @param apiKey
     * @return {@link List }<{@link BusinessSystemRule }>
     */
    List<BusinessSystemRule> getBusinessSystemRules(Integer sysId, String apiKey);

    /**
     * 更新该业务系统的监控服务规则
     * @param request
     * @return {@link List }<{@link BusinessSystemRule }>
     */
    List<BusinessSystemRule> updateBusinessSystemRules(UpdateBusinessSystemRulesRequest request);

    /**
     * 删除该业务系统与其属于的监控服务规则
     * @param sysId
     * @param apiKey
     * @return {@link DeleteBusinessSystemAndRulesResponse }
     */
    DeleteBusinessSystemAndRulesResponse deleteBusinessSystemAndRules(Integer sysId, String apiKey);
}
