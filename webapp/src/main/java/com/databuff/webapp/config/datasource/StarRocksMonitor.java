package com.databuff.webapp.config.datasource;

import com.alibaba.druid.pool.DruidDataSource;
import com.alibaba.druid.spring.boot.autoconfigure.DruidDataSourceBuilder;
import com.databuff.common.utils.OtelMetricUtil;
import com.databuff.common.utils.StringUtil;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;

import javax.sql.DataSource;
import java.sql.Connection;
import java.sql.ResultSet;
import java.util.*;

/**
 * Monitor for StarRocks database metrics.
 */
@Slf4j
public class StarRocksMonitor {

    private final Timer timer = new Timer();
    private final Map<String, DataSource> starRocksDataSources = new HashMap<>();
    private final DruidDataSource druidDataSource;

    private final List<String> beMetrics = new ArrayList<>();
    private final List<String> feMetrics = new ArrayList<>();

    private static final String JDBC_MYSQL_LOADBALANCE_PREFIX = "**********************://";
    private static final String JDBC_MYSQL_PREFIX = "jdbc:mysql://";

    public StarRocksMonitor(String url, String username, String password, String driverClassName) {
        initializeMetrics();

        druidDataSource = createDruidDataSource(username, password, driverClassName, url);
    }
    private void initializeMetrics() {
        initBeMetrics();
        initFeMetrics();
        // 检查 beMetrics 和 feMetrics 中是否有重复的值 ,否则metricName就重复了 ,如果有的话要记得改
        checkDuplicateMetrics();
    }


    private void initFeMetrics() {
        // BDBJE Writer：BDBJE写入情况，正常都是毫秒级别，如果出现秒级的写入速度就需要警惕，严重的元数据写入延迟可能会引起写入错误。通常高延迟的情况可能是磁盘性能较弱，
        // 此时建议为FE元数据目录更换性能更优的磁盘。在StarRocks中，使用BDBJE完成元数据操作日志的持久化、FE高可用等功能。左侧Y轴显示99th写入延迟，右侧的Y轴显示日志每秒写入次数。
        feMetrics.add("edit_log_write");
        //Scheduling Tablets：正在进行调度任务的Tablet数量。这些Tablet可能处于Recovery或Balance的过程中。
        feMetrics.add("scheduled_tablet_num");
        //为了计算RPS：各个FE的每秒请求数。这里的请求包括发送到FE的所有请求。
        feMetrics.add("request_total");
        //QPS：各个FE的每秒查询数。查询仅包括Select请求。
        feMetrics.add("query_total");
        // Connections：各个FE当前的连接数。
        feMetrics.add("connection_total");
        //Txn Begin/Success on FE：显示FE中事务开始和成功的数量和速度。
        feMetrics.add("txn_begin");
        feMetrics.add("txn_success");
        //Txn Failed/Reject on FE：显示FE中失败的事务请求，包括被拒绝的请求和失败的事务请求。
        feMetrics.add("txn_reject");
        feMetrics.add("txn_failed");
        //各个BE节点的Compaction Score值。通常该值需要维持在100以内，而在大部分批量导入或低频导入场景下，该值通常为10-20或者更低。如果该值过高，不仅会影响导入，还会影响集群的查询性能，此时我们就需要及时的降低导入频率。
        feMetrics.add("tablet_max_compaction_score");
        //FE BDB元数据log条数，默认超过5万条会触发Checkpoint进行落盘。如果该值过大，就要观察是否JVM内存过小或其他原因导致Checkpoint失败。
        feMetrics.add("meta_log_count");
    }

    private void initBeMetrics() {
        beMetrics.add("disks_avail_capacity");
        //Total Capacity：所有BE存储目录所在磁盘的合计容量。注意：该指标仅表示磁盘容量大小，不表示可用空间。
        beMetrics.add("disks_total_capacity");
        //Used Capacity：当前所有BE合计使用的磁盘空间。
        beMetrics.add("disks_data_used_capacity");
        beMetrics.add("cpu");
        beMetrics.add("jemalloc_allocated_bytes");
        //Net Send/Receive Bytes：除IO外的所有设备的网络发送（左Y轴）/接收（右Y轴）的字节速率。
        beMetrics.add("network_receive_bytes");
        beMetrics.add("network_send_bytes");

        // 通过网络发送的总字节数（所有网络接口中的最大值）。 单位：Byte  15秒平均
        beMetrics.add("max_network_send_bytes_rate");
        //通过网络接收的总字节数（所有网络接口中的最大值）。
        beMetrics.add("max_network_receive_bytes_rate");


        beMetrics.add("fragment_requests_total");
        beMetrics.add("fragment_request_duration_us");
        //为了取Publish Task on BE：BE中Publish Task请求总数和错误率。
        beMetrics.add("engine_requests_total");
        //数据导入速率，仅统计以Stream Load方式写入的数据，包括Routine Load、Flink Connector等
        beMetrics.add("stream_load");
        //BE最大的Base Compaction Score，表示当前BE节点的Base Compaction压力。
        beMetrics.add("base_max_compaction_score");
        //be 进程使用的内存
        beMetrics.add("process_mem_bytes");
        //Compaction 使用的内存
        beMetrics.add("compaction_mem_bytes");
        //数据导入的内存成本
        beMetrics.add("load_mem_bytes");
        //查询使用的内存
        beMetrics.add("query_mem_bytes");
        //元数据使用的内存
        beMetrics.add("metadata_mem_bytes");
    }

    private void checkDuplicateMetrics() {
        Set<String> uniqueMetrics = new HashSet<>();

        // 检查 beMetrics 中的重复值
        for (String metric : beMetrics) {
            if (!uniqueMetrics.add(metric)) {
                throw new RuntimeException("Duplicate metric found in beMetrics: " + metric);
            }
        }

        // 检查 feMetrics 中的重复值
        for (String metric : feMetrics) {
            if (!uniqueMetrics.add(metric)) {
                throw new RuntimeException("Duplicate metric found in feMetrics: " + metric);
            }
        }
    }

    private String extractInstanceFromUrl(String url) {
        if (url.startsWith(JDBC_MYSQL_LOADBALANCE_PREFIX)) {
            return url.split(JDBC_MYSQL_LOADBALANCE_PREFIX)[1].split("/")[0];
        } else if (url.startsWith(JDBC_MYSQL_PREFIX)) {
            return url.split(JDBC_MYSQL_PREFIX)[1].split("/")[0];
        } else {
            throw new RuntimeException("StarRocks url is not correct");
        }
    }

    private String buildNewUrl(String url, String instance, String ins) {
        if (url.startsWith(JDBC_MYSQL_LOADBALANCE_PREFIX)) {
            return new StringBuilder(url).replace(url.indexOf(instance), url.indexOf(instance) + instance.length(), ins).toString();
        } else if (url.startsWith(JDBC_MYSQL_PREFIX)) {
            return url;
        } else {
            throw new RuntimeException("StarRocks url is not correct");
        }
    }

    private DruidDataSource createDruidDataSource(String username, String password, String driverClassName, String url) {
        DruidDataSource druidDataSource = DruidDataSourceBuilder.create().build();
        druidDataSource.setUsername(username);
        druidDataSource.setPassword(password);
        druidDataSource.setQueryTimeout(120);
        druidDataSource.setUrl(url);
        druidDataSource.setValidationQuery("show status;");
        druidDataSource.setDriverClassName(driverClassName);
        return druidDataSource;
    }

    /**
     * Start monitoring the StarRocks database.
     *
     * @param interval the interval for monitoring
     */
    public void startMonitoring(long interval) {
        timer.scheduleAtFixedRate(new MonitorTask(), 30000, interval);
    }

    private class MonitorTask extends TimerTask {
        @Override
        public void run() {

            try (Connection connection = druidDataSource.getConnection()) {
                // 查询 BE 指标
                queryMetrics(connection, "information_schema.be_metrics", beMetrics);
                // 查询 FE 指标
                queryMetrics(connection, "information_schema.fe_metrics", feMetrics);

            } catch (Exception e) {
                log.error("Error in StarRocks monitoring", e);
            }

        }

        private void queryMetrics(Connection connection, String tableName, List<String> metrics) throws Exception {
            ResultSet resultSet = connection.createStatement().executeQuery("select * from " + tableName);
            long current = System.currentTimeMillis();
            ObjectMapper objectMapper = new ObjectMapper();

            while (resultSet.next()) {
                String name = resultSet.getString(2);
                if (!metrics.contains(name)) {
                    continue;
                }
                String tags = resultSet.getString(3);
                Map<String, String> tagMap = new HashMap<>();
                tagMap.put("instance",  resultSet.getString(1));

                if (StringUtil.isNotBlank(tags)) {
                    if (tags.startsWith("{") && tags.endsWith("}")) {
                        // 解析fe 的 JSON 格式 tags
                        Map<String, String> jsonTags = objectMapper.readValue(tags, new TypeReference<Map<String, String>>() {});
                        tagMap.putAll(jsonTags);
                    } else {
                        // 解析be的 tags
                        String[] tagArray = tags.split(",");
                        for (String tag : tagArray) {
                            String[] tagKeyValue = tag.split("=");
                            if (tagKeyValue.length == 2) {
                                tagMap.put(tagKeyValue[0], tagKeyValue[1]);
                            }
                        }
                    }
                }

                Long value = resultSet.getLong(4);
                OtelMetricUtil.logOriginalData("starrocks." + name, value, tagMap, current);
            }
        }
    }

}
