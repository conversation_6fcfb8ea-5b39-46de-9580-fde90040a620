//package com.databuff.webapp.config.swagger;
//
//import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
//import org.springframework.context.annotation.Bean;
//import org.springframework.context.annotation.Configuration;
//import org.springframework.web.servlet.config.annotation.ResourceHandlerRegistry;
//import org.springframework.web.servlet.config.annotation.WebMvcConfigurerAdapter;
//import springfox.documentation.annotations.ApiIgnore;
//import springfox.documentation.builders.ParameterBuilder;
//import springfox.documentation.schema.ModelRef;
//import springfox.documentation.service.Parameter;
//import springfox.documentation.spi.DocumentationType;
//import springfox.documentation.spring.web.plugins.Docket;
//import springfox.documentation.swagger2.annotations.EnableSwagger2;
//
//import java.util.ArrayList;
//import java.util.List;
//
//import static springfox.documentation.builders.PathSelectors.regex;
//
//@Configuration
//@EnableSwagger2
//@ConditionalOnProperty(prefix = "swagger", value = {"enable"}, havingValue = "true")
//public class Swagger2 extends WebMvcConfigurerAdapter {
//
//    @Bean
//    public Docket user() {
//        return docket(
//                "user",
//                "用户管理",
//                "用户管理",
//                "^/user.*"
//        );
//    }
//    @Bean
//    public Docket audit() {
//        return docket(
//                "audit",
//                "日志审计",
//                "日志审计",
//                "^/audit.*"
//        );
//    }
//    @Bean
//    public Docket system() {
//        return docket(
//                "system",
//                "系统管理",
//                "系统管理",
//                "^/system.*"
//        );
//    }
//    @Bean
//    public Docket send() {
//        return docket(
//                "api",
//                "被调用采集数据",
//                "被调用采集数据",
//                "^/api.*"
//        );
//    }
//    @Bean
//    public Docket agent() {
//        return docket(
//                "agent",
//                "agent管理",
//                "agent管理",
//                "^/agent.*"
//        );
//    }
//
//    @Bean
//    public Docket infrastructure() {
//        return docket(
//                "infrastructure",
//                "基础设施",
//                "基础设施",
//                "^/base.*"
//        );
//    }
//
//    @Bean
//    public Docket metrics() {
//        return docket(
//                "metrics",
//                "指标",
//                "指标",
//                "^/metrics.*"
//        );
//    }
//
//    @Bean
//    public Docket plugin() {
//        return docket(
//                "plugin",
//                "插件集成",
//                "插件集成",
//                "^/plugin.*"
//        );
//    }
//
//
//    @Bean
//    public Docket transaction() {
//        return docket(
//                "transaction",
//                "应用检测事务",
//                "应用检测事务",
//                "^/transaction.*"
//        );
//    }
//    @Bean
//    public Docket topo() {
//        return docket(
//                "topo",
//                "应用拓扑",
//                "应用拓扑",
//                "^/topo.*"
//        );
//    }
//
//    @Bean
//    public Docket monitor() {
//        return docket(
//                "monitor",
//                "监控",
//                "监控",
//                "^/monitor.*"
//        );
//    }
//
//    @Bean
//    public Docket service() {
//        return docket(
//                "service",
//                "Trace服务",
//                "Trace服务",
//                "^/service.*"
//        );
//    }
//    @Bean
//    public Docket Trace() {
//        return docket(
//                "trace",
//                "Trace调用链",
//                "Trace调用链",
//                "^/trace.*"
//        );
//    }
//    @Bean
//    public Docket saasCustomer() {
//        return docket(
//                "saasCustomer",
//                "saas客户端",
//                "saas客户端",
//                "^/saasCustomer.*"
//        );
//    }
//
//
//    @Bean
//    public Docket configManage() {
//        return docket(
//                "configManage",
//                "配置中心",
//                "配置中心",
//                "^/configManage.*"
//        );
//    }
//
//
//    @Bean
//    public Docket Notify() {
//        return docket(
//                "notify",
//                "通知告警",
//                "通知告警",
//                "^/notify.*"
//        );
//    }
//    @Bean
//    public Docket rum() {
//        return docket(
//                "rum",
//                "RUM真是用户体验",
//                "RUM真是用户体验",
//                "^/rum.*"
//        );
//    }
//    @Bean
//    public Docket networkDevice() {
//        return docket(
//                "networkDevice",
//                "网络设备",
//                "网络设备",
//                "^/networkDevice.*"
//        );
//    }
//
//
//    @Bean
//    public Docket aiops() {
//        return docket(
//                "aiops",
//                "aiops",
//                "aiops",
//                "^/aiops.*"
//        );
//    }
//
//    @Bean
//    public Docket business() {
//        return docket(
//                "business",
//                "business",
//                "business",
//                "^/business.*"
//        );
//    }
//    private Docket docket(String groupName, String title, String description, String pathRegex) {
//        List<Parameter> list = new ArrayList<>();
//        Parameter parame1 = new ParameterBuilder().name("Authorization")
//                .description("登录凭证")
//                .modelRef(new ModelRef("string"))
//                .parameterType("header")
//                .required(false)
//                .build();
//        Parameter parame2 = new ParameterBuilder().name("cid")
//                .description("授权凭证")
//                .modelRef(new ModelRef("string"))
//                .parameterType("header")
//                .required(false)
//                .build();
//        Parameter parame3 = new ParameterBuilder().name("apiKey")
//                .description("apiKey信息")
//                .modelRef(new ModelRef("string"))
//                .parameterType("header")
//                .required(false)
//                .build();
//        Parameter parame4 = new ParameterBuilder().name("hostName")
//                .description("hostName")
//                .modelRef(new ModelRef("string"))
//                .parameterType("header")
//                .required(false)
//                .build();
//
//        list.add(parame1);
//        list.add(parame2);
//        list.add(parame3);
//        list.add(parame4);
//
//        return new Docket(DocumentationType.SWAGGER_2)
//                .groupName(groupName)
//                .ignoredParameterTypes(ApiIgnore.class)
//                .globalOperationParameters(list)
//                .select()
//                .paths(regex(pathRegex))
//                .build();
//    }
//    @Override
//    public void addResourceHandlers(ResourceHandlerRegistry registry) {
//
//        registry.addResourceHandler("swagger-ui.html")
//                .addResourceLocations("classpath:/META-INF/resources/");
//
//        registry.addResourceHandler("/webjars/**")
//                .addResourceLocations("classpath:/META-INF/resources/webjars/");
//    }
//}
//
