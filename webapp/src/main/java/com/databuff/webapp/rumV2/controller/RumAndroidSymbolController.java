package com.databuff.webapp.rumV2.controller;

import com.databuff.entity.rum.mysql.AndroidSymbolFile;
import com.databuff.entity.rum.web.AndroidSymbolSearchCriteria;
import com.databuff.webapp.config.common.CommonResponse;
import com.databuff.webapp.rumV2.model.AndroidSymbolUploadRequest;
import com.databuff.webapp.rumV2.service.RumAndroidSymbolFileService;
import com.github.pagehelper.PageInfo;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import javax.validation.ConstraintViolation;
import javax.validation.Validation;
import javax.validation.Validator;
import javax.validation.ValidatorFactory;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

@RestController
@RequestMapping("/rum/v2/android/symbol")
@Slf4j
public class RumAndroidSymbolController {

    @Autowired
    private RumAndroidSymbolFileService rumAndroidSymbolFileService;


    @ApiOperation(value = "应用概览-应用设置-Android符号表设置-列表查询", notes = "Android符号表列表查询")
    @PostMapping("/list")
    public ResponseEntity<PageInfo<AndroidSymbolFile>> listSymbolFiles(@RequestBody AndroidSymbolSearchCriteria search) {
        PageInfo<AndroidSymbolFile> symbolFiles = rumAndroidSymbolFileService.getSymbolFiles(search);
        return ResponseEntity.ok(symbolFiles);
    }

    @ApiOperation(value = "应用概览-应用设置-Android符号表设置-上传符号表", notes = "Android符号表上传")
    @PostMapping(value = "/upload", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    public CommonResponse<String> uploadSymbolFile(
            @RequestParam(required = false) MultipartFile file,
            @RequestParam String operationType,
            @RequestParam Integer appId,
            @RequestParam String versionCode,
            @RequestParam String versionName,
            @RequestParam(required = false, defaultValue = "false") Boolean forceOverwrite) {

        AndroidSymbolUploadRequest request = new AndroidSymbolUploadRequest();
        request.setFile(file);
        request.setOperationType(operationType);
        request.setAppId(appId);
        request.setVersionCode(versionCode);
        request.setVersionName(versionName);
        request.setForceOverwrite(forceOverwrite);

        try (ValidatorFactory factory = Validation.buildDefaultValidatorFactory()) {
            Validator validator = factory.getValidator();
            Set<ConstraintViolation<AndroidSymbolUploadRequest>> violations = validator.validate(request);
            if (!violations.isEmpty()) {
                String errorMessage = violations.stream()
                        .map(ConstraintViolation::getMessage)
                        .collect(Collectors.joining(", "));
                return new CommonResponse<>(400, errorMessage, null);
            }
        }

        String error = rumAndroidSymbolFileService.saveSymbolFile(request);
        if (error != null) {
            return new CommonResponse<>(400, error, null);
        }
        return new CommonResponse<>(200, "SUCCESS", "Android符号表上传成功");
    }



    @ApiOperation(value = "应用概览-应用设置-Android符号表设置-删除符号表", notes = "根据ids删除多个Android符号表文件")
    @DeleteMapping("/delete")
    public CommonResponse<String> deleteSymbolFiles(@RequestParam("ids") List<Long> ids) {
        try {
            int deletedCount = rumAndroidSymbolFileService.deleteSymbolFiles(ids);
            String message = String.format("Requested to delete %d files, successfully deleted %d files", ids.size(), deletedCount);

            if (deletedCount == 0) {
                return new CommonResponse<>(400, "NO_FILES_DELETED", message);
            }

            return new CommonResponse<>(200, "SUCCESS", message);
        } catch (Exception e) {
            log.error("Delete symbol files failed", e);
            return new CommonResponse<>(500, "SYSTEM_ERROR", "Failed to delete files: " + e.getMessage());
        }
    }

    @ApiOperation(value = "应用概览-应用设置-Android符号表设置-下载符号表", notes = "下载指定的Android符号表文件")
    @GetMapping("/download/{id}")
    public void downloadSymbolFile(@PathVariable Long id, HttpServletResponse response) {
        rumAndroidSymbolFileService.downloadSymbolFile(id, response);
    }
}
