package com.databuff.webapp.rumV2.service.impl.application.list;

import com.alibaba.fastjson.JSON;
import com.databuff.entity.rum.mysql.RumAppSettings;
import com.databuff.entity.rum.web.*;
import com.databuff.webapp.rumV2.model.ApplicationListDto;
import com.databuff.webapp.rumV2.model.ApplicationListDtoV2;
import com.databuff.webapp.rumV2.model.ApplicationMetrics;
import com.databuff.webapp.rumV2.model.MobileApplicationMetrics;
import com.databuff.webapp.rumV2.service.impl.application.detail.ScoreCalculator;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
@Slf4j
public class ApplicationDtoMapper {

    public List<ApplicationListDto> mapToApplicationListDtos(List<RumAppSettings> settings,
                                                             Map<Integer, ApplicationMetrics> metricsMap,
                                                             List<PageUVDto> uvData) {
        Map<Integer, ApplicationListDto> dtoMap = createDtoMap(settings);
        updateDtoMapWithMetrics(dtoMap, metricsMap);
        updateDtoMapWithScores(dtoMap, settings);
        updateDtoMapWithUvData(dtoMap, uvData);
        updateDtoMapWithHasDataFlag(dtoMap);
        return new ArrayList<>(dtoMap.values());
    }

    public List<ApplicationListDtoV2> mapToApplicationListDtosV2(List<RumAppSettings> settings,
                                                                 List<AppSessionMetricsDto> sessionMetrics,
                                                                 Map<Integer, ApplicationMetrics> webMetricsMap,
                                                                 Map<Integer, MobileApplicationMetrics> iosMetricsMap,
                                                                 Map<Integer, MobileApplicationMetrics> androidMetricsMap) {
        Map<Integer, ApplicationListDtoV2> dtoMap = new HashMap<>();

        // Create base DTOs
        for (RumAppSettings setting : settings) {
            ApplicationListDtoV2 dto = new ApplicationListDtoV2();
            dto.setId(setting.getId());
            dto.setAppName(setting.getAppName());
            dto.setAppType(setting.getAppType());
            dto.setCreateTime(setting.getCreateTime());
            dto.setStatus(setting.getStatus());

            calculateScore(dto, setting, webMetricsMap, iosMetricsMap, androidMetricsMap);

            dtoMap.put(setting.getId(), dto);
        }

        // Merge session metrics
        mergeSessionMetrics(dtoMap, sessionMetrics);
        // Update hasData flags
        updateDtoMapWithHasDataFlagV2(dtoMap);

        return new ArrayList<>(dtoMap.values());
    }

    private Map<Integer, ApplicationListDto> createDtoMap(List<RumAppSettings> appSettings) {
        Map<Integer, ApplicationListDto> dtoMap = new HashMap<>();
        for (RumAppSettings appSetting : appSettings) {
            ApplicationListDto dto = new ApplicationListDto();
            dto.setId(appSetting.getId());
            dto.setAppName(appSetting.getAppName());
            dto.setCreateTime(appSetting.getCreateTime());
            dto.setStatus(appSetting.getStatus());
            dtoMap.put(appSetting.getId(), dto);
        }
        return dtoMap;
    }

    private void updateDtoMapWithMetrics(Map<Integer, ApplicationListDto> dtoMap, Map<Integer, ApplicationMetrics> metricsMap) {
        for (Map.Entry<Integer, ApplicationMetrics> entry : metricsMap.entrySet()) {
            int appId = entry.getKey();
            ApplicationMetrics metrics = entry.getValue();
            ApplicationListDto dto = dtoMap.get(appId);
            if (dto != null) {
                dto.setActionTime(metrics.getAvgActionDuration());
                dto.setPv(metrics.getTotalPv());
                dto.setLcp(metrics.getAvgLcp());
                dto.setCls(metrics.getAvgCls());
                dto.setFid(metrics.getAvgFid());
                dto.setActionTime75(metrics.getActionDuration75());
                dto.setLcp75(metrics.getLcp75());
                dto.setFid75(metrics.getFid75());
                dto.setActionAvailability(metrics.getActionAvailability());
                dto.setSlowPageRatio(metrics.getSlowPageRate());
                dto.setJsErrorRate(metrics.getJsErrorRate());
            }
        }
    }

    private void updateDtoMapWithScores(Map<Integer, ApplicationListDto> dtoMap, List<RumAppSettings> appSettings) {
        for (ApplicationListDto dto : dtoMap.values()) {
            RumAppSettings appSetting = appSettings.stream()
                    .filter(setting -> setting.getId().equals(dto.getId()))
                    .findFirst()
                    .orElseThrow(() -> new RuntimeException("Application settings not found"));
            Integer score = ScoreCalculator.calculateUserExperienceScore(dto, appSetting);
            dto.setUserExperienceScore(score);
        }
    }

    private void updateDtoMapWithUvData(Map<Integer, ApplicationListDto> dtoMap, List<PageUVDto> uvData) {
        Map<Integer, Long> uvMap = uvData.stream()
                .collect(Collectors.toMap(
                        dto -> Integer.parseInt(dto.getIdentifier()),
                        PageUVDto::getUv
                ));
        for (ApplicationListDto dto : dtoMap.values()) {
            dto.setUv(uvMap.getOrDefault(dto.getId(), null));
        }
    }

    private void updateDtoMapWithHasDataFlag(Map<Integer, ApplicationListDto> dtoMap) {
        for (ApplicationListDto dto : dtoMap.values()) {
            boolean hasData = dto.getPv() != null || dto.getUv() != null || dto.getCls() != null ||
                    dto.getFid() != null || dto.getActionTime() != null ||
                    dto.getActionAvailability() != null || dto.getSlowPageRatio() != null ||
                    dto.getLcp() != null;
            dto.setHasData(hasData);
        }
    }

    private void updateDtoMapWithHasDataFlagV2(Map<Integer, ApplicationListDtoV2> dtoMap) {
        for (ApplicationListDtoV2 dto : dtoMap.values()) {
            boolean hasData = dto.getSessionCount() != null ||
                    dto.getInteractionCount() != null ||
                    dto.getExceptionCount() != null ||
                    dto.getUserExperienceScore() != null;
            dto.setHasData(hasData);
        }
    }


    private void calculateScore(ApplicationListDtoV2 dto, RumAppSettings setting,
                                Map<Integer, ApplicationMetrics> webMetricsMap,
                                Map<Integer, MobileApplicationMetrics> iosMetricsMap,
                                Map<Integer, MobileApplicationMetrics> androidMetricsMap) {
        if ("web".equalsIgnoreCase(setting.getAppType())) {
            if (webMetricsMap == null) {
                log.error("webMetricsMap is null ,应该是查询指标有问题");
                return;
            }
            ApplicationMetrics webMetrics = webMetricsMap.get(setting.getId());
            if (webMetrics != null) {
                ApplicationListDto webDto = new ApplicationListDto();
                webDto.setLcp75(webMetrics.getLcp75());
                webDto.setFid75(webMetrics.getFid75());
                webDto.setActionTime75(webMetrics.getActionDuration75());
                webDto.setSlowPageRatio(webMetrics.getSlowPageRate());
                webDto.setJsErrorRate(webMetrics.getJsErrorRate());
                webDto.setActionAvailability(webMetrics.getActionAvailability());
                dto.setUserExperienceScore(ScoreCalculator.calculateUserExperienceScore(webDto, setting));
            }
        } else if ("ios".equalsIgnoreCase(setting.getAppType())) {
            if (iosMetricsMap == null) {
                log.error("iosMetricsMap is null ,应该是查询指标有问题");
                return;
            }
            MobileApplicationMetrics iosMetrics = iosMetricsMap.get(setting.getId());
            if (iosMetrics != null) {
                AppScoreSettings scoreSettings = JSON.parseObject(setting.getScoreSettings(), IosScoreSettings.class);
                dto.setUserExperienceScore(ScoreCalculator.calculateMobileUserExperienceScore(iosMetrics, scoreSettings));
            }
        } else if ("android".equalsIgnoreCase(setting.getAppType())) {
            if (androidMetricsMap == null) {
                log.error("androidMetricsMap is null ,应该是查询指标有问题");
                return;
            }
            MobileApplicationMetrics androidMetrics = androidMetricsMap.get(setting.getId());
            if (androidMetrics != null) {
                //ios android 分数类还是要分开 有不同的初始值
                AppScoreSettings scoreSettings = JSON.parseObject(setting.getScoreSettings(), AndroidScoreSettings.class);
                dto.setUserExperienceScore(ScoreCalculator.calculateMobileUserExperienceScore(androidMetrics, scoreSettings));
            }
        }
    }

    private void mergeSessionMetrics(Map<Integer, ApplicationListDtoV2> dtoMap, List<AppSessionMetricsDto> sessionMetrics) {
        for (AppSessionMetricsDto metric : sessionMetrics) {
            ApplicationListDtoV2 dto = dtoMap.get(metric.getAppId());
            if (dto != null) {
                dto.setSessionCount(metric.getSessionCount());
                dto.setInteractionCount(metric.getInteractionCount());
                dto.setExceptionCount(metric.getExceptionCount());
            }
        }
    }

}
