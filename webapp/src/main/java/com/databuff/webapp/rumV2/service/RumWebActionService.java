package com.databuff.webapp.rumV2.service;

import com.databuff.entity.dto.WebSearchCriteria;
import com.databuff.entity.rum.moredb.RumWebAction;
import com.databuff.metric.dto.MetricByGroupGraph;
import com.github.pagehelper.PageInfo;

import java.util.List;
import java.util.Map;

public interface RumWebActionService {
    PageInfo<RumWebAction> webActionSearch(WebSearchCriteria search);

    Long getActionCount(WebSearchCriteria searchCriteria);

    List<Map<Object, Object>> ueTrend(WebSearchCriteria search);

    List<MetricByGroupGraph> ueTrendV2(WebSearchCriteria search);

    Map<Map<String, String>, MetricByGroupGraph> timeDistribution(WebSearchCriteria search);

    List<String> getAllActionAliases(WebSearchCriteria search);
}
