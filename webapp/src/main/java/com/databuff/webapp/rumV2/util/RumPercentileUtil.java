package com.databuff.webapp.rumV2.util;

import com.databuff.common.constants.Constant;
import com.databuff.common.tsdb.builder.QueryBuilder;
import com.databuff.common.tsdb.model.*;
import com.databuff.common.utils.PercentageLatencyUtil;
import com.databuff.metric.dto.MetricByGroupGraph;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.*;

/**
 * RUM 百分位计算及查询辅助工具类。
 * <p>
 * 封装了根据不同数据库类型（MoreDB、OpenGemini等）构建百分位查询的逻辑，
 * 以及从查询结果（特别是直方图桶数据）中计算百分位值的具体实现。
 * </p>
 */
@Component
@Slf4j
public class RumPercentileUtil {

    private final String dbType; // 通过Spring注入数据库类型

    /**
     * RUM 指标使用的直方图桶的数量 (0-110, 共111个桶)。
     */
    private static final int RUM_HISTOGRAM_BUCKET_COUNT = 111;
    /**
     * 数据库中存储直方图桶数据字段的基础名称。
     * **重要**: 此值必须与您 Flink Sink (如 TraceUtil) 中使用的基础名称一致。
     * 例如，如果 TraceUtil 中是 HistogramUtil.HISTOGRAM_FIELD，且其值为 "histogramField"。
     */
    private static final String HISTOGRAM_FIELD_BASE_NAME = "histogramField";

    /**
     * 构造函数，通过Spring注入数据库类型。
     * @param dbType 数据库类型字符串。
     */
    public RumPercentileUtil(@Value("${tsdb.db:moredb}") String dbType) {
        this.dbType = dbType;
        log.info("RumPercentileUtil initialized with dbType: {}", this.dbType);
    }

    /**
     * 获取当前工具类配置的数据库类型。
     * @return 数据库类型字符串。
     */
    public String getDbType() {
        return this.dbType;
    }

    /**
     * 修改 QueryBuilder 以添加获取百分位数据的聚合逻辑。
     *
     * @param qb                  需要修改的 QueryBuilder 实例。
     * @param percentileTable     存储百分位数据源（针对MoreDB）或直方图桶数据（针对OpenGemini）的表名。
     * @param percentileToGet     要获取的百分位数值 (例如 75)。
     * @param finalAlias          最终百分位指标在查询结果中的别名 (例如 "lcp75")。
     * 对于 OpenGemini，这也是桶数据别名的基础部分。
     */
    public void addPercentileAggregationsToQuery(QueryBuilder qb,
                                                 String percentileTable,
                                                 int percentileToGet,
                                                 String finalAlias) {
        qb.setMeasurement(percentileTable);


        if (Constant.TS_DB.MOREDB.equalsIgnoreCase(this.dbType)) {
            qb.addAgg(Aggregation.of(AggFun.PERCENTILE, String.valueOf(percentileToGet), finalAlias));
        } else { // OpenGemini 或其他
            for (int i = 0; i < RUM_HISTOGRAM_BUCKET_COUNT; i++) {
                String dbFieldName = HISTOGRAM_FIELD_BASE_NAME + i;
                String bucketAlias = finalAlias + "_bucket" + i;
                qb.addAgg(Aggregation.of(AggFun.SUM, dbFieldName, bucketAlias));
            }
        }
    }

    /**
     * 从已聚合的直方图桶数据中计算单个百分位值。
     * 主要供 ResultMapper 使用，当处理非时间序列的、聚合后的指标时。
     *
     * @param histogramBuckets  包含桶索引到桶计数值映射的 Map。TreeMap可以保证顺序。
     * @param percentileToCalc  要计算的百分位 (例如 75)。
     * @param metricType        指标类型 (例如 "lcp", "launchDuration")，主要用于日志。
     * @param groupIdentifier   分组标识 (如appId)，用于日志。
     * @param isMobile          是否为Mobile指标 (true则计算结果并返回纳秒, false返回毫秒)。
     * @return 计算得到的百分位值。
     */
    public double calculateSinglePercentileFromBuckets(
            Map<Integer, Double> histogramBuckets,
            int percentileToCalc,
            String metricType,
            String groupIdentifier,
            boolean isMobile) {
        // 直接调用内部的静态计算方法
        return calculatePercentileFromBucketsInternal(histogramBuckets, percentileToCalc, metricType, groupIdentifier, isMobile);
    }


    /**
     * 将包含各时间点直方图桶数据的 TSDBResultSet 转换为各时间点的百分位值趋势图。
     * 此方法专用于 OpenGemini 等数据库返回桶数据，需要在应用层计算百分位的场景。
     * (此方法主要由 RumApplicationDetailService 使用)
     *
     * @param resultSet         TSDB 查询结果。
     * @param metricTypeForCalc 指标类型 (例如 "lcp", "launchDuration")。
     * @param percentileToCalc  要计算的百分位 (例如 75)。
     * @param finalAlias        最终在图表中展示此百分位值的列名/别名 (例如 "lcp75")。
     * @param isMobile          是否为Mobile指标 (true则计算结果为纳秒, false为毫秒)。
     * @return MetricByGroupGraph 列表。
     */
    public List<MetricByGroupGraph> convertHistogramResultToPercentileTrendGraph(
            TSDBResultSet resultSet, String metricTypeForCalc, int percentileToCalc, String finalAlias, boolean isMobile) {
        List<MetricByGroupGraph> graphList = new ArrayList<>();
        if (resultSet == null || resultSet.getResults() == null || resultSet.getResults().isEmpty() ||
                resultSet.getResults().get(0).getSeries() == null) {
            log.warn("RumPercentileUtil: convertHistogramResultToPercentileTrendGraph 收到空或无效的结果集 for metric: {}, P{}", metricTypeForCalc, percentileToCalc);
            return graphList;
        }

        for (TSDBResult tsdbResult : resultSet.getResults()) {
            if (tsdbResult.getSeries() == null) continue;
            for (TSDBSeries series : tsdbResult.getSeries()) {
                MetricByGroupGraph metricGraph = new MetricByGroupGraph();
                String appIdTagValue = (series.getTags() != null) ? series.getTags().getOrDefault("appId", "未知App") : "未知App";
                metricGraph.setName(appIdTagValue + " - " + finalAlias + " 趋势图");
                metricGraph.setTags(series.getTags());

                List<List<Object>> processedTrendValues = new ArrayList<>();
                int timeColIndex = series.getColumnIndex("time");
                if (timeColIndex == -1) {
                    log.warn("RumPercentileUtil: 时间列 'time' 未在直方图结果中找到 (metric: {}, P{}, group: {}), 无法生成趋势图。",
                            metricTypeForCalc, percentileToCalc, series.getTags());
                    continue;
                }

                if (series.getValues() != null) {
                    for (List<Object> rowValues : series.getValues()) {
                        if (rowValues == null || rowValues.size() <= timeColIndex || rowValues.get(timeColIndex) == null) continue;

                        Map<Integer, Double> histogramBucketsAtThisTime = new TreeMap<>();
                        for (int i = 0; i < RUM_HISTOGRAM_BUCKET_COUNT; i++) {
                            String bucketAlias = finalAlias + "_bucket" + i;
                            int bucketColIdx = series.getColumnIndex(bucketAlias);
                            if (bucketColIdx != -1 && rowValues.size() > bucketColIdx && rowValues.get(bucketColIdx) instanceof Number) {
                                histogramBucketsAtThisTime.put(i, ((Number) rowValues.get(bucketColIdx)).doubleValue());
                            } else {
                                histogramBucketsAtThisTime.put(i, 0.0);
                            }
                        }

                        double percentileValue = calculatePercentileFromBucketsInternal(
                                histogramBucketsAtThisTime, percentileToCalc, metricTypeForCalc, appIdTagValue, true);

                        List<Object> trendDataPoint = new ArrayList<>();
                        trendDataPoint.add(rowValues.get(timeColIndex));
                        trendDataPoint.add(percentileValue);
                        processedTrendValues.add(trendDataPoint);
                    }
                }
                metricGraph.setValues(processedTrendValues);
                metricGraph.setColumns(Arrays.asList("time", finalAlias));
                metricGraph.setUnits(Arrays.asList("毫秒", guessUnitsForTrendInternal(metricTypeForCalc, isMobile)));
                metricGraph.setDescribeCns(Arrays.asList("时间", finalAlias));
                graphList.add(metricGraph);
            }
        }
        return graphList;
    }

    /**
     * 内部静态辅助方法：从直方图桶数据计算百分位值。
     */
    private static double calculatePercentileFromBucketsInternal(Map<Integer, Double> histogramBuckets, int percentile, String metricType, String groupIdentifier, boolean isNeedMultiple) {
        double totalCount = 0;
        for(Double count : histogramBuckets.values()){
            if(count != null){
                totalCount += count;
            }
        }

        if (totalCount == 0) return 0.0;

        double targetRank = (percentile / 100.0) * totalCount;
        if (targetRank <= 0 && totalCount > 0) targetRank = 1e-9;
        if (targetRank > totalCount) targetRank = totalCount;

        double cumulativeCount = 0;
        TreeMap<Integer, Double> sortedBuckets = new TreeMap<>(histogramBuckets);

        for (Map.Entry<Integer, Double> entry : sortedBuckets.entrySet()) {
            int bucketIndex = entry.getKey();
            double bucketCountInCurrent = entry.getValue() != null ? entry.getValue() : 0.0;
            if (bucketCountInCurrent <= 0) continue;

            long[] intervalMicros;
            try {
                if (bucketIndex < 0 || bucketIndex >= RUM_HISTOGRAM_BUCKET_COUNT) {
                    log.warn("RumPercentileUtil内部计算: 指标 '{}' (分组: {}) 桶索引 {} 超出范围 [0, {}]，此桶不计入。",
                            metricType, groupIdentifier, bucketIndex, RUM_HISTOGRAM_BUCKET_COUNT - 1);
                    continue;
                }
                intervalMicros = PercentageLatencyUtil.getIndexTimeInterval(bucketIndex);
            } catch (Exception e) {
                log.error("RumPercentileUtil内部计算: 获取指标 '{}' (分组: {}) 桶 {} 区间失败: {}",
                        metricType, groupIdentifier, bucketIndex, e.getMessage(), e);
                return 0.0;
            }

            double lowerBoundUnits = intervalMicros[0]; // 微秒
            double upperBoundUnits = intervalMicros[1]; // 微秒
            double bucketWidthUnits = upperBoundUnits - lowerBoundUnits;
            if (bucketWidthUnits < 0) bucketWidthUnits = 0;

            if (cumulativeCount + bucketCountInCurrent >= targetRank) {
                double rankWithinBucket = targetRank - cumulativeCount;
                double valueInMicros = lowerBoundUnits; // 默认下限
                if (bucketCountInCurrent > 0) { // 避免除以零
                    valueInMicros += (rankWithinBucket / bucketCountInCurrent) * bucketWidthUnits;
                } else if (rankWithinBucket > 0) { // 如果桶计数为0但仍有排名需求（理论上不应发生），取上限
                    valueInMicros = upperBoundUnits;
                }
                return isNeedMultiple ? valueInMicros * 1000.0 : valueInMicros;
            }
            cumulativeCount += bucketCountInCurrent;
        }

        if (!sortedBuckets.isEmpty()) {
            int lastBucketIndex = sortedBuckets.lastKey();
            try {
                if (lastBucketIndex >= 0 && lastBucketIndex < RUM_HISTOGRAM_BUCKET_COUNT) {
                    double valueInMicros = PercentageLatencyUtil.getIndexTimeInterval(lastBucketIndex)[1];
                    return isNeedMultiple ? valueInMicros * 1000.0 : valueInMicros;
                }
            } catch (Exception e) {log.error("RumPercentileUtil内部计算: 获取指标 '{}' (分组: {}) 末桶 {} 上限失败: {}", metricType, groupIdentifier, lastBucketIndex, e.getMessage(), e);}
        }
        log.warn("RumPercentileUtil内部计算: 未能从直方图计算出指标 '{}' (分组: {}) P{} 值。", metricType, groupIdentifier, percentile);
        return 0.0;
    }

    /**
     * 内部静态辅助方法：猜测指标的单位用于图表展示。
     */
    public static String guessUnitsForTrendInternal(String metricType, boolean isMobile) {
        if (metricType != null) {
            String lowerField = metricType.toLowerCase();
            String baseMetric = lowerField.replaceAll("p\\d+$", "").replaceAll("\\d+$", "");

            if (baseMetric.contains("duration") || baseMetric.equals("lcp") || baseMetric.equals("fid") || baseMetric.equals("fcp")) {
                return isMobile ? "纳秒" : "毫秒";
            }
            if (lowerField.contains("rate") || lowerField.contains("availability")) {
                return "%";
            }
            if (lowerField.contains("count")) {
                return "次";
            }
        }
        return "";
    }

    /**
     * 处理包含按类型分组的多个时间序列的 TSDBResultSet，为每种类型计算百分位趋势。
     * 返回的 Map 的键是指标类型，MetricByGroupGraph 中的值列名也是该指标类型。
     *
     * @param rs               TSDB 查询结果。
     * @param typeTagKey       在 Series tags 中用于区分不同指标类型的键名 (例如 "type")。
     * @param percentileToGet  要计算的百分位数值 (例如 75)。
     * @param valueAliasInQuery 在 MoreDB 查询中直接百分位值的别名，或在 OpenGemini 查询中桶别名的基础 (例如 "upperVal")。
     * @param isNeedMultiple         是否为需要乘1000
     * @return 一个 Map，键是指标类型 (来自 typeTagKey)，值是对应的 MetricByGroupGraph。
     */
    public Map<String, MetricByGroupGraph> processMultiTypePercentileResultSet(
            TSDBResultSet rs,
            String typeTagKey,
            int percentileToGet,
            String valueAliasInQuery, // 例如 "upperVal"
            boolean isNeedMultiple) {

        Map<String, MetricByGroupGraph> resultMap = new LinkedHashMap<>();
        if (rs == null || rs.getResults() == null || rs.getResults().isEmpty() ||
                rs.getResults().get(0).getSeries() == null) {
            log.warn("RumPercentileUtil: processMultiTypePercentileResultSet 收到空或无效的结果集。");
            return resultMap;
        }

        for (TSDBResult tsdbResult : rs.getResults()) {
            if (tsdbResult.getSeries() == null) continue;
            for (TSDBSeries series : tsdbResult.getSeries()) { // 每个 series 对应一个 type 和 actionName 的组合
                Map<String, String> tags = series.getTags();
                String metricType = (tags != null) ? tags.get(typeTagKey) : null;
                String actionName = (tags != null) ? tags.get("actionName") : "未知Action"; // 假设 actionName 也在 tags 中

                if (metricType == null || metricType.isEmpty()) {
                    log.warn("RumPercentileUtil: Series 中缺少指定的 typeTagKey '{}'。 Tags: {}", typeTagKey, tags);
                    metricType = "unknownType_" + UUID.randomUUID().toString().substring(0, 6);
                }

                MetricByGroupGraph metricGraph = new MetricByGroupGraph();
                // 构建图表名称时可以包含 actionName 和 metricType
                String graphDisplayName = metricType; // 这是 orderMetricByFields 期望的 key
                // metricGraph.setName(appIdTagValue + " - " + actionName + " - " + metricType + " P" + percentileToGet + " 趋势图");
                // 对于Map的key，我们使用原始的metricType，例如"lcp", "fid", "actionDuration"
                // 对于MetricByGroupGraph内部的列名，parseUpperPercentile之前用的是typeField，这里也用metricType

                metricGraph.setName("Trend for " + metricType + " P" + percentileToGet); // 内部图表名称
                metricGraph.setTags(tags); // 保留所有分组标签，包括 actionName 和 type

                List<List<Object>> processedTrendValues = new ArrayList<>();
                Integer timeColIndex = series.getColumnIndex("time"); // 使用您TSDBSeries的getColumnIndex
                if (timeColIndex == null || timeColIndex == -1) { // 检查 null 和 -1
                    log.warn("RumPercentileUtil: 时间列 'time' 未在结果中找到 (metricType: {}, group: {}), 无法生成趋势图。",
                            metricType, tags);
                    continue;
                }

                if (series.getValues() != null) {
                    for (List<Object> rowValues : series.getValues()) {
                        if (rowValues == null || rowValues.size() <= timeColIndex || rowValues.get(timeColIndex) == null) continue;
                        Object timeValue = rowValues.get(timeColIndex);
                        double percentileAtTimePoint;

                        if (Constant.TS_DB.MOREDB.equalsIgnoreCase(this.dbType)) {
                            Integer directValueIdx = series.getColumnIndex(valueAliasInQuery); // e.g., "upperVal"
                            if (directValueIdx != null && directValueIdx != -1 && rowValues.size() > directValueIdx && rowValues.get(directValueIdx) instanceof Number) {
                                percentileAtTimePoint = ((Number) rowValues.get(directValueIdx)).doubleValue();
                                // MoreDB 直接返回的值，假设单位已符合期望，或在这里根据 isNeedMultiple 转换
                                // 如果 PercentageLatencyUtil 的桶是微秒，那么这里假设MoreDB的PERCENTILE也是微秒级
                                percentileAtTimePoint = isNeedMultiple ? percentileAtTimePoint * 1000.0 : percentileAtTimePoint;
                            } else {
                                log.warn("RumPercentileUtil (MoreDB): 未能在行数据中找到或转换列 '{}' 的值 for metricType: {}", valueAliasInQuery, metricType);
                                percentileAtTimePoint = 0.0;
                            }
                        } else { // OpenGemini 或其他
                            Map<Integer, Double> histogramBucketsAtThisTime = new TreeMap<>();
                            for (int i = 0; i < RUM_HISTOGRAM_BUCKET_COUNT; i++) {
                                String bucketAlias = valueAliasInQuery + "_bucket" + i; // 例如 "upperVal_bucket0"
                                Integer bucketColIdx = series.getColumnIndex(bucketAlias);
                                if (bucketColIdx != null && bucketColIdx != -1 && rowValues.size() > bucketColIdx && rowValues.get(bucketColIdx) instanceof Number) {
                                    histogramBucketsAtThisTime.put(i, ((Number) rowValues.get(bucketColIdx)).doubleValue());
                                } else {
                                    histogramBucketsAtThisTime.put(i, 0.0);
                                }
                            }
                            percentileAtTimePoint = calculatePercentileFromBucketsInternal( // 这个方法返回的是基于isMobile转换后的单位
                                    histogramBucketsAtThisTime, percentileToGet, metricType, tags.toString(), isNeedMultiple);
                        }

                        List<Object> trendDataPoint = new ArrayList<>();
                        trendDataPoint.add(timeValue);
                        trendDataPoint.add(percentileAtTimePoint);
                        processedTrendValues.add(trendDataPoint);
                    }
                }
                metricGraph.setValues(processedTrendValues);
                // MetricByGroupGraph 的列名设置为 "time" 和 该指标的类型名 (例如 "lcp", "fid")
                // 这样 orderMetricByFields(map.values(), search.getFields()) 时，
                // map.values() 中的每个 MetricByGroupGraph 的第二列名就能和 search.getFields() 中的元素匹配上。
                metricGraph.setColumns(Arrays.asList("time", metricType));
                metricGraph.setUnits(Arrays.asList("毫秒", guessUnitsForTrendInternal(metricType, isNeedMultiple)));
                metricGraph.setDescribeCns(Arrays.asList("时间", metricType)); // 中文描述也用 metricType

                // Map的key是原始的metricType (例如 "lcp", "actionDuration")
                resultMap.put(metricType, metricGraph);
            }
        }
        return resultMap;
    }

}