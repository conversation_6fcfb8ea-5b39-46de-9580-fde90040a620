package com.databuff.webapp.rumV2.service.impl.application.creation;

import com.alibaba.fastjson.JSON;
import com.databuff.dao.mysql.RumAppV2Mapper;
import com.databuff.dao.mysql.SaasCustomerMapper;
import com.databuff.entity.rum.mysql.RumAppSettings;
import com.databuff.entity.rum.web.*;
import com.databuff.webapp.rumV2.model.CreateApplicationRequest;
import com.databuff.webapp.rumV2.model.CreateApplicationResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.UUID;

@Service
public class RumApplicationCreationService {

    private static final String APP_NAME_EXISTS_ERROR = "Application name already exists";
    private static final String API_KEY_INVALID_ERROR = "Api key is invalid";

    @Autowired
    private SaasCustomerMapper customerMapper;

    @Value("#{'${rum.application.upload.urls:http://webapp/rum,https://webapp/rum}'.split(',')}")
    private List<String> dataUploadUrls;

    @Value("${rum.ios.score.launch-duration:770}")
    private double iosLaunchDuration;

    @Value("${rum.ios.score.action-duration:330}")
    private double iosActionDuration;

    @Value("${rum.ios.score.page-duration:460}")
    private double iosPageDuration;

    @Value("${rum.ios.score.request-duration:610}")
    private double iosRequestDuration;

    @Value("${rum.ios.score.anr-rate:0.84}")
    private double iosAnrRate;

    @Value("${rum.ios.score.crash-rate:0.45}")
    private double iosCrashRate;

    @Value("${rum.android.score.launch-duration:1050}")
    private double androidLaunchDuration;

    @Value("${rum.android.score.action-duration:340}")
    private double androidActionDuration;

    @Value("${rum.android.score.page-duration:290}")
    private double androidPageDuration;

    @Value("${rum.android.score.request-duration:816}")
    private double androidRequestDuration;

    @Value("${rum.android.score.anr-rate:2.59}")
    private double androidAnrRate;

    @Value("${rum.android.score.crash-rate:0.55}")
    private double androidCrashRate;

    @Autowired
    private RumAppV2Mapper rumAppV2Mapper;

    public CreateApplicationResponse createApplication(CreateApplicationRequest request) {
        if (rumAppV2Mapper.existsByAppName(request.getAppName())) {
            throw new IllegalArgumentException(APP_NAME_EXISTS_ERROR);
        }

        String appKey = generateUniqueAppKey();
        int tenantIdByApiKey = customerMapper.getTenantIdByApiKey(request.getApiKey());
        if (tenantIdByApiKey == 0) {
            throw new IllegalArgumentException(API_KEY_INVALID_ERROR);
        }

        RumAppSettings newApp = new RumAppSettings();
        newApp.setAppName(request.getAppName());
        newApp.setAppKey(appKey);
        newApp.setApiKeyId(tenantIdByApiKey);
        newApp.setApiKey(request.getApiKey());
        newApp.setDataUploadUrl(String.join(",", dataUploadUrls));
        newApp.setAppType(request.getAppType().toString());

        // 根据应用类型设置不同的默认配置
        if (CreateApplicationRequest.AppType.ios == request.getAppType()) {
            newApp.setScoreSettings(JSON.toJSONString(createDefaultIosScoreSettings()));
            newApp.setSecuritySettings(JSON.toJSONString(new IosSecuritySettings()));
        } else if (CreateApplicationRequest.AppType.android == request.getAppType()) {
            newApp.setScoreSettings(JSON.toJSONString(createDefaultAndroidScoreSettings()));
            newApp.setSecuritySettings(JSON.toJSONString(new AndroidSecuritySettings()));
        } else {
            newApp.setScoreSettings(JSON.toJSONString(new WebScoreSettings()));
        }

        rumAppV2Mapper.insertApplication(newApp);

        CreateApplicationResponse response = new CreateApplicationResponse();
        response.setAppName(newApp.getAppName());
        response.setAppKey(newApp.getAppKey());
        response.setAppType(newApp.getAppType());
        response.setDataUploadUrls(dataUploadUrls);

        return response;
    }

    private IosScoreSettings createDefaultIosScoreSettings() {
        // 性能评分阈值要能使用配置 ,可能每季都会更新 , 后续修改配置文挡 新增新应用时可修改默认阈值
        IosScoreSettings settings = new IosScoreSettings();
        settings.setLaunchDurationThreshold(iosLaunchDuration);
        settings.setActionDurationThreshold(iosActionDuration);
        settings.setPageDurationThreshold(iosPageDuration);
        settings.setRequestDurationThreshold(iosRequestDuration);
        settings.setAnrRateThreshold(iosAnrRate);
        settings.setCrashRateThreshold(iosCrashRate);

        return settings;
    }

    private AndroidScoreSettings createDefaultAndroidScoreSettings() {
        // 性能评分阈值要能使用配置 ,可能每季都会更新 , 后续修改配置文挡 新增新应用时可修改默认阈值
        AndroidScoreSettings settings = new AndroidScoreSettings();
        settings.setLaunchDurationThreshold(androidLaunchDuration);
        settings.setActionDurationThreshold(androidActionDuration);
        settings.setPageDurationThreshold(androidPageDuration);
        settings.setRequestDurationThreshold(androidRequestDuration);
        settings.setAnrRateThreshold(androidAnrRate);
        settings.setCrashRateThreshold(androidCrashRate);

        return settings;
    }

    private String generateUniqueAppKey() {
        return UUID.randomUUID().toString();
    }
}
