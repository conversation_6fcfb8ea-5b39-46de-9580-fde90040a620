package com.databuff.webapp.rumV2.model;

import com.github.pagehelper.PageInfo;

import java.util.List;

public class ExtendedPageInfo<T> extends PageInfo<T> {
    private int hiddenAppsCount;

    public ExtendedPageInfo(List<T> list) {
        super(list);
    }

    public void setHiddenAppsCount(int hiddenAppsCount) {
        this.hiddenAppsCount = hiddenAppsCount;
    }

    public int getHiddenAppsCount() {
        return hiddenAppsCount;
    }
}
