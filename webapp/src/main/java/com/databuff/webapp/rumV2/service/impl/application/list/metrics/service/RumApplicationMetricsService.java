package com.databuff.webapp.rumV2.service.impl.application.list.metrics.service;

import com.databuff.entity.rum.web.ApplicationSearchCriteria;
import com.databuff.entity.rum.web.PvUvTrendRequest;
import com.databuff.entity.rum.web.UserExperienceScoreRequest;
import com.databuff.webapp.rumV2.model.ApplicationMetrics;
import com.databuff.webapp.rumV2.model.MobileApplicationMetrics;
import com.databuff.webapp.rumV2.model.MobileType;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.TreeMap;

@Service
public class RumApplicationMetricsService {

    @Autowired
    private RumApplicationMoreDBService rumApplicationMoreDBService;

    public Map<Integer, ApplicationMetrics> getApplicationMetrics(ApplicationSearchCriteria criteria, List<String> appIds) {
        return rumApplicationMoreDBService.getApplicationMetrics(criteria, appIds);
    }

    public ApplicationMetrics getApplicationMetric(UserExperienceScoreRequest request) {
        return rumApplicationMoreDBService.getApplicationMetric(request);
    }

    public TreeMap<String, Double> getPvTrend(PvUvTrendRequest request) {
        return rumApplicationMoreDBService.getPvTrend(request);
    }

    public MobileApplicationMetrics getMobileApplicationMetrics(UserExperienceScoreRequest request, MobileType platform) {
        return rumApplicationMoreDBService.getMobileApplicationMetrics(request, platform);
    }

    public Map<Integer, MobileApplicationMetrics> getAllMobileApplicationMetrics(ApplicationSearchCriteria criteria, List<String> appIds, MobileType platform) {
        return rumApplicationMoreDBService.getAllMobileApplicationMetrics(criteria, appIds, platform);
    }

}
