package com.databuff.webapp.component.olap;

import com.alibaba.druid.pool.DruidDataSource;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.jdbc.core.JdbcTemplate;

@Configuration
public class DatasourceConfig {

    @Value("${spring.olap.url}")
    private String olapUrl;
    @Value("${spring.olap.username}")
    private String olapUserName;
    @Value("${spring.olap.password}")
    private String olapPassword;

    @Bean(name = "olapJdbcTemplate")
    public JdbcTemplate olapJdbcTemplate() {
        DruidDataSource druidDataSource = new DruidDataSource();
        druidDataSource.setUrl(olapUrl);
        druidDataSource.setUsername(olapUserName);
        druidDataSource.setPassword(olapPassword);
        druidDataSource.setTestOnBorrow(false);
        druidDataSource.setTestOnReturn(false);
        return new JdbcTemplate(druidDataSource);
    }
}