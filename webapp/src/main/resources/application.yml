org:
  id: databuff
  isDocker: true
#版本
version: "DataBuff|v2.9.1"
#配置都写到下面，上面的不要动
server:
  port: 18080

# actuator配置
management:
  endpoints:
    web:
      exposure:
        include: "*"
        exclude: "env,beans" # 开启所有端点，但是排除env和beans端点

databuff:
  # 突变检测配置
  changePoint:
    api:
      # 所有接口超时时间（秒）
      timeout: 30
      url:
        # 数据变更点检测接口
        detect: http://root-engine:18666/abnormal/detect
        # 数据变更点预览接口
        preview: http://root-engine:18666/abnormal/preview

spring:
  main:
    allow-circular-references: true
  cloud:
    compatibility-verifier:
      enabled: false
  # 上传最大文件限制
  servlet:
    multipart:
      max-file-size: 5048MB
      max-request-size: 5048MB
  jackson:
    date-format: yyyy-MM-dd HH:mm:ss
    time-zone: GMT+8
  datasource:
    name: Databuff
    url: **************************************************************************************************************************************************************************************
    username: root
    password: 234*(sdlj12
    # 使用Druid数据源
    type: com.alibaba.druid.pool.DruidDataSource
    driver-class-name: com.mysql.jdbc.Driver
    query-timeout: 300
    druid:
      filters: stat
      maxActive: 20
      initialSize: 1
      maxWait: 60000
      minIdle: 1
      timeBetweenEvictionRunsMillis: 60000
      minEvictableIdleTimeMillis: 300000
      validationQuery: select 'x'
      testWhileIdle: true
      testOnBorrow: false
      testOnReturn: false
      poolPreparedStatements: true
      maxOpenPreparedStatements: 20
  olap:
    driver-class-name: com.mysql.jdbc.Driver
    url: ************************************
    httpUrl: starrocks:8040
    feIpPorts: starrocks:8040
    username: root
    password: Databuff@123
    openMonitor: true
    maxFilterRatio: 1
  redis:
    host: redis
    port: 6379
    timeout: 10000
    password: Databuff@123+
    jedis:
      pool:
        max-idle: 8
        min-idle: 0
        max-wait: -1
        max-active: 200
  resources:
    add-mappings: false
  mvc:
    throw-exception-if-no-handler-found: true
  ### xxl-job, email
  mail:
    host:
    port:
    username:
    from:
    password:
    properties:
      mail:
        smtp:
          auth:
          starttls:
            enable:
            required:
          socketFactory:
            class:

  # 使用aop操作日志
  aop:
    auto: true
    proxy-target-class: true


# mysql config
mybatis-plus:
  mapper-locations: classpath:mappers/*.xml,classpath:mapper/*.xml
  configuration:
    #    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
    log-impl: org.apache.ibatis.logging.nologging.NoLoggingImpl
    map-underscore-to-camel-case: true
    cache-enabled: false
  global-config:
    refresh: true
    db-config:
      id-type: auto
      field-strategy: not_empty
      db-column-underline: true
      logic-delete-value: 1
      logic-not-delete-value: 0
      db-type: mysql
  # Mybatis配置Model类对应
#  type-aliases-package: com.databuff.webapp.admin.model

pagehelper:
  params: count=countSql
  # 指定分页插件使用哪种方言
  helper-dialect: mysql
  # 分页合理化参数 pageNum<=0时会查询第一页 pageNum>pages(超过总数时) 会查询最后一页
  reasonable: 'true'
  support-methods-arguments: 'false'

mapper:
  # 通用Mapper的insertSelective和updateByPrimaryKeySelective中是否判断字符串类型!=''
  not-empty: true

#日志级别从低到高分为TRACE < DEBUG < INFO < WARN < ERROR < FATAL，如果设置为WARN，则低于WARN的信息都不会输出
logging:
  config: classpath:logback-spring.xml
  # Debug打印SQL
  level:
    com.databuff.dao.mysql: info

swagger:
  enable: false
jasypt:
  encryptor:
    password: 123456   #开发环境密码

#数据保存天数设置，定时清理
data:
  #保存天数
  expireDays: 30

tsdb:
  # 默认数据库配置，支持集群，适合读写,优先读写
  db: moredb
  # 连接配置（同时用于读写，支持逗号分隔的集群配置）
  url: moredb:2890
  api: http://moredb:8080/api
  user: databuff
  password: databuff666
  duration: 30d
  # 生产环境连接池优化配置
  max-total: 100
  max-idle: 50
  min-idle: 10
  max-wait-millis: 3000
  soft-min-evictable-idle-time-millis: 600000
  test-on-borrow: false
  test-while-idle: true
  time-between-eviction-runs-millis: 120000
  queryTimeout: 60000
  shard: 2
  replication: 1
  interval: 60

  # moredb数据库，支持集群，只写不读
  #  moredb:
  #    # 连接配置（支持逗号分隔的集群配置）
  #    url: moredb:2890
  #    api: http://moredb:8080/api
  #    user: databuff
  #    password: databuff666

  # opengemini数据库，支持集群，只写不读
  opengemini:
    # 连接配置（支持逗号分隔的集群配置）
    url: opengemini:8086
    api: http://opengemini:8086
    user: admin
    password: password123


kafka:
  bootstrap-servers: kafka:9092

#渠道: 官方000
channel: "000"
# logo路径
logoPath:
# 二维码地址
qrUrl: http://databuff.com:7300

# AccessToken过期时间-5分钟-5*60(秒为单位)
accessTokenExpireTime: 30240000
# RefreshToken过期时间-30分钟-30*60(秒为单位)
refreshTokenExpireTime: 3600
# dczw缓存过期时间-5分钟-5*60(秒为单位)(一般设置与AccessToken过期时间一致)
shiroCacheExpireTime: 3600

# 云通信短信API
sms:
  accessKeyId:
  accessKeySecret:
  regionId:
  signName:
  templateCode:
  enable: false
# 邮件发件人API
mail:
  addr:
  user: DataBuff
  password:
  enable: false
  smtp:
    host: smtp.dacheng-tech.com
    port: 25
    ssl: false


  ### datasource-pool
  #  spring.datasource.type=com.zaxxer.hikari.HikariDataSource
  #  spring.datasource.hikari.minimum-idle=10
  #  spring.datasource.hikari.maximum-pool-size=30
  #  spring.datasource.hikari.auto-commit=true
  #  spring.datasource.hikari.idle-timeout=30000
  #  spring.datasource.hikari.pool-name=HikariCP
  #  spring.datasource.hikari.max-lifetime=900000
  #  spring.datasource.hikari.connection-timeout=10000
  #  spring.datasource.hikari.connection-test-query=SELECT 1
  #  spring.datasource.hikari.validation-timeout=1000

  ### xxl-job, access token
xxl:
  job:
    accessToken: default_token
    ### xxl-job, i18n (default is zh_CN, and you can choose "zh_CN", "zh_TC" and "en")
    i18n: zh_CN
    ## xxl-job, triggerpool max size
    triggerpool:
      fast:
        max: 20
      slow:
        max: 10
    ### xxl-job, log retention days
    logretentiondays: 5


queryOlapEnabled: true

# dts配置
dts:
  url: http://dts:18111


root-engine.url: http://root-engine:18666/root



rum:
  application:
    upload:
      urls: http://webapp/rum/rum,https://webapp/rum
  sourcemap:
    storage:
      path: /var/dacheng/staticDir/sourcemap
  sdk:
    base-path: /var/dacheng/sdk
    file-names:
      web: browser.js
      ios: DatabuffRUM_1.0.1.xcframework.zip
      android: databuff-android-1.0.0.aar
  ios:
    score:
      launch-duration: 770
      action-duration: 330
      page-duration: 460
      request-duration: 610
      anr-rate: 0.84
      crash-rate: 0.45
  android:
    score:
      launch-duration: 1050
      action-duration: 340
      page-duration: 290
      request-duration: 816
      anr-rate: 2.59
      crash-rate: 0.55
  native:
    library:
      path: /var/dacheng/lib/native
  storage:
    ios:
      user: /var/dacheng/staticDir/ios/dsym      # 用户上传的iOS符号表
      system: /var/dacheng/staticDir/ios/system   # 系统iOS符号表
    android:
      mapping: /var/dacheng/staticDir/android/mapping      # 用户上传的android符号表

datahub: #  datahub对应的配置。
  # 配置模板, ${cluster_id}、${port}、${other_path}会被替换成对应的值,如果环境有额外的路径，可以设置额外的，在nginx等进行解析
  webhook-template: /datahub/{{cluster_id}}/{{port}}/{{other_path}}
  default-log-topic: dc_databuff_datahub_log
  default-metric-topic: dc_databuff_datahub_metric
  default-trace-topic: dc_databuff_datahub_trace
  default-event-topic: dc_event
  default-port: 30666