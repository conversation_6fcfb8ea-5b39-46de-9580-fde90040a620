package com.databuff.webapp.util;

import com.databuff.webapp.rumV2.model.ThreadStackDto;
import org.junit.Test;

import java.util.List;

import static org.junit.Assert.*;

public class IosErrorStackParserTest {

    @Test
    public void testParseStackTrace() {
        String incidentLog = "Incident Identifier: 4370415911835779\n" +
                "CrashReporter Key:   1.0.0\n" +
                "Hardware Model:      iPhone14,5\n" +
                "Process:             iOSTest [82144]\n" +
                "Identifier:          iOSTest\n" +
                "Version:             1.0 (1)\n" +
                "Backtrace of Thread 259:\n" +
                "libsystem_kernel.dylib          0x209adca24 semaphore_wait_trap + 8\n" +
                "libdispatch.dylib               0x1027ea788 _dispatch_sema4_wait + 28\n" +
                "UIKitCore                       0x1ce60ca04 <redacted> + 144\n" +
                "iOSTest                         0x102049f90 main + 140\n" +
                "\n" +
                "Backtrace of Thread 4867:\n" +
                "libsystem_kernel.dylib          0x209adcaa8 mach_msg2_trap + 8\n" +
                "DatabuffRUM                     0x102e33a7c bs_mach_copyMem + 76\n" +
                "libsystem_pthread.dylib         0x21a1f9dbc _pthread_wqthread + 228\n";

        List<ThreadStackDto> result = IosErrorStackParser.parseStackTrace(incidentLog);
        assertBasicParsing(result);
    }

    @Test
    public void testParseEmptyStackTrace() {
        String emptyLog = "Incident Identifier: 123456\n" +
                "CrashReporter Key:   1.0.0\n" +
                "Hardware Model:      iPhone14,5\n";

        List<ThreadStackDto> result = IosErrorStackParser.parseStackTrace(emptyLog);
        assertTrue(result.isEmpty());
    }

    @Test
    public void testParseComplexStackTrace() {
        String complexLog = "Backtrace of Thread 0:\n" +
                "CoreFoundation                  0x1cc171da4 objc_exception_throw + 164\n" +
                "iOSTest                         0x1027c9884 -[CrashViewController arrCrash] + 136\n" +
                "DatabuffRUM                     0x102dac880 processAction + 312\n" +
                "\n" +
                "Backtrace of Thread 1:\n" +
                "libsystem_kernel.dylib          0x209adca34 semaphore_timedwait_trap + 8\n" +
                "DatabuffRUM                     0x102da9cb0 registerObserver + 184\n";

        List<ThreadStackDto> result = IosErrorStackParser.parseStackTrace(complexLog);
        assertEquals(2, result.size());
        assertEquals("0", result.get(0).getThreadName());
        assertEquals("1", result.get(1).getThreadName());
        assertTrue(result.get(0).getStackTrace().stream()
                .anyMatch(trace -> trace.getText().contains("CrashViewController")));
    }

    @Test
    public void testSystemVsNonSystemStackTrace() {
        String mixedLog = "Backtrace of Thread 0:\n" +
                "libsystem_kernel.dylib          0x209adca24 semaphore_wait_trap + 8\n" +
                "iOSTest                         0x102049f90 main + 140\n" +
                "DatabuffRUM                     0x102e33a7c monitor + 76\n";

        List<ThreadStackDto> result = IosErrorStackParser.parseStackTrace(mixedLog);
        assertEquals(1, result.size());
        assertEquals(3, result.get(0).getStackTrace().size());
        assertEquals("system", result.get(0).getStackTrace().get(0).getType());
        assertEquals("non-system", result.get(0).getStackTrace().get(1).getType());
    }

    private void assertBasicParsing(List<ThreadStackDto> result) {
        assertNotNull(result);
        assertEquals(2, result.size());

        ThreadStackDto thread1 = result.get(0);
        assertEquals("259", thread1.getThreadName());
        assertEquals(4, thread1.getStackTrace().size());
        assertEquals("system", thread1.getStackTrace().get(0).getType());

        ThreadStackDto thread2 = result.get(1);
        assertEquals("4867", thread2.getThreadName());
        assertEquals(3, thread2.getStackTrace().size());
        assertEquals("system", thread2.getStackTrace().get(0).getType());
    }

    @Test
    public void testSpecialStackTrace() {
        String specialLog = "Backtrace of Thread 0:\n" +
                "UIKitCore                       0x1ce60ca04 <redacted> + 144\n" +
                "CoreFoundation                  0x1cc171da4 + 164\n" +
                "libsystem_kernel.dylib          0x209adca24\n" +
                "iOSTest                         0x102049f90 -[ViewController crash]\n" +
                "\n" +
                "Backtrace of Thread 1:\n" +
                "CoreFoundation                  <redacted>\n" +
                "DatabuffRUM                     0x102e33a7c";

        List<ThreadStackDto> result = IosErrorStackParser.parseStackTrace(specialLog);
        assertEquals(2, result.size());

        // Verify Thread 0 parsing
        ThreadStackDto thread0 = result.get(0);
        assertEquals("0", thread0.getThreadName());
        assertEquals(4, thread0.getStackTrace().size());
        assertTrue(thread0.getStackTrace().get(0).getText().contains("<redacted>"));
        assertFalse(thread0.getStackTrace().get(1).getText().contains("method_name"));

        // Verify Thread 1 parsing
        ThreadStackDto thread1 = result.get(1);
        assertEquals("1", thread1.getThreadName());
        assertEquals(2, thread1.getStackTrace().size());
    }

    @Test
    public void testStackTraceWithoutOffset() {
        String logWithoutOffset = "Backtrace of Thread 1:\n" +
                "CoreFoundation                  <redacted>\n" +
                "DatabuffRUM                     0x102e33a7c\n" +
                "libsystem_kernel.dylib          0x209adca24 semaphore_wait_trap\n" +
                "UIKitCore                       0x1ce60ca04 <redacted> + 144\n";

        List<ThreadStackDto> result = IosErrorStackParser.parseStackTrace(logWithoutOffset);
        assertEquals(1, result.size());

        ThreadStackDto thread = result.get(0);
        assertEquals("1", thread.getThreadName());
        assertEquals(4, thread.getStackTrace().size());
        assertTrue(thread.getStackTrace().get(0).getText().contains("<redacted>"));
        assertTrue(thread.getStackTrace().get(1).getText().contains("0x102e33a7c"));
    }

}
