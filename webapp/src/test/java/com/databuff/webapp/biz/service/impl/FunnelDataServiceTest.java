package com.databuff.webapp.biz.service.impl;

import com.databuff.dao.mysql.BizObservabilityMapper;
import com.databuff.entity.BizScenario;
import com.databuff.webapp.biz.model.BizFunnelData;
import com.databuff.webapp.biz.model.BizFunnelRequest;
import com.databuff.webapp.biz.model.BizFunnelStepData;
import com.databuff.webapp.biz.model.TsdbMetrics;
import org.apache.commons.collections.CollectionUtils;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.*;
import java.util.stream.Collectors;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * Unit tests for the FunnelDataService class.
 * 使用 Mockito 模拟依赖 (Mapper, TsdbQueryService)。
 * 测试 Service 层的参数校验、逻辑编排、数据聚合和响应构建。
 * **注意:** 此测试假设 FunnelDataService 已重构为依赖 TsdbQueryService。
 */
@ExtendWith(MockitoExtension.class) // Integrate Mockito with JUnit 5
class FunnelDataServiceTest {

    @Mock
    private BizObservabilityMapper bizMapper; // Mock 数据库 Mapper

    @Mock
    private TsdbQueryService tsdbQueryService; // *** 修改点: Mock 新的依赖 ***

    @InjectMocks
    private FunnelDataService funnelDataService; // 要测试的服务

    private BizFunnelRequest validRequest;
    private String apiKey = "test-api-key";
    private String scenarioIdStr = "1";
    private Integer scenarioIdInt = 1;
    private String validScenarioGraphJson;
    private BizScenario mockBizScenario;
    private Map<String, String> timeRange;

    // 默认路径 (1->...->6->7) 涉及的 Event IDs
    private Set<String> defaultPathEventIds = new HashSet<>(Arrays.asList("ev1", "ev2a", "ev2b", "ev3", "ev4", "ev5", "ev6", "ev7"));
    // 备选路径 (1->...->5->7) 涉及的 Event IDs
    private Set<String> altPathEventIds = new HashSet<>(Arrays.asList("ev1", "ev2a", "ev2b", "ev3", "ev4", "ev5", "ev7"));


    @BeforeEach
    void setUp() {
        // --- 创建有效的请求对象 ---
        validRequest = new BizFunnelRequest();
        validRequest.setBizScenarioId(scenarioIdInt);
        validRequest.setFromTime("2025-01-01 10:00:00");
        validRequest.setToTime("2025-01-01 11:00:00");
        // leafNodeId 默认为 null，用于测试默认路径

        // --- 创建 timeRange Map ---
        timeRange = new HashMap<>();
        timeRange.put("start", validRequest.getFromTime());
        timeRange.put("end", validRequest.getToTime());

        // --- 创建有效的场景图配置 JSON ---
        validScenarioGraphJson = "{\n" +
                "  \"version\": 2,\n" +
                "  \"nodes\": [\n" +
                "    {\"id\": \"node1\", \"name\": \"登录验证\", \"events\": {\"included\": [{\"bizEventId\": \"ev1\"}]}},\n" +
                "    {\"id\": \"node2\", \"name\": \"交易申请\", \"events\": {\"included\": [{\"bizEventId\": \"ev2a\"}, {\"bizEventId\": \"ev2b\"}]}},\n" +
                "    {\"id\": \"node3\", \"name\": \"资格审核\", \"events\": {\"included\": [{\"bizEventId\": \"ev3\"}]}},\n" +
                "    {\"id\": \"node4\", \"name\": \"交易匹配\", \"events\": {\"included\": [{\"bizEventId\": \"ev4\"}]}},\n" +
                "    {\"id\": \"node5\", \"name\": \"结果公示\", \"events\": {\"included\": [{\"bizEventId\": \"ev5\"}]}},\n" +
                "    {\"id\": \"node6\", \"name\": \"异议处理\", \"events\": {\"included\": [{\"bizEventId\": \"ev6\"}]}},\n" +
                "    {\"id\": \"node7\", \"name\": \"结算确认\", \"events\": {\"included\": [{\"bizEventId\": \"ev7\"}]}}\n" +
                "  ],\n" +
                "  \"edges\": [\n" +
                "    {\"source\": \"node1\", \"target\": \"node2\"}, {\"source\": \"node2\", \"target\": \"node3\"},\n" +
                "    {\"source\": \"node3\", \"target\": \"node4\"}, {\"source\": \"node4\", \"target\": \"node5\"},\n" +
                "    {\"source\": \"node5\", \"target\": \"node7\"}, {\"source\": \"node5\", \"target\": \"node6\"},\n" + // 注意：这里定义了两个分支
                "    {\"source\": \"node6\", \"target\": \"node7\"}\n" + // node6 指向 node7
                "  ]\n" +
                "}";

        // Mock BizScenario 对象
        mockBizScenario = new BizScenario();
        mockBizScenario.setId(scenarioIdInt);
        mockBizScenario.setApiKey(apiKey);
        mockBizScenario.setScenarioGraph(validScenarioGraphJson);
        mockBizScenario.setVersion(2); // 确认是 V2 版本
    }

    /**
     * *** 修改点: 创建模拟的 TSDB 指标 Map ***
     *
     * @param expectedEventIds 查询时预期的 Event ID 集合
     * @return 模拟的 Map<String, TsdbMetrics>
     */
    private Map<String, TsdbMetrics> createMockTsdbMetricsMap(Set<String> expectedEventIds) {
        Map<String, TsdbMetrics> metricsMap = new HashMap<>();
        if (CollectionUtils.isEmpty(expectedEventIds)) {
            return metricsMap;
        }
        for (String eventId : expectedEventIds) {
            TsdbMetrics metrics = new TsdbMetrics();
            // 根据 eventId 生成模拟数据 (确定性)
            long total = 100 + Math.abs(eventId.hashCode() % 100); // 100-199
            long error = total / 10; // ~10%
            long slow = (total - error) / 5; // ~20% of non-error
            double duration = total * 100000.0;
            metrics.setTotalCnt(total);
            metrics.setErrorCnt(error);
            metrics.setTotalDuration(duration);
            metrics.setSlowCnt(slow);
            metricsMap.put(eventId, metrics);
        }
        return metricsMap;
    }


    @Test
    @DisplayName("测试获取漏斗数据 - 成功情况 (默认路径)")
    void testGetFunnelData_Success_DefaultPath() {
        // --- Arrange ---
        when(bizMapper.getBizScenarioById(eq(scenarioIdInt), eq(apiKey))).thenReturn(mockBizScenario);

        Set<String> expectedEventIds = defaultPathEventIds;
        Map<String, TsdbMetrics> mockMetricsMap = createMockTsdbMetricsMap(expectedEventIds);

        // *** 修改点: Mock tsdbQueryService ***
        when(tsdbQueryService.queryFunnelBaseMetrics(eq(apiKey), eq(expectedEventIds), eq(timeRange)))
                .thenReturn(mockMetricsMap);

        // --- Act ---
        BizFunnelData resultData = funnelDataService.getFunnelData(apiKey, validRequest);

        // --- Assert ---
        assertNotNull(resultData, "返回结果不应为 null");
        assertNotNull(resultData.getPathData(), "路径数据列表不应为 null");
        // 根据 validScenarioGraphJson，默认最深路径是 1->2->3->4->5->6->7
        assertEquals(7, resultData.getPathData().size(), "默认最深路径应包含 7 个节点");

        // 验证路径顺序和基本信息
        List<String> expectedPathIds = Arrays.asList("node1", "node2", "node3", "node4", "node5", "node6", "node7");
        for (int i = 0; i < expectedPathIds.size(); i++) {
            BizFunnelStepData step = resultData.getPathData().get(i);
            assertEquals(expectedPathIds.get(i), step.getNodeId(), "节点 ID 不匹配，索引: " + i);
            assertEquals(i, step.getDepth(), "节点深度不匹配，索引: " + i);
            assertNotNull(step.getBusinessPerformance(), "业务性能数据不应为 null，索引: " + i);
            assertNotNull(step.getFunnelStage(), "漏斗阶段数据不应为 null，索引: " + i);
            // applicationPerformance 列表可能为空，所以不强制非空
            // assertNotNull(step.getApplicationPerformance(), "应用性能数据不应为 null，索引: " + i);
        }

        // 验证聚合结果 (以 node2 为例)
        BizFunnelStepData node2Step = resultData.getPathData().get(1); // node2 在索引 1
        assertNotNull(node2Step.getFunnelStage());
        AggregatedNodeMetrics expectedNode2 = calculateExpectedMetrics(Arrays.asList("ev2a", "ev2b"), mockMetricsMap);
        assertEquals(expectedNode2.totalCnt, node2Step.getBusinessPerformance().getTotalRequests(), "Node2 总请求数聚合错误");
        assertEquals(expectedNode2.errorCnt, node2Step.getFunnelStage().getErrorRequests(), "Node2 错误请求数聚合错误");
        assertEquals(expectedNode2.slowCnt, node2Step.getFunnelStage().getSlowRequests(), "Node2 慢请求数聚合错误");
        assertEquals(Math.max(0, expectedNode2.totalCnt - expectedNode2.errorCnt - expectedNode2.slowCnt), node2Step.getFunnelStage().getNormalRequests(), "Node2 普通请求数聚合错误");


        // 验证分支信息 (在 node5 上)
        BizFunnelStepData node5Step = resultData.getPathData().get(4); // node5 在索引 4
        assertTrue(node5Step.isSwitchPossible(), "Node5 应可以切换分支");
        assertNotNull(node5Step.getAlternativeNextSteps());
        // 因为默认路径走了 node6，所以备选项应该是 node7
        assertEquals(1, node5Step.getAlternativeNextSteps().size(), "Node5 应有 1 个备选分支");
        assertEquals("node7", node5Step.getAlternativeNextSteps().get(0).getOptionNodeId(), "Node5 的备选分支应是 node7");
        assertEquals("结算确认", node5Step.getAlternativeNextSteps().get(0).getOptionNodeName()); // 假设 node7 名称是 "结算确认"

        // 验证叶子节点 node7 无分支
        BizFunnelStepData node7Step = resultData.getPathData().get(6);
        assertFalse(node7Step.isSwitchPossible());
        assertNotNull(node7Step.getAlternativeNextSteps());
        assertTrue(node7Step.getAlternativeNextSteps().isEmpty());

        // *** 修改点: 验证 Mock 调用 ***
        verify(bizMapper, times(1)).getBizScenarioById(eq(scenarioIdInt), eq(apiKey));
        verify(tsdbQueryService, times(1)).queryFunnelBaseMetrics(eq(apiKey), eq(expectedEventIds), eq(timeRange));
        verifyNoMoreInteractions(bizMapper, tsdbQueryService); // 确认没有其他多余交互
    }

    @Test
    @DisplayName("测试获取漏斗数据 - 成功情况 (指定叶子节点 - 模拟切换)")
    void testGetFunnelData_Success_SpecificLeaf() {
        // --- Arrange ---
        validRequest.setLeafNodeId("node7"); // 用户点击了 node5 上的 "结算确认" 选项, 请求路径 1->...->5->7

        when(bizMapper.getBizScenarioById(eq(scenarioIdInt), eq(apiKey))).thenReturn(mockBizScenario);

        Set<String> expectedEventIds = altPathEventIds; // 这条路径涉及的 event ids
        Map<String, TsdbMetrics> mockMetricsMap = createMockTsdbMetricsMap(expectedEventIds);

        // *** 修改点: Mock tsdbQueryService ***
        when(tsdbQueryService.queryFunnelBaseMetrics(eq(apiKey), eq(expectedEventIds), eq(timeRange)))
                .thenReturn(mockMetricsMap);

        // --- Act ---
        BizFunnelData resultData = funnelDataService.getFunnelData(apiKey, validRequest);

        // --- Assert ---
        assertNotNull(resultData);
        assertNotNull(resultData.getPathData());
        // 路径 1->2->3->4->5->7 应包含 6 个节点
        assertEquals(6, resultData.getPathData().size(), "路径 1->...->5->7 应包含 6 个节点");

        List<String> expectedPathIds = Arrays.asList("node1", "node2", "node3", "node4", "node5", "node7");
        List<String> actualPathIds = resultData.getPathData().stream().map(BizFunnelStepData::getNodeId).collect(Collectors.toList());
        assertEquals(expectedPathIds, actualPathIds, "路径节点顺序应为 1->...->5->7");

        // 验证分支信息 (在 node5 上)
        BizFunnelStepData node5Step = resultData.getPathData().get(4); // node5 在索引 4
        assertTrue(node5Step.isSwitchPossible());
        assertNotNull(node5Step.getAlternativeNextSteps());
        // 因为当前路径走了 node7，备选项应该是 node6
        assertEquals(1, node5Step.getAlternativeNextSteps().size());
        assertEquals("node6", node5Step.getAlternativeNextSteps().get(0).getOptionNodeId());
        assertEquals("异议处理", node5Step.getAlternativeNextSteps().get(0).getOptionNodeName()); // 假设 node6 名称是 "异议处理"

        // *** 修改点: 验证 Mock 调用 ***
        verify(bizMapper, times(1)).getBizScenarioById(eq(scenarioIdInt), eq(apiKey));
        verify(tsdbQueryService, times(1)).queryFunnelBaseMetrics(eq(apiKey), eq(expectedEventIds), eq(timeRange));
        verifyNoMoreInteractions(bizMapper, tsdbQueryService);
    }


    @Test
    @DisplayName("测试获取漏斗数据 - 输入参数无效")
    void testGetFunnelData_InvalidInput() {
        // 测试 apiKey 为空
        assertThrows(IllegalArgumentException.class, () -> {
            funnelDataService.getFunnelData(null, validRequest);
        }, "ApiKey 为空时应抛出 IllegalArgumentException");

        // 测试 request 为空
        assertThrows(IllegalArgumentException.class, () -> {
            funnelDataService.getFunnelData(apiKey, null);
        }, "Request 为空时应抛出 IllegalArgumentException");

        // 测试 startTime 为空
        validRequest.setFromTime(null);
        assertThrows(IllegalArgumentException.class, () -> {
            funnelDataService.getFunnelData(apiKey, validRequest);
        }, "StartTime 为空时应抛出 IllegalArgumentException");

        // 测试 endTime 为空
        validRequest.setFromTime("2025-01-01 10:00:00"); // Reset start time
        validRequest.setToTime(null);
        assertThrows(IllegalArgumentException.class, () -> {
            funnelDataService.getFunnelData(apiKey, validRequest);
        }, "EndTime 为空时应抛出 IllegalArgumentException");

        // 确认依赖未被调用
        verifyNoInteractions(bizMapper, tsdbQueryService);
    }

    @Test
    @DisplayName("测试获取漏斗数据 - 场景配置加载失败 - 未找到")
    void testGetFunnelData_ScenarioNotFound() {
        // --- Arrange ---
        when(bizMapper.getBizScenarioById(eq(scenarioIdInt), eq(apiKey))).thenReturn(null);

        // --- Act & Assert ---
        RuntimeException exception = assertThrows(RuntimeException.class, () -> {
            funnelDataService.getFunnelData(apiKey, validRequest);
        });
        assertTrue(exception.getMessage().contains("找不到指定的业务场景"), "异常消息应包含场景未找到信息");

        // 确认依赖调用情况
        verify(bizMapper, times(1)).getBizScenarioById(eq(scenarioIdInt), eq(apiKey));
        verifyNoInteractions(tsdbQueryService); // *** 修改点: 验证 tsdbQueryService 未交互 ***
    }

    @Test
    @DisplayName("测试获取漏斗数据 - 场景配置加载失败 - Graph JSON 为空")
    void testGetFunnelData_EmptyGraphJson() {
        // --- Arrange ---
        mockBizScenario.setScenarioGraph(""); // 设置为空字符串
        when(bizMapper.getBizScenarioById(eq(scenarioIdInt), eq(apiKey))).thenReturn(mockBizScenario);

        // --- Act & Assert ---
        RuntimeException exception = assertThrows(RuntimeException.class, () -> {
            funnelDataService.getFunnelData(apiKey, validRequest);
        });
        assertTrue(exception.getMessage().contains("未配置场景地图"), "异常消息应包含未配置地图信息");

        verify(bizMapper, times(1)).getBizScenarioById(eq(scenarioIdInt), eq(apiKey));
        verifyNoInteractions(tsdbQueryService);
    }

    @Test
    @DisplayName("测试获取漏斗数据 - 场景配置加载失败 - Graph JSON 无效")
    void testGetFunnelData_InvalidGraphJson() {
        // --- Arrange ---
        mockBizScenario.setScenarioGraph("this is not json");
        when(bizMapper.getBizScenarioById(eq(scenarioIdInt), eq(apiKey))).thenReturn(mockBizScenario);

        // --- Act & Assert ---
        RuntimeException exception = assertThrows(RuntimeException.class, () -> {
            funnelDataService.getFunnelData(apiKey, validRequest);
        });
        // FastJSON 可能会抛出 JSONException，FunnelDataService 应该捕获并包装成 RuntimeException
        assertTrue(exception.getMessage().contains("场景地图配置 JSON 格式错误"), "异常消息应包含 JSON 格式错误信息");

        verify(bizMapper, times(1)).getBizScenarioById(eq(scenarioIdInt), eq(apiKey));
        verifyNoInteractions(tsdbQueryService);
    }

    @Test
    @DisplayName("测试获取漏斗数据 - 场景配置加载失败 - Graph 缺少节点")
    void testGetFunnelData_GraphMissingNodes() {
        // --- Arrange ---
        String jsonNoNodes = "{\"version\": 2, \"nodes\": [], \"edges\": [{\"source\": \"a\", \"target\": \"b\"}]}";
        mockBizScenario.setScenarioGraph(jsonNoNodes);
        when(bizMapper.getBizScenarioById(eq(scenarioIdInt), eq(apiKey))).thenReturn(mockBizScenario);

        // --- Act & Assert ---
        RuntimeException exception = assertThrows(RuntimeException.class, () -> {
            funnelDataService.getFunnelData(apiKey, validRequest);
        });
        assertTrue(exception.getMessage().contains("节点列表不能为空"), "异常消息应包含节点列表为空信息");

        verify(bizMapper, times(1)).getBizScenarioById(eq(scenarioIdInt), eq(apiKey));
        verifyNoInteractions(tsdbQueryService);
    }

    @Test
    @DisplayName("测试获取漏斗数据 - 场景配置加载失败 - Graph 无根节点")
    void testGetFunnelData_InvalidGraphStructure() {
        // --- Arrange ---
        String jsonNoRoot = "{\n" +
                "  \"version\": 2,\n" +
                "  \"nodes\": [\n" +
                "    {\"id\": \"node1\", \"name\": \"N1\", \"events\": {\"included\": []}},\n" +
                "    {\"id\": \"node2\", \"name\": \"N2\", \"events\": {\"included\": []}}\n" +
                "  ],\n" +
                "  \"edges\": [\n" +
                "    {\"source\": \"node1\", \"target\": \"node2\"},\n" +
                "    {\"source\": \"node2\", \"target\": \"node1\"}\n" + // Cycle makes no root
                "  ]\n" +
                "}";
        mockBizScenario.setScenarioGraph(jsonNoRoot);
        when(bizMapper.getBizScenarioById(eq(scenarioIdInt), eq(apiKey))).thenReturn(mockBizScenario);

        // --- Act & Assert ---
        RuntimeException exception = assertThrows(RuntimeException.class, () -> {
            funnelDataService.getFunnelData(apiKey, validRequest);
        });
        assertTrue(exception.getMessage().contains("无法找到根节点"), "异常消息应包含无法找到根节点信息");

        verify(bizMapper, times(1)).getBizScenarioById(eq(scenarioIdInt), eq(apiKey));
        verifyNoInteractions(tsdbQueryService);
    }


    @Test
    @DisplayName("测试获取漏斗数据 - 路径重建失败 (无效叶子节点)")
    void testGetFunnelData_PathReconstructionFail() {
        // --- Arrange ---
        validRequest.setLeafNodeId("non-existent-node");
        when(bizMapper.getBizScenarioById(eq(scenarioIdInt), eq(apiKey))).thenReturn(mockBizScenario); // Config is valid

        // --- Act & Assert ---
        RuntimeException exception = assertThrows(RuntimeException.class, () -> {
            funnelDataService.getFunnelData(apiKey, validRequest);
        });
        assertTrue(exception.getMessage().contains("无法为目标节点 'non-existent-node' 重建有效路径"), "异常消息不正确");

        verify(bizMapper, times(1)).getBizScenarioById(eq(scenarioIdInt), eq(apiKey));
        verifyNoInteractions(tsdbQueryService); // 查询 TSDB 前就失败了
    }

    @Test
    @DisplayName("测试获取漏斗数据 - TSDB 查询无结果")
    void testGetFunnelData_TsdbNoResults() {
        // --- Arrange ---
        when(bizMapper.getBizScenarioById(eq(scenarioIdInt), eq(apiKey))).thenReturn(mockBizScenario);

        Set<String> expectedEventIds = defaultPathEventIds;
        // *** 修改点: Mock tsdbQueryService 返回空 Map ***
        when(tsdbQueryService.queryFunnelBaseMetrics(eq(apiKey), eq(expectedEventIds), eq(timeRange)))
                .thenReturn(Collections.emptyMap());

        // --- Act ---
        BizFunnelData resultData = funnelDataService.getFunnelData(apiKey, validRequest);

        // --- Assert ---
        assertNotNull(resultData);
        assertNotNull(resultData.getPathData());
        assertEquals(7, resultData.getPathData().size()); // 路径仍然是 7 个节点
        // 检查任意节点的指标是否为 0
        BizFunnelStepData firstStep = resultData.getPathData().get(0);
        assertEquals(0, firstStep.getBusinessPerformance().getTotalRequests());
        assertEquals(0, firstStep.getFunnelStage().getNormalRequests());
        assertEquals(0, firstStep.getFunnelStage().getSlowRequests());
        assertEquals(0, firstStep.getFunnelStage().getErrorRequests());

        // *** 修改点: 验证 Mock 调用 ***
        verify(bizMapper, times(1)).getBizScenarioById(eq(scenarioIdInt), eq(apiKey));
        verify(tsdbQueryService, times(1)).queryFunnelBaseMetrics(eq(apiKey), eq(expectedEventIds), eq(timeRange));
    }

    @Test
    @DisplayName("测试获取漏斗数据 - TSDB 查询抛出异常")
    void testGetFunnelData_TsdbException() {
        // --- Arrange ---
        when(bizMapper.getBizScenarioById(eq(scenarioIdInt), eq(apiKey))).thenReturn(mockBizScenario);

        // *** 修改点: Mock tsdbQueryService 抛出异常 ***
        when(tsdbQueryService.queryFunnelBaseMetrics(anyString(), anySet(), anyMap()))
                .thenThrow(new RuntimeException("TSDB connection failed"));

        // --- Act & Assert ---
        RuntimeException exception = assertThrows(RuntimeException.class, () -> {
            funnelDataService.getFunnelData(apiKey, validRequest);
        });
        // 异常应该被 FunnelDataService 捕获并包装
        assertTrue(exception.getMessage().contains("处理漏斗数据时发生未预期错误"), "异常消息不正确");
        assertNotNull(exception.getCause(), "包装后的异常应包含原始原因");
        assertEquals("TSDB connection failed", exception.getCause().getMessage(), "原始异常消息不匹配");


        // *** 修改点: 验证 Mock 调用 ***
        verify(bizMapper, times(1)).getBizScenarioById(eq(scenarioIdInt), eq(apiKey));
        verify(tsdbQueryService, times(1)).queryFunnelBaseMetrics(anyString(), anySet(), anyMap());
    }

    @Test
    @DisplayName("测试获取漏斗数据 - TSDB 查询返回部分结果")
    void testGetFunnelData_TsdbPartialResults() {
        // --- Arrange ---
        when(bizMapper.getBizScenarioById(eq(scenarioIdInt), eq(apiKey))).thenReturn(mockBizScenario);

        Set<String> expectedEventIdsOnPath = defaultPathEventIds;
        // 模拟 TSDB 只返回部分 Event ID 的数据 (例如缺少 ev3, ev6)
        Set<String> returnedEventIds = new HashSet<>(Arrays.asList("ev1", "ev2a", "ev2b", "ev4", "ev5", "ev7"));
        Map<String, TsdbMetrics> partialMetricsMap = createMockTsdbMetricsMap(returnedEventIds);

        // *** 修改点: Mock tsdbQueryService 返回部分 Map ***
        when(tsdbQueryService.queryFunnelBaseMetrics(eq(apiKey), eq(expectedEventIdsOnPath), eq(timeRange)))
                .thenReturn(partialMetricsMap);

        // --- Act ---
        BizFunnelData resultData = funnelDataService.getFunnelData(apiKey, validRequest); // 请求默认路径

        // --- Assert ---
        assertNotNull(resultData);
        assertEquals(7, resultData.getPathData().size()); // 路径仍然是 7 个节点

        // 验证 node3 (依赖 ev3) 的指标是否为 0
        BizFunnelStepData node3Step = resultData.getPathData().get(2);
        assertEquals(0, node3Step.getBusinessPerformance().getTotalRequests());
        assertEquals(0, node3Step.getFunnelStage().getNormalRequests());
        assertEquals(0, node3Step.getFunnelStage().getSlowRequests());
        assertEquals(0, node3Step.getFunnelStage().getErrorRequests());

        // 验证 node6 (依赖 ev6) 的指标是否为 0
        BizFunnelStepData node6Step = resultData.getPathData().get(5);
        assertEquals(0, node6Step.getBusinessPerformance().getTotalRequests());

        // 验证 node7 (依赖 ev7) 的指标是否 *不* 为 0 (因为它有关联且返回了数据)
        BizFunnelStepData node7Step = resultData.getPathData().get(6);
        AggregatedNodeMetrics expectedNode7 = calculateExpectedMetrics(Arrays.asList("ev7"), partialMetricsMap);
        assertEquals(expectedNode7.totalCnt, node7Step.getBusinessPerformance().getTotalRequests());
        assertTrue(node7Step.getBusinessPerformance().getTotalRequests() > 0, "Node7 应有数据");

        // *** 修改点: 验证 Mock 调用 ***
        verify(bizMapper, times(1)).getBizScenarioById(eq(scenarioIdInt), eq(apiKey));
        verify(tsdbQueryService, times(1)).queryFunnelBaseMetrics(eq(apiKey), eq(expectedEventIdsOnPath), eq(timeRange));
    }

    @Test
    @DisplayName("测试获取漏斗数据 - 成功情况 (默认路径, Java 8, 详细断言)")
    void testGetFunnelData_Success_DefaultPath_Detailed_Java8() {
        // --- Arrange ---
        when(bizMapper.getBizScenarioById(eq(scenarioIdInt), eq(apiKey))).thenReturn(mockBizScenario);

        Set<String> expectedEventIds = defaultPathEventIds;
        Map<String, TsdbMetrics> mockMetricsMap = createMockTsdbMetricsMap(expectedEventIds);
        when(tsdbQueryService.queryFunnelBaseMetrics(eq(apiKey), eq(expectedEventIds), eq(timeRange)))
                .thenReturn(mockMetricsMap);

        // --- Act ---
        BizFunnelData resultData = funnelDataService.getFunnelData(apiKey, validRequest);

        // --- Assert ---
        assertNotNull(resultData);
        assertNotNull(resultData.getPathData());
        assertEquals(7, resultData.getPathData().size(), "默认最深路径应包含 7 个节点");

        // 验证路径顺序
        List<String> expectedPathIds = Arrays.asList("node1", "node2", "node3", "node4", "node5", "node6", "node7");
        for (int i = 0; i < expectedPathIds.size(); i++) {
            assertEquals(expectedPathIds.get(i), resultData.getPathData().get(i).getNodeId());
            assertEquals(i, resultData.getPathData().get(i).getDepth());
        }

        // 详细验证聚合结果 (node1, node2, node6, node7)
        BizFunnelStepData node1Step = resultData.getPathData().get(0);
        AggregatedNodeMetrics expectedNode1 = calculateExpectedMetrics(Arrays.asList("ev1"), mockMetricsMap);
        assertEquals(expectedNode1.totalCnt, node1Step.getBusinessPerformance().getTotalRequests());
        assertEquals(expectedNode1.errorCnt, node1Step.getFunnelStage().getErrorRequests());
        assertEquals(expectedNode1.slowCnt, node1Step.getFunnelStage().getSlowRequests());
        assertEquals(Math.max(0, expectedNode1.totalCnt - expectedNode1.errorCnt - expectedNode1.slowCnt), node1Step.getFunnelStage().getNormalRequests());

        BizFunnelStepData node2Step = resultData.getPathData().get(1);
        AggregatedNodeMetrics expectedNode2 = calculateExpectedMetrics(Arrays.asList("ev2a", "ev2b"), mockMetricsMap);
        assertEquals(expectedNode2.totalCnt, node2Step.getBusinessPerformance().getTotalRequests());
        assertEquals(expectedNode2.errorCnt, node2Step.getFunnelStage().getErrorRequests());
        assertEquals(expectedNode2.slowCnt, node2Step.getFunnelStage().getSlowRequests());
        assertEquals(Math.max(0, expectedNode2.totalCnt - expectedNode2.errorCnt - expectedNode2.slowCnt), node2Step.getFunnelStage().getNormalRequests());

        BizFunnelStepData node6Step = resultData.getPathData().get(5);
        AggregatedNodeMetrics expectedNode6 = calculateExpectedMetrics(Arrays.asList("ev6"), mockMetricsMap);
        assertEquals(expectedNode6.totalCnt, node6Step.getBusinessPerformance().getTotalRequests());
        assertEquals(expectedNode6.errorCnt, node6Step.getFunnelStage().getErrorRequests());

        BizFunnelStepData node7Step = resultData.getPathData().get(6);
        AggregatedNodeMetrics expectedNode7 = calculateExpectedMetrics(Arrays.asList("ev7"), mockMetricsMap);
        assertEquals(expectedNode7.totalCnt, node7Step.getBusinessPerformance().getTotalRequests());
        assertEquals(expectedNode7.errorCnt, node7Step.getFunnelStage().getErrorRequests());

        // 详细验证分支信息 (在 node5 上)
        BizFunnelStepData node5Step = resultData.getPathData().get(4);
        assertTrue(node5Step.isSwitchPossible(), "Node5 应可以切换分支");
        assertNotNull(node5Step.getAlternativeNextSteps());
        assertEquals(1, node5Step.getAlternativeNextSteps().size(), "Node5 应有 1 个备选分支");
        assertEquals("node7", node5Step.getAlternativeNextSteps().get(0).getOptionNodeId(), "Node5 的备选分支应是 node7 (因为默认走了node6)");
        assertEquals("结算确认", node5Step.getAlternativeNextSteps().get(0).getOptionNodeName());

        // 验证叶子节点 node7 无分支
        assertFalse(node7Step.isSwitchPossible());
        assertNotNull(node7Step.getAlternativeNextSteps());
        assertTrue(node7Step.getAlternativeNextSteps().isEmpty());

        // 验证 Mock 调用
        verify(bizMapper, times(1)).getBizScenarioById(eq(scenarioIdInt), eq(apiKey));
        verify(tsdbQueryService, times(1)).queryFunnelBaseMetrics(eq(apiKey), eq(expectedEventIds), eq(timeRange));
    }

    @Test
    @DisplayName("测试获取漏斗数据 - 成功情况 (指定路径, Java 8, 详细断言)")
    void testGetFunnelData_Success_SpecificLeaf_Detailed_Java8() {
        // --- Arrange ---
        validRequest.setLeafNodeId("node7"); // 请求 1->...->5->7 路径

        when(bizMapper.getBizScenarioById(eq(scenarioIdInt), eq(apiKey))).thenReturn(mockBizScenario);

        Set<String> expectedEventIds = altPathEventIds;
        Map<String, TsdbMetrics> mockMetricsMap = createMockTsdbMetricsMap(expectedEventIds);
        when(tsdbQueryService.queryFunnelBaseMetrics(eq(apiKey), eq(expectedEventIds), eq(timeRange)))
                .thenReturn(mockMetricsMap);

        // --- Act ---
        BizFunnelData resultData = funnelDataService.getFunnelData(apiKey, validRequest);

        // --- Assert ---
        assertNotNull(resultData);
        assertEquals(6, resultData.getPathData().size());

        // 验证路径顺序
        List<String> expectedPathIds = Arrays.asList("node1", "node2", "node3", "node4", "node5", "node7");
        List<String> actualPathIds = resultData.getPathData().stream().map(BizFunnelStepData::getNodeId).collect(Collectors.toList());
        assertEquals(expectedPathIds, actualPathIds);

        // 详细验证聚合结果 (node5, node7)
        BizFunnelStepData node5StepAssert = resultData.getPathData().get(4);
        AggregatedNodeMetrics expectedNode5 = calculateExpectedMetrics(Arrays.asList("ev5"), mockMetricsMap);
        assertEquals(expectedNode5.totalCnt, node5StepAssert.getBusinessPerformance().getTotalRequests());
        assertEquals(expectedNode5.errorCnt, node5StepAssert.getFunnelStage().getErrorRequests());

        BizFunnelStepData node7StepAssert = resultData.getPathData().get(5);
        AggregatedNodeMetrics expectedNode7Alt = calculateExpectedMetrics(Arrays.asList("ev7"), mockMetricsMap);
        assertEquals(expectedNode7Alt.totalCnt, node7StepAssert.getBusinessPerformance().getTotalRequests());
        assertEquals(expectedNode7Alt.errorCnt, node7StepAssert.getFunnelStage().getErrorRequests());

        // 详细验证分支信息 (在 node5 上)
        assertTrue(node5StepAssert.isSwitchPossible());
        assertNotNull(node5StepAssert.getAlternativeNextSteps());
        assertEquals(1, node5StepAssert.getAlternativeNextSteps().size());
        assertEquals("node6", node5StepAssert.getAlternativeNextSteps().get(0).getOptionNodeId()); // 备选项是 node6
        assertEquals("异议处理", node5StepAssert.getAlternativeNextSteps().get(0).getOptionNodeName());

        // 验证叶子节点 node7 无分支
        assertFalse(node7StepAssert.isSwitchPossible());
        assertNotNull(node7StepAssert.getAlternativeNextSteps());
        assertTrue(node7StepAssert.getAlternativeNextSteps().isEmpty());

        // 验证 Mock 调用
        verify(bizMapper, times(1)).getBizScenarioById(eq(scenarioIdInt), eq(apiKey));
        verify(tsdbQueryService, times(1)).queryFunnelBaseMetrics(eq(apiKey), eq(expectedEventIds), eq(timeRange));

    }


    /**
     * *** 修改点: 辅助方法，计算预期聚合指标 (用于断言) ***
     *
     * @param eventIds    节点关联的 Event IDs
     * @param tsdbResults 模拟的 TSDB 指标 Map
     * @return 聚合后的指标
     */
    private AggregatedNodeMetrics calculateExpectedMetrics(List<String> eventIds, Map<String, TsdbMetrics> tsdbResults) {
        AggregatedNodeMetrics expected = new AggregatedNodeMetrics();
        if (eventIds != null && tsdbResults != null) { // 添加 tsdbResults 非空检查
            for (String eventId : eventIds) {
                TsdbMetrics m = tsdbResults.get(eventId);
                if (m != null) {
                    expected.totalCnt += m.getTotalCnt() != null ? m.getTotalCnt() : 0;
                    expected.errorCnt += m.getErrorCnt() != null ? m.getErrorCnt() : 0;
                    expected.slowCnt += m.getSlowCnt() != null ? m.getSlowCnt() : 0;
                    expected.totalDuration += m.getTotalDuration() != null ? m.getTotalDuration() : 0.0;
                }
            }
        }
        return expected;
    }

    // --- 内部辅助类，用于聚合指标 ---
    private static class AggregatedNodeMetrics {
        long totalCnt = 0;
        long errorCnt = 0;
        long slowCnt = 0;
        double totalDuration = 0.0;
    }

}