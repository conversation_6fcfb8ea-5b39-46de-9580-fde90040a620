package com.databuff.dts.ringbuffer;

import com.alibaba.fastjson.JSONObject;
/**
 * Disruptor 事件对象，用于在 RingBuffer 中传递需要进行业务处理的 Span 数据。
 * 修改为持有 JSONObject 以优化 TraceReceiver 性能。
 */
public class SpanEvent {
    private JSONObject spanJson; //

    /**
     * 设置事件数据。
     * @param spanJson 从 TraceReceiver 传来的 Span JSON 对象。
     */
    public void set(JSONObject spanJson) {
        this.spanJson = spanJson;
    }

    /**
     * 获取事件数据。
     * @return Span JSON 对象。
     */
    public JSONObject getSpanJson() {
        return spanJson;
    }

    /**
     * 用于 Disruptor 的 EventFactory 重用 Event 对象时清理状态。
     */
    public void clear() {
        this.spanJson = null;
    }
}