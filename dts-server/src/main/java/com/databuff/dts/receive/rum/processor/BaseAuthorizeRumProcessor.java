package com.databuff.dts.receive.rum.processor;

import com.databuff.dts.receive.rum.processor.auth.RumAuthService;
import com.databuff.entity.rum.Attribute;
import com.databuff.entity.rum.ResourceSpan;
import com.databuff.entity.rum.Span;
import com.databuff.entity.rum.mysql.RumAppSettings;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 用来统一app授权
 * <AUTHOR>
 * @date 2025/01/19
 */
@Component
public abstract class BaseAuthorizeRumProcessor {

    @Autowired
    protected RumAuthService rumAuthService;

    protected boolean authorizeDevice(ResourceSpan resourceSpan, RumAppSettings appSettings) {
        // 1) get device.uuid
        String deviceUuid = null;
        List<Attribute> resAttrs = resourceSpan.getResource().getAttributes();
        for (Attribute a : resAttrs) {
            if ("device.uuid".equals(a.getKey())) {
                deviceUuid = a.getStringValue();
                break;
            }
        }
        if (deviceUuid == null) {
            // 没有uuid => 视为不可授权 => 直接丢
            return true;
        }

        // 2) doAuthorize
        boolean authorized = rumAuthService.authorizeDevice(appSettings.getId(), deviceUuid);
        // 直接丢整个 trace
        return !authorized;
    }

    protected boolean authorizePage(RumAppSettings appSettings, Span span) {
        // 1) 在 span.attributes 里拿 page_id
        //    如果拿不到 => 直接丢弃
        String pageId = findPageId(span.getAttributes());
        if (pageId == null) {
            return true;
        }

        // 2) 调用 doAuthorize => 传 (appSettings.getId(), pageId, "page")
        boolean authorized = rumAuthService.authorizePage(appSettings.getId(), pageId);
        if (!authorized) {
            // 授权用尽 => 丢弃该 span
            return true;
        }
        return false;
    }

    private String findPageId(List<Attribute> attributes) {
        if (attributes == null || attributes.isEmpty()) {
            return null;
        }

        for (Attribute attribute : attributes) {
            if ("page_id".equals(attribute.getKey())) {
                return attribute.getStringValue();
            }
        }
        return null;
    }
}
