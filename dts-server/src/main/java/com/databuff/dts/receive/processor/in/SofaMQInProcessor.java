package com.databuff.dts.receive.processor.in;

import com.databuff.dts.config.RefreshScopeConfig;
import com.databuff.service.ServiceSyncService;

import static com.databuff.common.constants.Constant.Trace.*;

public class SofaMQInProcessor extends MQInProcessor {

    public SofaMQInProcessor(ServiceSyncService serviceSyncService, RefreshScopeConfig refreshScopeConfig) {
        super(serviceSyncService, TRACE_NAME_IN_SOFAMQ_CONSUME, TRACE_TOPIC, TRACE_BROKER_NAME, TRACE_COMPONENT_SOFAMQ, refreshScopeConfig);
    }
}
