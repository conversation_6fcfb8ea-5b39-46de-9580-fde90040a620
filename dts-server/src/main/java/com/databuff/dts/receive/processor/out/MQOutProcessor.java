package com.databuff.dts.receive.processor.out;

import com.alibaba.fastjson.JSONObject;
import com.databuff.dts.config.RefreshScopeConfig;
import com.databuff.dts.receive.model.SlowConfig;
import com.databuff.dts.receive.service.ComponentInstanceService;
import com.databuff.service.JedisService;
import com.databuff.service.ServiceSyncService;

import static com.databuff.common.constants.Constant.DEFAULT_BROKER_NAME;
import static com.databuff.common.constants.Constant.Trace.*;
import static com.databuff.dts.receive.processor.in.RabbitMQInProcessor.GEN_QUEUE;

public class MQOutProcessor extends OutProcessor {

    private String mqProducerName;
    private String topicKey;
    private String brokerKey;
    private String type;

    public MQOutProcessor(ServiceSyncService serviceSyncService, ComponentInstanceService componentInstanceService, JedisService jedisService,
                          String mqProducerName, String topicKey, String brokerKey, String type, RefreshScopeConfig refreshScopeConfig) {
        super(serviceSyncService, componentInstanceService, jedisService, refreshScopeConfig);
        this.mqProducerName = mqProducerName;
        this.topicKey = topicKey;
        this.brokerKey = brokerKey;
        this.type = type;
    }

    @Override
    public boolean match(String name) {
        return mqProducerName.equals(name);
    }

    @Override
    public boolean process(JSONObject s, String name, String k8sClusterId) {
        JSONObject meta = s.getJSONObject(META);
        String topic = meta.getString(topicKey);
        String brokerName = meta.getString(brokerKey);
        if (topic == null) {
            return false;
        }
        if (TRACE_NAME_OUT_RABBITMQ_PRODUCE.equals(mqProducerName)) {
            if (topic.startsWith(GEN_QUEUE)) {
                topic = GEN_QUEUE;
                meta.put(topicKey, topic);
            }
        }
        if (brokerName == null) {
            brokerName = DEFAULT_BROKER_NAME;
        }
        String service ="["+type + "]"+topic;
        String serviceInstance = brokerName;
        Integer partition = meta.getInteger("partition");
        if (partition != null) {
            JSONObject metrics = s.getJSONObject("metrics");
            if (metrics != null) {
                metrics.put("partition", partition);
            }else {
                metrics = new JSONObject();
                metrics.put("partition", partition);
                s.put("metrics", metrics);
            }
        }

        fillSlow(s);
        return initComponentService(s, meta, service, serviceInstance, SERVICE_TYPE_MQ, type);
    }

    private void fillSlow(JSONObject s) {
        String name = s.getString(NAME);
        long duration = s.getLong(DURATION);
        if (null == name) {
            return;
        }
        if (TRACE_OUT_NAMES_MQ.contains(name)) {
            s.put("slowTime", refreshScopeConfig.getSlowMq());
            if (duration > refreshScopeConfig.getSlowMq() * 1000 * 1000) {
                s.put("slow", 1);
            } else {
                s.put("slow", 0);
            }
        }
    }

}
