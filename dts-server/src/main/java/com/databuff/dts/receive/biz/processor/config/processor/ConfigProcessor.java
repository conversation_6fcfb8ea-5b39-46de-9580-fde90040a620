package com.databuff.dts.receive.biz.processor.config.processor; // 建议放在新的 processor 子包下

import com.databuff.dts.receive.biz.processor.config.dto.ConfigSourceData;
import com.databuff.dts.receive.biz.processor.config.dto.ProcessedConfigData;

/**
 * 配置处理器接口。
 * <p>
 * 定义了处理从数据源获取的原始配置数据，并将其转换/编译为
 * 可用于运行时缓存的结构化数据的契约。
 * </p>
 * <p>
 * 实现类负责遍历原始事件和场景列表，解析 JSON 字段（如 bizReqs, exceptionRules, scenarioGraph, kpiConfig），
 * 调用 {@link com.databuff.dts.receive.biz.processor.config.compiler.ConditionCompiler} 编译条件，
 * 构建所有最终用于缓存的 Map（如事件规则索引、异常规则、场景图、KPI 配置、事件-场景映射）以及收集必需字段集合。
 * </p>
 */
public interface ConfigProcessor {

    /**
     * 处理从数据源获取的原始配置数据，生成包含所有新构建缓存内容的结果对象。
     * <p>
     * 此方法应是幂等的，对于相同的输入应产生相同的输出。
     * 实现中应处理各种解析错误和无效配置，记录日志并跳过有问题部分，尽量完成处理。
     * </p>
     *
     * @param sourceData 包含活跃事件和场景列表的 {@link ConfigSourceData} 对象。
     * @return 一个 {@link ProcessedConfigData} 对象，包含了所有新构建的、准备用于替换当前缓存的 Map 和 Set，
     * 以及处理过程中的统计信息（如成功处理的事件/场景数量）。
     * @throws RuntimeException 如果在处理过程中发生不可恢复的严重错误。
     */
    ProcessedConfigData processConfigs(ConfigSourceData sourceData);

}