package com.databuff.dts.receive.rum.processor;

import com.databuff.common.utils.StringUtil;
import com.databuff.dts.receive.rum.processor.enhancers.RegionEnhancer;
import com.databuff.entity.rum.*;
import com.databuff.entity.rum.mysql.RumAppSettings;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
@Slf4j
public class AndroidRumDataProcessor extends BaseAuthorizeRumProcessor {

    @Autowired
    private RegionEnhancer regionEnhancer;

    public void enhanceAndroidResource(Resource resource) {
        if (resource == null || resource.getAttributes() == null) return;

        List<Attribute> attributes = resource.getAttributes();
        String osName = null;
        String osVersion = null;
        String deviceIdentifier = null;
        String deviceVendor = null;

        for (Attribute attr : attributes) {
            switch (attr.getKey()) {
                case "os.name":
                    osName = attr.getStringValue();
                    break;
                case "os.version":
                    osVersion = attr.getStringValue();
                    break;
                case "device.identifier":
                    deviceIdentifier = attr.getStringValue();
                    break;
                case "device.vendor":
                    deviceVendor = attr.getStringValue();
                    break;
                case "app.uiOrientation":
                    String orientation = mapOrientation(attr.getStringValue());
                    attr.getValue().put("stringValue", orientation);
                    break;
            }
        }

        enhanceOsInfo(attributes, osName, osVersion);
        enhanceDeviceIdentifier(attributes, deviceIdentifier, deviceVendor);
        regionEnhancer.enhanceAndroidRegion(attributes);
    }

    private void enhanceOsInfo(List<Attribute> attributes, String osName, String osVersion) {
        if (osName != null && osVersion != null) {
            attributes.add(Attribute.create("os", osName + " " + osVersion));
        }
    }

    private void enhanceDeviceIdentifier(List<Attribute> attributes, String deviceIdentifier, String deviceVendor) {
        if (deviceIdentifier != null && deviceVendor != null) {
            attributes.add(Attribute.create("device_identifier", deviceVendor + " " + deviceIdentifier));
        }
    }

    private String mapOrientation(String orientation) {
        switch (orientation) {
            case "Portrait":
                return "竖屏";
            case "Landscape":
                return "横屏";
            default:
                return "其他";
        }
    }

    public boolean enhanceAndFilterResourceSpan(ResourceSpan resourceSpan, RumAppSettings appSettings) {
        if (resourceSpan == null || resourceSpan.getResource() == null) return false;

        if (authorizeDevice(resourceSpan, appSettings)) return false;

        enhanceAndroidResource(resourceSpan.getResource());
        truncateResourceAttributes(resourceSpan.getResource().getAttributes());

        for (ScopeSpan scopeSpan : resourceSpan.getScopeSpans()) {
            String scopeName = scopeSpan.getScope().getName();
            scopeSpan.getSpans().removeIf(span -> {
                truncateSpanAttributes(span.getAttributes());
                return false;
            });
        }

        return !resourceSpan.getScopeSpans().isEmpty();
    }

    public boolean enhanceAndFilterResourceLog(ResourceLog resourceLog) {
        if (resourceLog == null || resourceLog.getResource() == null) return false;
        Resource resource = resourceLog.getResource();
        enhanceAndroidResource(resource);


        return !resourceLog.getScopeLogs().isEmpty();
    }


    private void truncateResourceAttributes(List<Attribute> attributes) {
        for (Attribute attr : attributes) {
            switch (attr.getKey()) {
                case "customUserId":
                    attr.getValue().put("stringValue", StringUtil.truncateToByteLength(attr.getStringValue(), 50));
                    break;
                case "app.versionCode":
                    attr.getValue().put("stringValue", StringUtil.truncateToByteLength(attr.getStringValue(), 16));
                    break;
                case "app.versionName":
                    attr.getValue().put("stringValue", StringUtil.truncateToByteLength(attr.getStringValue(), 30));
                    break;
                case "device.identifier":
                    attr.getValue().put("stringValue", StringUtil.truncateToByteLength(attr.getStringValue(), 50));
                    break;
                case "carrier.networkType":
                    attr.getValue().put("stringValue", StringUtil.truncateToByteLength(attr.getStringValue(), 10));
                    break;
                case "device.arch":
                    attr.getValue().put("stringValue", StringUtil.truncateToByteLength(attr.getStringValue(), 20));
                    break;
            }
        }
    }

    private void truncateSpanAttributes(List<Attribute> attributes) {
        for (Attribute attr : attributes) {
            switch (attr.getKey()) {
                case "viewName":
                case "actionName":
                    attr.getValue().put("stringValue", StringUtil.truncateToByteLength(attr.getStringValue(), 100));
                    break;
                // Add other iOS specific fields as needed
            }
        }
    }

    private void truncateLogAttributes(List<Attribute> attributes) {
        for (Attribute attr : attributes) {
            switch (attr.getKey()) {
                case "threadTrace":
                    attr.getValue().put("stringValue", StringUtil.truncateToByteLength(attr.getStringValue(), 1048576));
                    break;
                case "binaryImage":
                    attr.getValue().put("stringValue", StringUtil.truncateToByteLength(attr.getStringValue(), 36));
                    break;
                case "crashType":
                    attr.getValue().put("stringValue", StringUtil.truncateToByteLength(attr.getStringValue(), 100));
                    break;
                // Add other iOS specific log fields
            }
        }
    }

}
