package com.databuff.dts.receive.biz.processor.config.compiler; // 建议放在新的 compiler 子包下

import com.databuff.dts.receive.biz.processor.condition.ExecutableCondition;

import java.util.Set;

/**
 * 条件编译器接口。
 * <p>
 * 负责将原始的、通常来自 JSON 的条件配置（可能是嵌套的 Map 或 List 结构）
 * 解析并编译成可执行的 {@link ExecutableCondition} 对象树。
 * </p>
 * <p>
 * 实现类应包含处理不同条件类型（简单条件、逻辑组合）、操作符、
 * 数据类型推断与转换等复杂逻辑。
 * </p>
 */
public interface ConditionCompiler {

    /**
     * 编译给定的原始条件配置。
     *
     * @param conditionsRaw           从配置（如 JSON 的 "conditions" 字段）中反序列化得到的原始条件数据。
     * 通常是 {@code List<Map<String, Object>>} 或类似的嵌套结构。
     * @param requiredFieldsCollector 一个 Set，用于收集在条件中遇到的所有字段路径 (e.g., "duration", "meta.http.code")。
     * 编译器在解析条件时应将遇到的字段添加到此集合中。
     * @return 编译后的 {@link ExecutableCondition} 树的根节点。如果原始条件数据为 null、空、无效或解析/编译失败，
     * 则返回 null（调用方需要处理 null 情况，例如跳过该规则）。
     */
    ExecutableCondition compileConditions(Object conditionsRaw, Set<String> requiredFieldsCollector);

    // 未来可以考虑增加更多方法，例如校验条件配置的有效性等
    // boolean validateConditions(Object conditionsRaw);
}