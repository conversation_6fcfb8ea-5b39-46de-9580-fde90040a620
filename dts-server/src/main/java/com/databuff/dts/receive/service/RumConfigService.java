package com.databuff.dts.receive.service;

import com.alibaba.fastjson.JSON;
import com.databuff.dts.receive.cache.AppSettingsCacheService;
import com.databuff.dts.receive.model.RumConfigDTO;
import com.databuff.entity.rum.mysql.RumAppSettings;
import com.databuff.entity.rum.web.AndroidSecuritySettings;
import com.databuff.entity.rum.web.IosSecuritySettings;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class RumConfigService {
    @Autowired
    private AppSettingsCacheService appSettingsCacheService;

    public RumConfigDTO getRumConfig(String appKey) {
        RumAppSettings appSettings = appSettingsCacheService.getAppSettings(appKey);
        if (appSettings == null) {
            return null;
        }

        RumConfigDTO config = new RumConfigDTO();
        config.setAppId(appSettings.getId());

        // 目前两者数据结构相同
        if ("ios".equals(appSettings.getAppType())){
            // Convert security settings from JSON string to IosSecuritySettings
            IosSecuritySettings securitySettings = JSON.parseObject(
                    appSettings.getSecuritySettings(),
                    IosSecuritySettings.class
            );

            if (securitySettings != null) {
                config.setEnabled(securitySettings.getEnabled());
                config.setNetworkEnabled(securitySettings.getNetworkEnabled());
                config.setWebviewEnabled(securitySettings.getWebviewEnabled());
                config.setCrashEnabled(securitySettings.getCrashEnabled());
                config.setAnrEnabled(securitySettings.getAnrEnabled());
                config.setLaunchEnabled(securitySettings.getLaunchEnabled());
                config.setPageEnabled(securitySettings.getPageEnabled());
                config.setActionEnabled(securitySettings.getActionEnabled());
            }
        } else if ("android".equals(appSettings.getAppType())) {
            // Convert security settings from JSON string to AndroidSecuritySettings
            AndroidSecuritySettings securitySettings = JSON.parseObject(
                    appSettings.getSecuritySettings(),
                    AndroidSecuritySettings.class
            );

            if (securitySettings != null) {
                config.setEnabled(securitySettings.getEnabled());
                config.setNetworkEnabled(securitySettings.getNetworkEnabled());
                config.setWebviewEnabled(securitySettings.getWebviewEnabled());
                config.setCrashEnabled(securitySettings.getCrashEnabled());
                config.setAnrEnabled(securitySettings.getAnrEnabled());
                config.setLaunchEnabled(securitySettings.getLaunchEnabled());
                config.setPageEnabled(securitySettings.getPageEnabled());
                config.setActionEnabled(securitySettings.getActionEnabled());
            }
        }

        return config;
    }
}
