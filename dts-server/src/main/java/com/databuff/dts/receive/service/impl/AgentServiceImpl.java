package com.databuff.dts.receive.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.databuff.common.constants.Constant;
import com.databuff.common.exception.ErrorCode;
import com.databuff.common.utils.OtelMetricUtil;
import com.databuff.dao.mysql.*;
import com.databuff.dts.config.common.CommonResponse;
import com.databuff.dts.receive.model.AllProcessCollectRules;
import com.databuff.dts.receive.model.InsertProcessCollectRuleRequest;
import com.databuff.dts.receive.model.InsertProcessIdentifyRuleRequest;
import com.databuff.dts.receive.service.AgentAuthUnitService;
import com.databuff.dts.receive.service.AgentService;
import com.databuff.dts.receive.service.NormalService;
import com.databuff.dts.ringbuffer.CustomRingBuffer;
import com.databuff.dts.ringbuffer.ParseTrace;
import com.databuff.dts.util.CustomBlockException;
import com.databuff.dts.util.CustomException;
import com.databuff.dts.util.FileUtil;
import com.databuff.dts.util.JsonConvertUtil;
import com.databuff.entity.*;
import com.databuff.entity.dump.AgentDump;
import com.databuff.service.JedisService;
import com.databuff.service.MetricsInsertService;
import com.databuff.util.MetricsTransformUtil;
import com.github.benmanes.caffeine.cache.Cache;
import com.github.benmanes.caffeine.cache.Caffeine;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import static com.databuff.common.constants.MetricName.HOST_INFO_DISCARD;
import static com.databuff.common.constants.MetricName.METRIC_PUBLISH;
import static com.databuff.dts.config.common.CommonConstants.TAG_AGENT_HOST;

/**
 * @package com.databuff.webapp.collect.service.impl
 * @company: dacheng
 * @author: zlh
 * @createDate: 2021/7/15
 */
@Service
@Slf4j
public class AgentServiceImpl implements AgentService {

    @Resource
    private AggentManagerMapper aggentManagerMapper;
    public static final String AGENT_DUMP_PATH = "/var/dacheng/agentDump/";

    @Resource
    private HostInfoEntityMapper hostInfoEntityMapper;
    @Resource
    private PluginMapper pluginMapper;
    @Resource
    private JedisService jedisService;
    @Autowired
    private AgentAuthUnitService agentAuthUnitService;
    @Autowired
    private NormalService normalService;
    @Resource
    private MetricReceiver metricReceiver;
    @Autowired
    private MetricsInsertService metricsInsertService;
    @Autowired
    private ProcessCollectRulesMapper processCollectRulesMapper;
    @Autowired
    private MetaConfigMapper metaConfigMapper;
    @Autowired
    private ProcessIdentifyRulesMapper processIdentifyRulesMapper;
    @Autowired
    private CustomRingBuffer<ParseTrace> parseRingBuffer;
    /**
     * 获取头信息
     */
    public final static String[] HEADER_KEYS = {Constant.AGENT_HOSTNAME, Constant.AGENT_HOSTNAME2, Constant.API_KEY, Constant.API_KEY2, Constant.USER_AGENT, Constant.HOST_ID, Constant.IS_K8S};

    public static final String AGENT_LOG_PATH = "/var/dacheng/agentLog/";
    @Resource
    private AgentDumpMapper agentDumpMapper;

    /**
     * 开启的指标缓存
     */
    private final Cache<String, JSONObject> metricOpenCache = Caffeine.newBuilder()
            .softValues().maximumSize(500)
            .expireAfterWrite(3, TimeUnit.MINUTES).build();
    private final Cache<String, List<String>> pluginAppOpenCache = Caffeine.newBuilder()
            .softValues().maximumSize(500)
            .expireAfterWrite(3, TimeUnit.MINUTES).build();

    @Override
    public Long agentValidate(HttpServletRequest request, HttpServletResponse response, String apiKey) {
        try {
            // 处理agent授权units
            Map<String, Object> headerMap = metricReceiver.getHeaders(request, HEADER_KEYS);
            String host = headerMap.get(Constant.AGENT_HOSTNAME2).toString();
            String hostIdStr = headerMap.get(Constant.HOST_G_ID).toString();

            if (StringUtils.isNotBlank(apiKey)) {
                agentAuthUnitService.dealAgentAuthUnits(headerMap, request, apiKey);
            }
            //新增字段 agent_type 表示 agent 的类型
            //●  agent_type = one_agent
            //●  agent_type = cluster_agent
            //k8s部署新增环境变量 DC_AGENT_TYPE
            //●  DC_AGENT_TYPE
            //●  value: one_agent cluster_agent
            String agentType = request.getHeader("agent_type") == null ? null : request.getHeader("agent_type");

            // 判断当前主机是否在授权内
            Integer isValidAAU = agentAuthUnitService.getCurrAgentValid(apiKey, hostIdStr);
            if (isValidAAU == 0) {
                response.setStatus(HttpStatus.FORBIDDEN.value());
                response.setCharacterEncoding("UTF-8");
                response.setContentType("application/json; charset=utf-8");
                try (PrintWriter out = response.getWriter()) {
                    String data = JsonConvertUtil.objectToJson(new CommonResponse(ErrorCode.LOGIN_TOKEN_ERROR, "无权访问(Unauthorized): 主机：{} " + host + "授权未通过", null));
                    out.append(data);
                } catch (IOException e) {
                    log.error("直接返回Response信息出现IOException异常:" + e.getMessage());
                    throw new CustomException("直接返回Response信息出现IOException异常:" + e.getMessage());
                }
                return null;
            }

            if (StringUtils.isNotBlank(agentType) && "cluster_agent".equals(agentType)) {
                log.info("agentValidate==============================> isk8s:{}，hostName：{} ", agentType, host);
                return System.currentTimeMillis();
            } else {
                //这边agnet需要计算网络时间，改为异步直接返回，因为apikey等相关验证拦截器已验证
                normalService.agentAndDashboardUpdate(headerMap, apiKey);
                log.info("agentValidate==============================> apiKey:{},host:{},hostId:{},agent_type:{} ", apiKey, host, hostIdStr, headerMap.get("agent_type"));

                //返回当前时间戳
                return System.currentTimeMillis();
            }
        } catch (Exception e) {
            //错误了，将request的header信息全部打印出来
            Enumeration<String> headerNames = request.getHeaderNames();
            StringBuilder headerStr = new StringBuilder();
            while (headerNames.hasMoreElements()) {
                String key = headerNames.nextElement();
                String value = request.getHeader(key);
                headerStr.append(key).append(":").append(value).append(";");
            }
            log.error("agentValidate header:{} errorMsg:{}", headerStr, e);
        }
        return null;
    }

    @Override
    public void agentIntake(HttpServletRequest req, String apiKey) {
        // body信息
        String content = null;
        String host = "";
        try {
            content = metricReceiver.decompress(req).toString();
            // 数据转json
            if (StringUtils.isBlank(content)) {
                return;
            }
            // 获取头信息
            Map<String, Object> headerMap = metricReceiver.getHeaders(req, HEADER_KEYS);
            JSONObject contentJson = JSON.parseObject(content);

            parseRingBuffer.getRingBuffer().publishEvent((event1, sequence) -> {
                // 将数据放入事件对象中
                event1.setType("agentHostInfo");
                event1.setHeaderMap(headerMap);
                event1.setData(contentJson);
                event1.setDataLength(contentJson.size());
                event1.setHeaderMap(headerMap);
            });
        } catch (Exception e) {
            Map<String, String> logTags = new HashMap<>();
            logTags.put(TAG_AGENT_HOST, host);
            logTags.put("error", e.getMessage());
            OtelMetricUtil.logCounter(HOST_INFO_DISCARD, logTags,1);
            log.error("--agent Intake数据处理异常-{}-{}", content, e);
        }
    }

    @Override
    public CommonResponse modifAgentUpdateList(HttpServletRequest req, AgentUpdata agentUpdata) {
        CommonResponse commonResponse = new CommonResponse();
        Map<String, Object> headerMap = metricReceiver.getHeaders(req, HEADER_KEYS);
        String apiKey = headerMap.getOrDefault(Constant.API_KEY2, "").toString();
        String hostName = headerMap.getOrDefault(Constant.AGENT_HOSTNAME2, "").toString();

        Integer agentStatus = agentUpdata.getStatus();
        // Status:   1,
        //    Progress: "安装包下载成功",
        //    Status:   2,
        //    Progress: "通知解压包成功",
        //  Status:   3,
        //    Progress: "安装进行中", 更新/重启/停止/启动 进行中
        //  Status:   4,
        //        Progress: "更新失败",更新/重启/停止/启动 失败
        //    Status:   5,
        //    Progress: "更新成功", 更新/重启/停止/启动 成功
        //  Status:   6,
        //      Progress: 下载失败,
        //  Status:   7,
        //      Progress: 安装包下载中,
        //平台状态 0待更新/重启/停止/启动，1更新/重启/停止/启动，2更新/重启/停止/启动失败，3更新/重启/停止/启动中
        switch (agentStatus) {
            case 0:
                agentUpdata.setStatus(0);
                break;
            case 5:
                agentUpdata.setStatus(1);
                break;
            case 4:
            case 6:
                agentUpdata.setStatus(2);
                break;
            case 1:
            case 3:
            case 2:
            case 7:
                agentUpdata.setStatus(3);
                break;
            default:
                commonResponse.setMessage("未知agent状态码=" + agentUpdata.getId());
                commonResponse.setStatus(40001);
                return commonResponse;
        }
        if (agentUpdata != null && agentUpdata.getId() != null) {
            //任务id不为空，则代表存在更新进度状态等操作
            AgentUpdata info = aggentManagerMapper.loadAgentUpdataInfo(agentUpdata.getId());
            if (info == null) {
                commonResponse.setMessage("未查询到id=" + agentUpdata.getId() + "当条记录");
                commonResponse.setStatus(40001);
                return commonResponse;
            }
            if (info.getStartTime() == null) {
                agentUpdata.setStartTime(new Date());
            }
            if ((agentUpdata.getStatus() == 1 || agentUpdata.getStatus() == 2) && info.getEndTime() == null) {
                agentUpdata.setEndTime(new Date());
            }
            agentUpdata.setApiKey(apiKey);
            //id不空，则需要更新操作
            aggentManagerMapper.upAgentUpdata(agentUpdata);
            info = aggentManagerMapper.loadAgentUpdataInfo(agentUpdata.getId());
            commonResponse.setData(info);
            return commonResponse;
        }
        final AgentUpdata updata = aggentManagerMapper.getUpdateOne(apiKey, hostName, null, 0, null);
        if (updata != null) {
            commonResponse.setData(updata);
            return commonResponse;
        }
        return commonResponse;
    }

    @Override
    public void agentDownload(String packName, String version, String zipName, HttpServletRequest req, HttpServletResponse response) {
        Map<String, Object> headerMap = metricReceiver.getHeaders(req, HEADER_KEYS);
        String apiKey = headerMap.getOrDefault(Constant.API_KEY2, "").toString();
        String host = headerMap.getOrDefault(Constant.AGENT_HOSTNAME2, "").toString();
        List<AgentPack> packs = aggentManagerMapper.getAgentPackList(apiKey, packName, version);
        if (packs == null || packs.size() == 0) {
            log.error("未找到安装包名称和版本号对应的记录,{},{}", packName, version);
            throw new CustomException("未找到安装包名称和版本号对应的记录！");
        }

        AgentPack pack = packs.get(0);

        String path = pack.getPath();
        if (StringUtils.isNotBlank(zipName)) {
            path = path + File.separator + zipName;
        } else {
            //老版本更新，根据操作系统类型获取对应的安装包
            HostInfoEntity hostInfo = hostInfoEntityMapper.selectByName(host, null, null, apiKey);
            zipName = "linux-x86_64.zip";
            if (hostInfo != null) {
                JSONObject platform = JSONObject.parseObject(hostInfo.getPlatform());
                String gooarch = platform.getString("GOOARCH");
                if (gooarch != null && "arm64".equals(gooarch)) {
                    zipName = "linux-arm64.zip";
                }
            }
            path = path + File.separator + zipName;
        }
        File file = new File(path);

        if (!file.exists()) {
            log.error("未找到安装包名称和版本号对应的记录的本地安装包,{},{},{}", packName, version, path);
            throw new CustomException("未找到安装包名称和版本号对应的记录的本地安装包！");
        }
        Set<String> hkeys = jedisService.hkeys("agent-download");
        log.info("apiKey:{},host:{}请求下载安装包；当前下载数为{}", apiKey, host, hkeys.size());
        if (hkeys.size() >= 5) {
            log.warn("apiKey:{},host:{}请求下载安装包；当前下载数为{},超出限制,请稍后再说", apiKey, host, hkeys.size());
            throw new CustomBlockException("apiKey:{" + apiKey + "},host:{" + host + "}请求下载安装包；当前下载数据为{" + hkeys.size() + "}超出限制，请稍后重试");
        } else if (hkeys.contains(apiKey + ":" + host)) {
            log.warn("apiKey:{},host:{}正在下载安装包；请勿重复请求下载", apiKey, host);
            throw new CustomBlockException("apiKey:{" + apiKey + "},host:{" + host + "}正在下载安装包；请勿重复请求下载");
        }

        jedisService.hset("agent-download", apiKey + ":" + host, packName + "::" + version, 60 * 30);

        try (FileInputStream fileInputStream = new FileInputStream(file)) {
            response.reset();
            // 设置response的Header
            response.setHeader("Content-Disposition", "attachment; filename=" + packName);
            response.setContentType("application/octet-stream");
            FileUtil.writeIntoOut(fileInputStream, response.getOutputStream(), 1024, false);
        } catch (FileNotFoundException e) {
            log.error("安装包名下载失败，文件未找到,{},{},{}", packName, version, path, e);
            throw new CustomException("安装包名下载失败，文件未找到！");
        } catch (IOException e) {
            log.error("安装包名下载失败，IO异常,{},{},{},{}", packName, version, path, e);
            throw new CustomException("安装包名下载失败，IO异常！");
        } finally {
            log.info("apiKey:{},host:{}下载安装包，下载结束，删除key", apiKey, host);
            jedisService.hdel("agent-download", apiKey + ":" + host);
        }
    }

    @Override
    public CommonResponse agentConfigSync(HttpServletRequest req, JSONObject info) {
        String data = info.getString("data");
        String msg = info.getString("msg");
        //毫秒
        Long modifyTime = info.getLong("modifyTime");
        Integer error = info.getInteger("error");
        CommonResponse commonResponse = new CommonResponse();

        Map<String, Object> headerMap = metricReceiver.getHeaders(req, HEADER_KEYS);
        String apiKey = headerMap.getOrDefault(Constant.API_KEY2, "").toString();
        String hostName = headerMap.getOrDefault(Constant.AGENT_HOSTNAME2, "").toString();
        AgentEntity agentConfig = aggentManagerMapper.getAgentConfig(apiKey, hostName);
        if (agentConfig == null) {
            commonResponse.setMessage("未查询到apikey=" + apiKey + ",host:" + hostName + " 当条记录");
            commonResponse.setStatus(40001);
            return commonResponse;
        }

        if (StringUtils.isNotBlank(data)) {
            //data 不为空代表上传了配置文件上来，更新配置文件
//            try {
//                data = Base64ConvertUtil.decode(data);
//            } catch (UnsupportedEncodingException e) {
//                log.error("  base64 decode error2:{}",e);
//            }
            if (agentConfig.getConfigTime() == null || modifyTime == null || agentConfig.getConfigTime().getTime() <= modifyTime) {
                log.info("平台配置时间小于等于agent服务器时间 将配置内容更新数据库");

                //只有agent端的修改时间大于平台修改时间才更新内容
                agentConfig.setConfigContent(data);
                agentConfig.setConfigStatus(1);
                agentConfig.setConfigTime(new Date(modifyTime));
                aggentManagerMapper.updataAgentConfig(agentConfig);
            }
            commonResponse.setData("0");
            return commonResponse;
        }
        if (agentConfig.getConfigStatus() != null && error == 0 && StringUtils.isNotBlank(msg) && msg.equals("success")) {
            //错误0且msg为success 代表更新成功
            agentConfig.setConfigMsg(msg);
            agentConfig.setConfigStatus(1);
            aggentManagerMapper.updataAgentConfig(agentConfig);
            commonResponse.setData("0");
            return commonResponse;
        }

        if (agentConfig.getConfigStatus() != null && error == 1) {
            //存在错误，更新信息
            agentConfig.setConfigMsg(msg);
            agentConfig.setConfigStatus(2);
            aggentManagerMapper.updataAgentConfig(agentConfig);
            commonResponse.setData("0");
            return commonResponse;
        }

        if (agentConfig.getConfigContent() == null || agentConfig.getConfigContent().length() == 0) {
            //配置文件内容为空，则agent上报配置内容。
            commonResponse.setData("1");
            return commonResponse;
        }

        if (agentConfig.getConfigStatus() == 0) {
            if (agentConfig.getConfigTime() == null || modifyTime == null || agentConfig.getConfigTime().getTime() >= modifyTime) {
                log.info("平台配置时间大于agent服务器时间 将配置内容返回更新 状态修改为更新中");
                //状态为待更新，则将配置文件内容返回
                commonResponse.setData(agentConfig.getConfigContent());
                //返回更新内容后，将 状态改为更新中。
                agentConfig.setConfigStatus(3);
                aggentManagerMapper.updataAgentConfig(agentConfig);
            }
            return commonResponse;
        }
        commonResponse.setData("0");
        return commonResponse;
    }

    @Override
    public CommonResponse agentLogUpload(HttpServletRequest request, Integer id, MultipartFile logZip) {
        Map<String, Object> headerMap = metricReceiver.getHeaders(request, HEADER_KEYS);
        String apiKey = headerMap.getOrDefault(Constant.API_KEY2, "").toString();
        String hostName = headerMap.getOrDefault(Constant.AGENT_HOSTNAME2, "").toString();
        CommonResponse commonResponse = new CommonResponse();
        //任务id不为空，则代表存在更新进度状态等操作
        AgentUpdata info = aggentManagerMapper.loadAgentUpdataInfo(id);
        if (info == null || !info.getHost().equals(hostName)) {
            commonResponse.setMessage(hostName + "上传日志文件,未查询到任务id=" + id + "当条记录");
            commonResponse.setStatus(40001);
            return commonResponse;
        }
        String path = AGENT_LOG_PATH + File.separator + apiKey + File.separator;
//        String path = "C:\\Users\\<USER>\\Desktop\\agentLog"+File.separator+apiKey+File.separator;
        File upgradePath = new File(path);
        if (!upgradePath.exists()) {
            upgradePath.mkdirs();
        }
        File agentLogZipFile = null;
        try {
            log.info("-------------------{}上传agent日志压缩包", hostName);
            agentLogZipFile = FileUtil.multipartFileToFile(logZip);
        } catch (IOException e) {
            log.error("流转换文件 error:{}", e);
            commonResponse.setMessage(hostName + "上传agent日志包失败，流转换文件错误");
            if (agentLogZipFile != null && !agentLogZipFile.delete()) {
                log.warn(hostName + "上传agent日志压缩包文件删除失败");
            }
            return commonResponse;
        }
        //解压安装包到版本文件下面
        //解压后文件的文件夹路径
        String upPackPath = path + hostName + File.separator;
        boolean ret = false;
        List<String> fileNames = new ArrayList<>();
        try {
            log.info("-------------------解压{}日志包，并删除解压文件夹", hostName);
            ret = FileUtil.unzip(agentLogZipFile.getAbsolutePath(), upPackPath, null);
            fileNames = FileUtil.getZipFileNames(agentLogZipFile.getAbsolutePath());
        } catch (Exception e) {
            log.error("解压更新包 error:{}", e);
            commonResponse.setMessage(hostName + "解压日志包异常");
        } finally {
            if (agentLogZipFile != null && !agentLogZipFile.delete()) {
                log.warn(hostName + "上传日志压缩包文件删除失败");
            }
        }
        if (!ret) {
            commonResponse.setMessage(hostName + "日志包解压失败，请确认日志包完整");
            return commonResponse;
        }
        for (String fileName : fileNames) {
            File file = new File(upPackPath + fileName);
            if (file.exists()) {
                AgentLog agentLog = new AgentLog();
                agentLog.setApiKey(apiKey);
                agentLog.setHost(hostName);
                agentLog.setLogName(fileName);
                agentLog.setPath(upPackPath + fileName);
                agentLog.setIsNew(1);
                agentLog.setUploadTime(new Date());
                //读取文件大小,
                //读取日志第一条和最后一条时间戳
                //日志内容 2024/12/06 - 05:25:59.821	info	[skip process 32190 bash since of no stats]
                fillAgentLogTime(file, agentLog);
                aggentManagerMapper.inOrUpAgentLog(agentLog);
            }
        }
        return commonResponse;
    }

    @Override
    public CommonResponse agentDumpUpload(HttpServletRequest request, Integer id, MultipartFile zipFile) {
        Map<String, Object> headerMap = metricReceiver.getHeaders(request, HEADER_KEYS);
        final String apiKey = headerMap.getOrDefault(Constant.API_KEY2, "").toString();
        final String service = headerMap.getOrDefault(Constant.Trace.SERVICE, "").toString();
        final String serviceInstance = headerMap.getOrDefault(Constant.Trace.SERVICE_INSTANCE, "").toString();
        final String serviceAndInstance = service + File.separator + serviceInstance;
        CommonResponse commonResponse = new CommonResponse();
        //任务id不为空，则代表存在更新进度状态等操作
        AgentUpdata info = aggentManagerMapper.loadAgentUpdataInfo(id);
        if (info == null) {
            commonResponse.setMessage(service + "上传Dump文件,未查询到任务id=" + id + "当条记录");
            commonResponse.setStatus(40001);
            return commonResponse;
        }
        final String path = AGENT_DUMP_PATH + File.separator + apiKey + File.separator;
        File upgradePath = new File(path);
        if (!upgradePath.exists()) {
            upgradePath.mkdirs();
        }
        File dumpZipFile = null;
        try {
            log.info("-------------------{}上传agentDump压缩包", serviceAndInstance);
            dumpZipFile = FileUtil.multipartFileToFile(zipFile);
        } catch (IOException e) {
            log.error("流转换文件 error:{}", e);
            commonResponse.setMessage(serviceAndInstance + "上传agentDump包失败，流转换文件错误");
            if (dumpZipFile != null && !dumpZipFile.delete()) {
                log.warn(serviceAndInstance + "上传agentDump压缩包文件删除失败");
            }
            return commonResponse;
        }
        //解压安装包到版本文件下面
        //解压后文件的文件夹路径
        String upPackPath = path + serviceAndInstance + File.separator;
        boolean ret = false;
        List<String> fileNames = new ArrayList<>();
        try {
            log.info("-------------------解压{}Dump包，并删除解压文件夹", serviceAndInstance);
            ret = FileUtil.unzip(dumpZipFile.getAbsolutePath(), upPackPath, null);
            fileNames = FileUtil.getZipFileNames(dumpZipFile.getAbsolutePath());
        } catch (Exception e) {
            log.error("解压更新包 error:{}", e);
            commonResponse.setMessage(serviceAndInstance + "解压Dump包异常");
        } finally {
            if (dumpZipFile != null && !dumpZipFile.delete()) {
                log.warn(serviceAndInstance + "上传Dump压缩包文件删除失败");
            }
        }
        if (!ret) {
            commonResponse.setMessage(serviceAndInstance + "Dump包解压失败，请确认Dump包完整");
            return commonResponse;
        }
        for (String fileName : fileNames) {
            final String filePath = (upPackPath + fileName).replaceAll("[:\"*?<>|]", "");
            File file = new File(filePath);
            if (file.exists()) {
                final Date nowTime = new Date();
                AgentDump dump = AgentDump.builder()
                        .apiKey(apiKey)
                        .fileName(fileName.replaceAll("[:\"*?<>|]", ""))
                        .path(filePath)
                        .fileSize(file.length())
                        .uploadTime(nowTime)
                        .createTime(nowTime)
                        .build();
                agentDumpMapper.upsert(dump);
            }
        }
        return commonResponse;
    }

    /**
     * 读取日志的第一条和最后一条时间戳.设置文件大小
     */
    private void fillAgentLogTime(File file, AgentLog agentLog) {
        // 读取日志的第一条和最后一条时间戳
        String firstTimestamp = null;
        String lastTimestamp = null;

        try (BufferedReader reader = new BufferedReader(new FileReader(file))) {
            String firstLine = reader.readLine(); // 读取第一行
            if (firstLine != null) {
                firstTimestamp = extractTimestamp(firstLine); // 提取时间戳
            }
        } catch (IOException e) {
            log.error("读取第一行{}日志文件日期异常: {}", file.getName(), e);
        }
        try (RandomAccessFile raf = new RandomAccessFile(file, "r")) {
            long pos = raf.length() - 1;
            StringBuilder sb = new StringBuilder();

            while (pos >= 0) {
                raf.seek(pos);
                char c = (char) raf.readByte();
                if (c == '\n' && sb.length() > 0) {
                    break;
                } else if (c != '\n') {
                    sb.append(c);
                }
                pos--;
            }
            if (sb.length() > 0) {
                lastTimestamp = extractTimestamp(sb.reverse().toString()); // 提取时间戳
            }
        } catch (IOException e) {
            log.error("读取最后一行{}日志文件日期异常: {}", file.getName(), e);
        }
        // 设置日志的时间戳
        if (firstTimestamp != null) {
            agentLog.setLogFileCreateTime(parseTimestamp(firstTimestamp)); // 第一个时间戳作为更新时刻
        }
        if (lastTimestamp != null) {
            agentLog.setLogFileUpdateTime(parseTimestamp(lastTimestamp)); // 最后一个时间戳作为最后更新时间
        }
        agentLog.setLogFileSize(file.length());
    }

    /**
     * 从日志行中提取时间戳（假设格式是 "yyyy/MM/dd - HH:mm:ss.SSS"）
     */
    private static String extractTimestamp(String logLine) {
        String timestampPattern = "^(\\d{4}/\\d{2}/\\d{2} - \\d{2}:\\d{2}:\\d{2}\\.\\d{3})";
        Pattern pattern = Pattern.compile(timestampPattern);
        Matcher matcher = pattern.matcher(logLine);
        if (matcher.find()) {
            return matcher.group(1); // 提取时间戳
        }
        return null;
    }

    /**
     * 将时间戳字符串解析为 Date 类型
     */
    private static Date parseTimestamp(String timestamp) {
        try {
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy/MM/dd - HH:mm:ss.SSS");
            return sdf.parse(timestamp);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    @Override
    public CommonResponse agentMetricSync(HttpServletRequest req, JSONObject info) {
        Map<String, Object> headerMap = metricReceiver.getHeaders(req, HEADER_KEYS);
        String apiKey = headerMap.getOrDefault(Constant.API_KEY2, "").toString();
        String hostName = headerMap.getOrDefault(Constant.AGENT_HOSTNAME2, "").toString();
        List<JSONObject> syncMetrics = new ArrayList<>();
        CommonResponse commonResponse = new CommonResponse();

        String allVersion = info.getString("all");

        List<String> pluginApps = pluginAppOpenCache.get(apiKey, v -> null);
        if (pluginApps == null) {
            List<String> installedApps = pluginMapper.queryInstalledPluginApps(apiKey, 1);
            List<String> openApps = pluginMapper.getPluginOpenMetricsAppList(apiKey);
            installedApps.retainAll(openApps); // 取交集
            pluginAppOpenCache.put(apiKey, installedApps);
            pluginApps = installedApps;
        }
        if (StringUtils.isNotBlank(allVersion)) {
            //all 不为空代表agent端需要全部采集指标
            info.remove("all");
            for (String app : pluginApps) {
                info.put(app, allVersion);
            }
        }
        for (Map.Entry<String, Object> entry : info.entrySet()) {
            String app = entry.getKey();
            if (!pluginApps.contains(app)) {
                //不包含没有开启或授权
                continue;
            }
            Integer version = Integer.parseInt(entry.getValue().toString());
            String key = apiKey + ":" + app;
            JSONObject metricInfo = metricOpenCache.get(key, v -> null);
            if (metricInfo == null) {
                //没有缓存则查询数据库
                DatabuffPluginMetric pluginMetric = pluginMapper.getPluginOpenMetrics(app, apiKey);
                if (pluginMetric == null) {
                    //数据库配置中也没有则采取默认采集指标
                    continue;
                }
                List<MetricsInsert> metricsInserts = metricsInsertService.findAllByApp(app);

                Integer dbVersion = pluginMetric.getVersion();
                JSONObject openMetricJson = JSONObject.parseObject(pluginMetric.getOpenMetrics());

                JSONObject openInsertMetricJson = MetricsTransformUtil.core2Insert(metricsInserts, openMetricJson);

                for (Map.Entry<String, Object> openMjobj : openInsertMetricJson.entrySet()) {
                    JSONObject openMj = openInsertMetricJson.getJSONObject(openMjobj.getKey());
                    openMj.put("version", dbVersion);
                    openInsertMetricJson.put(openMjobj.getKey(), openMj);
                }
                if (dbVersion > version) {
                    syncMetrics.add(openInsertMetricJson);
                }
                metricOpenCache.put(key, openInsertMetricJson);
            } else {
                Integer dbVersion = 0;
                for (Map.Entry<String, Object> openMjobj : metricInfo.entrySet()) {
                    JSONObject openMj = metricInfo.getJSONObject(openMjobj.getKey());
                    dbVersion = openMj.getInteger("version");
                }
                if (dbVersion > version) {
                    syncMetrics.add(metricInfo);
                }
            }
        }
        Map<String, String> tags = new HashMap<>();
        for (JSONObject jsonObject : syncMetrics) {
            for (Map.Entry<String, Object> entry : jsonObject.entrySet()) {
                String plugin = entry.getKey();
                tags.put("plugin", plugin);
                OtelMetricUtil.logCounter(METRIC_PUBLISH, tags, 1);
            }
        }
        commonResponse.setData(syncMetrics);
        return commonResponse;
    }

    @Override
    public CommonResponse processCollectRuleSync(HttpServletRequest req, JSONObject info) {
        CommonResponse commonResponse = new CommonResponse();

        Map<String, Object> headerMap = metricReceiver.getHeaders(req, HEADER_KEYS);
        String hostName = headerMap.getOrDefault(Constant.AGENT_HOSTNAME2, "").toString();

        AllProcessCollectRules allProcessCollectRules = new AllProcessCollectRules();
        List<ProcessCollectRulesEntity> whitelistTemp = processCollectRulesMapper.getProcessCollectRules(1, 0);
        List<ProcessCollectRulesEntity> blacklistTemp = processCollectRulesMapper.getProcessCollectRules(1, 1);

        List<InsertProcessCollectRuleRequest> whitelist = Lists.newArrayList();
        for (ProcessCollectRulesEntity processCollectRulesEntity : whitelistTemp) {
            InsertProcessCollectRuleRequest insertProcessCollectRuleRequest = new InsertProcessCollectRuleRequest();
            BeanUtils.copyProperties(processCollectRulesEntity, insertProcessCollectRuleRequest);
            insertProcessCollectRuleRequest.setHostnames(JSONObject.parseArray(processCollectRulesEntity.getHostnames(), String.class));
            insertProcessCollectRuleRequest.setIps(JSONObject.parseArray(processCollectRulesEntity.getIps(), String.class));
            whitelist.add(insertProcessCollectRuleRequest);
        }

        List<InsertProcessCollectRuleRequest> blacklist = Lists.newArrayList();
        for (ProcessCollectRulesEntity processCollectRulesEntity : blacklistTemp) {
            InsertProcessCollectRuleRequest insertProcessCollectRuleRequest = new InsertProcessCollectRuleRequest();
            BeanUtils.copyProperties(processCollectRulesEntity, insertProcessCollectRuleRequest);
            insertProcessCollectRuleRequest.setHostnames(JSONObject.parseArray(processCollectRulesEntity.getHostnames(), String.class));
            insertProcessCollectRuleRequest.setIps(JSONObject.parseArray(processCollectRulesEntity.getIps(), String.class));
            blacklist.add(insertProcessCollectRuleRequest);
        }

        if (hostName != null) {
            allProcessCollectRules.setWhitelist(whitelist.stream().filter(item -> {
                if (item.getHostname() == null) {
                    return true;
                }
                switch (item.getMatchHost()) {
                    case "=":
                        return item.getHostname().equals(hostName);
                    case "like":
                        return hostName.contains(item.getHostname());
                    case "startWith":
                        return hostName.startsWith(item.getHostname());
                    case "endWith":
                        return hostName.endsWith(item.getHostname());
                    case "regEx":
                        return hostName.matches(item.getHostname());
                }
                return true;
            }).collect(Collectors.toList()));
            allProcessCollectRules.setBlacklist(blacklist.stream().filter(item -> {
                if (item.getHostname() == null) {
                    return true;
                }
                switch (item.getMatchHost()) {
                    case "=":
                        return item.getHostname().equals(hostName);
                    case "like":
                        return hostName.contains(item.getHostname());
                    case "startWith":
                        return hostName.startsWith(item.getHostname());
                    case "endWith":
                        return hostName.endsWith(item.getHostname());
                    case "regEx":
                        return hostName.matches(item.getHostname());
                }
                return true;
            }).collect(Collectors.toList()));
        } else {
            allProcessCollectRules.setWhitelist(whitelist);
            allProcessCollectRules.setBlacklist(blacklist);
        }

        allProcessCollectRules.setCollectAllProcess(false);
        MetaConfigEntity metaConfig = metaConfigMapper.getEnabledConfig("PROCESS_COLLECT_RULES");
        if (null != metaConfig && metaConfig.getEnabled() && null != metaConfig.getParams()) {
            allProcessCollectRules.setCollectAllProcess(Boolean.valueOf(metaConfig.getParams()));
        }

        List<ProcessIdentifyRulesEntity> identifyBusinessProcessTemp = processIdentifyRulesMapper.identifyBusinessProcess();
        List<InsertProcessIdentifyRuleRequest> identifyBusinessProcess = Lists.newArrayList();
        for (ProcessIdentifyRulesEntity entity : identifyBusinessProcessTemp) {
            InsertProcessIdentifyRuleRequest insertProcessIdentifyRuleRequest = new InsertProcessIdentifyRuleRequest();
            BeanUtils.copyProperties(entity, insertProcessIdentifyRuleRequest);
            identifyBusinessProcess.add(insertProcessIdentifyRuleRequest);
        }
        allProcessCollectRules.setIdentifyBusinessProcess(identifyBusinessProcess);

        commonResponse.setData(allProcessCollectRules);
        return commonResponse;
    }



}
