package com.databuff.dts.receive.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.databuff.common.constants.Constant;
import com.databuff.dao.mysql.AggentManagerMapper;
import com.databuff.dao.mysql.PluginMapper;
import com.databuff.dts.receive.service.NormalService;
import com.databuff.entity.AgentEntity;
import com.databuff.entity.SaasDashboard;
import com.databuff.entity.dto.DatabuffPluginDashboardDic;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.digest.DigestUtils;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.Map;
import java.util.UUID;

/**
 * @author:TianMing
 * @date: 2024/1/4
 * @time: 10:41
 */
@Service
@Slf4j
public class NormalServiceImpl implements NormalService {
    @Resource
    private AggentManagerMapper aggentManagerMapper;
    @Resource
    private PluginMapper pluginMapper;
    @Override
    public void agentAndDashboardUpdate(Map<String, Object> headerMap, String apiKey) {
        try {
            String hostName = headerMap.get(Constant.AGENT_HOSTNAME2) == null ? "" : headerMap.get(Constant.AGENT_HOSTNAME2).toString();
            String userAgent = headerMap.get(Constant.USER_AGENT) == null ? "" : headerMap.get(Constant.USER_AGENT).toString();
            String agentVersion = "";
            //没有时间差，设为0将mysql中如果存在时差的更新过来
            Long timeDiffS = headerMap.get(Constant.TIME_DIFF) == null ? 0 : Long.parseLong(headerMap.get(Constant.TIME_DIFF).toString()) / 1000;
            String id = DigestUtils.md5Hex(apiKey + hostName);
            AgentEntity agentEntity = AgentEntity.builder()
                    .id(id)
                    .hostName(hostName)
                    .apiKey(apiKey)
                    .agentServer(userAgent)
                    .agentVersion(agentVersion)
                    .lastSynAgentTime(new Date())
                    .createTime(new Date())
                    .timeDiff(timeDiffS).build();
            int ret = aggentManagerMapper.updateAgent(agentEntity);
            if (ret < 1) {
                ret = aggentManagerMapper.inOrUpAgent(agentEntity);
            }
            // 查询是否有apiKey的仪表盘，没有则添加
            Long dashboardId = aggentManagerMapper.queryHostDashboardByApiKey(agentEntity);
            // 添加仪表盘
            if (dashboardId == null) {
                this.addDashboardByApiKey(hostName, apiKey);
            }
        } catch (Exception e) {
            log.error("agentValidate error {}", e);
        }
    }

    @Override
    public void updateAgentListInfo(AgentEntity agentEntity) {
        int ret = aggentManagerMapper.updateAgent(agentEntity);
        if (ret == 0) {
            log.warn("更新agent info 0 ,apikey={},host={}", agentEntity.getApiKey(),agentEntity.getHostName());
        }
    }

    /**
     * 添加主机仪表盘
     *
     * @param hostName
     * @param apiKey
     */
    private void addDashboardByApiKey(String hostName, String apiKey) {
        // 根据apiKey查找orgId
        long orgId = pluginMapper.getOrgIdByApiKey(apiKey);
        // 如果仪表盘title(hostname)存在，则删除仪表盘;注：修改模版后允许客户重启主机agent，即时重新创建主机与标配
        Integer dashId = pluginMapper.getDashboardIdByTitle(hostName, orgId);
        if (dashId != null) {
            pluginMapper.delDashboardByDashboardId(dashId);
            pluginMapper.delDashboardVersionByDashboardId(dashId);
            pluginMapper.delDashboardTagByDashId(dashId);
        }
        // 获取模版
        DatabuffPluginDashboardDic pluginDashboardDic = pluginMapper.getDashboardPlugDic("主机");
        String uuid = UUID.randomUUID().toString().replace("-", "");
        // 分别替换apikey、主机名、uid
        String pluginDashboard = pluginDashboardDic.getPluginDashboard()
                .replace("$@$", hostName).replace("#@#", uuid);
        // 给仪表盘添加数据源
//        String sourceName = pluginMapper.getDataSourceByOrgId(orgId);
//        JSONObject dashboardJson = this.addDataSourceForDashboard(JSON.parseObject(pluginDashboard), sourceName);
        JSONObject dashboardJson = JSON.parseObject(pluginDashboard);
        // 生成仪表盘
        long folderId = pluginMapper.getFolderId("所有主机", orgId);
        SaasDashboard dashboard = new SaasDashboard();
        dashboard.setSlug(hostName);
        dashboard.setData(dashboardJson.toJSONString());
        dashboard.setOrgId(orgId);
        dashboard.setTitle(hostName);
        Date date = new Date();
        dashboard.setCreated(date);
        dashboard.setUpdated(date);
        dashboard.setFolderId(folderId);
        dashboard.setIsFolder(0);
        dashboard.setUid(uuid);
        pluginMapper.addPlugDashboard(dashboard);
        // 保存主机仪表盘关系
        aggentManagerMapper.addHostDashboard(apiKey, hostName, dashboard.getId());
        // 保存主机仪表盘标签关系
        String pluginTags = pluginDashboardDic.getPluginTags();
        String[] split = pluginTags.split(",");
        for (String tag : split) {
            pluginMapper.addDashboardTag(dashboard.getId(), tag);
        }
    }
}
