package com.databuff.dts.util;

/**
 * Created by shuoy<PERSON>.z<PERSON> on 2020/3/3.
 */


import org.apache.http.util.Asserts;

import java.util.Stack;

/**
 * @author:TianMing
 * @date: 2020/3/10
 * @time: 17:40
 */
public class IpUtils {

    /**
     * 功能：判断一个IP是不是在一个网段下的
     * 格式：isInRange("***********", "************/22");
     */
    public static boolean inIpSegment(String ip, String cidr) {
        IpSegment ipv4Segment = getIpv4Segment(cidr);
        return ipv4Segment.inSegment(ip);
    }

    /**
     * 功能：根据位数返回IP总数
     * 格式：isIP("*************")
     */
    public static boolean isIPv4(String str) {
        Asserts.notBlank((str=str.trim()), "ip address can not be null");
        char sep = '.';
        try {
            int index;
            return (Integer.parseInt(str.substring(0, (index = str.indexOf(sep)))) & 0x700) == 0
                    && ((Integer.parseInt(str.substring(index + 1, (index = str.indexOf(sep, index + 1)))) & 0x700) == 0
                    && ((Integer.parseInt(str.substring(index + 1, (index = str.indexOf(sep, index + 1)))) & 0x700) == 0
                    && ((Integer.parseInt(str.substring(index + 1)) & 0x700) == 0)));
        } catch (Exception e){
            return false;
        }
    }

    /**
     *
     * @param mask
     * @return
     */
    public static long getIpCount(int mask) {
        mask = Math.max(mask, 0);
        mask = Math.min(32, mask);
        return (long) Math.pow(2, 32D-mask);
    }

    public static class IPv6{
        private static final String DOUBLE_COLON = "::";
        private static final String COLON = ":";
        private static final int COLON_INT_VALUE = ':';
        private static final int MAX_COLON_COUNT = 7;
        private static final String DEFAULT_IPV6 = "0000:0000:0000:0000:0000:0000:0000:0000";

        /**
         * 验证是否为IPv6格式
         * @param str
         * @return
         */
        public static boolean isIPv6(String str){
            Asserts.notBlank((str=str.trim()), "ip address can not be null");
            Stack<Character> stack = new Stack<>();
            byte doubleColonCount = 0;
            byte colonCount = 0;
            int validateColonIndex = -2;
            for (int index=0;index<str.length();index++) {
                char c = str.charAt(index);
                if (COLON_INT_VALUE != c){
                    stack.push(c);
                } else {
                    colonCount++;
                    if (colonCount > MAX_COLON_COUNT){
                        return false;
                    }
                    if (validateColonIndex==index-1){
                        if(!stack.isEmpty() || doubleColonCount!=0){
                            return false;
                        }
                        doubleColonCount++;
                        validateColonIndex = -1;
                    } else {
                        if (stack.size()>4 || stack.isEmpty()){
                            return false;
                        } else {
                            if ((validateColonIndex=index)==str.length()-1){
                                return false;
                            }
                            while (!stack.isEmpty()){
                                if (!isIllegalUnit(stack.pop())){
                                    return false;
                                }
                            }
                        }
                    }
                }
            }
            if (!stack.isEmpty()){
                if (stack.size() > 4){
                    return false;
                }
                while (!stack.isEmpty()){
                    if (!isIllegalUnit(stack.pop())){
                        return false;
                    }
                }
            }
            return doubleColonCount==0&&colonCount== MAX_COLON_COUNT || doubleColonCount==1&&colonCount< MAX_COLON_COUNT;
        }

        public static long[] toLongArray(String ipv6){
            if (!isIPv6(ipv6)){
                throw new IllegalArgumentException("not a ipv6 address");
            }
            String richIPv6 = toRichIPv6(ipv6);
            String[] split = richIPv6.split(":");
            long[] nums = new long[4];
            for (int i=0;i<nums.length; i++){
                int firstIndex = i<<1;
                String s = split[firstIndex]+split[firstIndex+1];
                long num = 0;
                for (int index = 0; index < s.length(); index++) {
                    byte n = 0;
                    switch (s.charAt(index)){
                        case '1': n=1;break;
                        case '2': n=2;break;
                        case '3': n=3;break;
                        case '4': n=4;break;
                        case '5': n=5;break;
                        case '6': n=6;break;
                        case '7': n=7;break;
                        case '8': n=8;break;
                        case '9': n=9;break;
                        case 'A':
                        case 'a': n=10;break;
                        case 'B':
                        case 'b': n=11;break;
                        case 'C':
                        case 'c': n=12;break;
                        case 'D':
                        case 'd': n=13;break;
                        case 'E':
                        case 'e': n=14;break;
                        case 'F':
                        case 'f': n=15;break;
                        default:
                    }
                    num += n *(1L <<(MAX_COLON_COUNT -index-1)*4);
                }
                nums[i] = num;
            }
            return nums;
        }

        /**
         * 简写的IPv6 转换成 非简写的IPv6
         * @param simpleIPv6
         * @return fullIPv6
         */
        public static String toRichIPv6(String simpleIPv6) {
            if (DOUBLE_COLON.equals(simpleIPv6)) {
                return DEFAULT_IPV6;
            }
            final String[] arr = new String[]{"0000", "0000", "0000", "0000", "0000", "0000", "0000", "0000"};

            if (simpleIPv6.startsWith(DOUBLE_COLON)) {
                String[] tempArr = simpleIPv6.substring(DOUBLE_COLON.length()).split(COLON);
                for (int i = 0; i < tempArr.length; i++) {
                    arr[i+ MAX_COLON_COUNT -tempArr.length] = fillUnit(tempArr[i]);
                }
            } else if (simpleIPv6.endsWith(DOUBLE_COLON)) {
                String[] tempArr = simpleIPv6.substring(0, simpleIPv6.length()-DOUBLE_COLON.length()).split(COLON);
                for (int i = 0; i < tempArr.length; i++) {
                    arr[i] = fillUnit(tempArr[i]);
                }
            } else if (simpleIPv6.contains(DOUBLE_COLON)) {
                String[] tempArr = simpleIPv6.split(DOUBLE_COLON);
                String[] tempArr0 = tempArr[0].split(COLON);
                for (int i = 0; i < tempArr0.length; i++) {
                    arr[i] = fillUnit(tempArr0[i]);
                }
                String[] tempArr1 = tempArr[1].split(COLON);
                for (int i = 0; i < tempArr1.length; i++) {
                    arr[i+ MAX_COLON_COUNT -tempArr1.length] = fillUnit(tempArr1[i]);
                }
            } else {
                String[] tempArr = simpleIPv6.split(COLON);
                for (int i = 0; i < tempArr.length; i++) {
                    arr[i] = fillUnit(tempArr[i]);
                }
            }
            return String.join(COLON, arr);
        }

        /**
         * 10进制  16进制  图形
         * 48	30	0
         * 49	31	1
         * 50	32	2
         * 51	33	3
         * 52	34	4
         * 53	35	5
         * 54	36	6
         * 55	37	7
         * 56	38	8
         * 57	39	9
         *
         * 65	41	A
         * 66	42	B
         * 67	43	C
         * 68	44	D
         * 69	45	E
         * 70	46	F
         *
         * 97	61	a
         * 98	62	b
         * 99	63	c
         * 100	64	d
         * 101	65	e
         * 102	66	f
         * @param character
         * @return
         */
        private static boolean isIllegalUnit(Character character){
            return character<58&&character>47 || character<71&&character>64 || character<103&&character>96;
        }

        /**
         * IPv6单元补充
         * @param unit
         * @return
         */
        private static String fillUnit(String unit){
            int fillLength = 4-unit.length();
            if (fillLength == 0){
                return unit;
            }
            StringBuilder stringBuilder = new StringBuilder();
            for (int i=0;i<fillLength;i++){
                stringBuilder.append("0");
            }
            return stringBuilder.append(unit).toString();
        }
    }

    /**
     * IPv4最大值
     */
    public static final long MAX_IP = 0xFFFFFFFFL;

    /**
     * ip转long值， int值最高位是符号位，不合适
     * @param ip
     * @return
     */
    public static long getLongFromIpv4String(String ip) {
        String ipTemp = ip;
        long ipLong = 0;
        int index;
        while (true){
            index=ipTemp.indexOf('.');
            if (index == -1){
                ipLong = ipLong<<8 | Long.parseLong(ipTemp);
                break;
            } else {
                ipLong = ipLong<<8 | Long.parseLong(ipTemp.substring(0, index));
                ipTemp = ipTemp.substring(index+1);
            }
        }
        return ipLong;
    }

    /**
     * 解析ip段
     * 支持格式
     *      *************/24
     *      ************-192.168.50.355
     *      ************
     * @param ips
     * @return
     */
    public static IpSegment getIpv4Segment(String ips){
        //如果包含- 则是ip范围判断
        int separator = -1;
        IpSegment ipSegment = null;
        if ((separator=ips.indexOf('-')) != -1){
            ips = ips.trim();
            ipSegment = getIpv4SegmentFromRange(ips.substring(0, separator), ips.substring(separator + 1));
        }else if((separator=ips.indexOf('/')) != -1){
            byte bit = Byte.parseByte(ips.substring(separator+1));
            long ip = getLongFromIpv4String(ips.substring(0, separator));
            ipSegment = getIpv4SegmentFromCidr(ip, bit);
        }else {
            ipSegment = getIpv4SegmentFromSingletonIp(ips);
        }
        ipSegment.setIps(ips);
        return ipSegment;
    }

    /**
     * 支持格式 ************-192.168.50.355
     * @param fromStr
     * @param toStr
     * @return
     */
    public static IpSegment getIpv4SegmentFromRange(String fromStr, String toStr){
        long from = getLongFromIpv4String(fromStr);
        long to = getLongFromIpv4String(toStr);
        return new IpSegment(null, from, to);
    }

    /**
     * cidr解析 *************/24
     * @param cidr
     * @param maskBit
     * @return
     */
    public static IpSegment getIpv4SegmentFromCidr(String cidr, byte maskBit){
        return getIpv4SegmentFromCidr(getLongFromIpv4String(cidr), maskBit);
    }

    /**
     * 根据子网掩码位数解析ip段
     * @param ip
     * @param maskBit
     * @return
     */
    public static IpSegment getIpv4SegmentFromCidr(long ip, byte maskBit){
        long mask = 0;
        for (byte i=0;i<maskBit;i++){
            mask = mask<<1 | 1;
        }
        long fromMask = mask<<(32-maskBit);
        long from = ip & fromMask;
        long to = MAX_IP ^ fromMask | from;
        return new IpSegment(null, from, to);
    }

    /**
     * 将单个IP解析成ip范围
     * @param singletonIp
     * @return
     */
    public static IpSegment getIpv4SegmentFromSingletonIp(String singletonIp){
        //都不包含直接判断ip是否相等
        long from= getLongFromIpv4String(singletonIp.trim());
        return new IpSegment(singletonIp, from, from);
    }

    public static class IpSegment {
        private String ips;
        private long from;
        private long to;


        public IpSegment(String ips, long from, long to) {
            this.ips = ips;
            this.from = from;
            this.to = to;
        }

        public IpSegment(long from, long to) {
            this(null, from, to);
        }

        public void setIps(String ips){
            this.ips = ips;
        }

        public String getIps() {
            return ips;
        }

        public long getFrom() {
            return from;
        }

        public long getTo() {
            return to;
        }

        public boolean inSegment(String ip){
            return inSegment(getLongFromIpv4String(ip));
        }

        public boolean inSegment(long ip){
            return ip<=to && ip>=from;
        }

        public boolean intersectedWith(IpSegment ipSegment, boolean strict){
            return strict ? (this.from< ipSegment.to  && this.to> ipSegment.from)
                    :(this.from<= ipSegment.to && this.to>= ipSegment.from);
        }

        @Override
        public int hashCode() {
            return (String.valueOf(ips)+from+to).hashCode();
        }

        @Override
        public boolean equals(Object obj) {
            if (!(obj instanceof IpSegment)){
                return false;
            }
            IpSegment ipSegment = (IpSegment) obj;
            return String.valueOf(ips).equals(String.valueOf(ipSegment.ips)) && from==ipSegment.from && to==ipSegment.to;
        }

        public boolean intersectedWith(IpSegment ipSegment){
            return intersectedWith(ipSegment, true);
        }
    }

    /**
     * 把long类型的Ip转为一般Ip类型：xx.xx.xx.xx
     *
     * @param ip
     * @return
     */
    public static String getIpv4FromLong(Long ip) {
        String s1 = String.valueOf(ip & 255L);
        ip = ip>>8;
        String s2 = String.valueOf(ip & 255L);
        ip = ip>>8;
        String s3 = String.valueOf(ip & 255L);
        ip = ip>>8;
        String s4 = String.valueOf(ip & 255L);
        return String.join(".", s4, s3, s2, s1);
    }

    public static long bytesToLong(byte a, byte b, byte c, byte d) {
        return int2long((((a & 0xff) << 24) | ((b & 0xff) << 16) | ((c & 0xff) << 8) | (d & 0xff)));
    }

    public static long int2long(int i) {
        long l = i & 0x7fffffffL;
        if (i < 0) {
            l |= 0x080000000L;
        }
        return l;
    }
}

