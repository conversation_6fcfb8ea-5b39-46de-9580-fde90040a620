package com.databuff.dts.util;


import lombok.extern.slf4j.Slf4j;

import java.util.HashSet;
import java.util.Set;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * iOS崩溃日志过滤工具类
 * 用于过滤和优化iOS崩溃日志中的二进制镜像信息
 * <AUTHOR>
 * @date 2024/12/04
 */
@Slf4j
public class IosTraceFilterUtil {

    /**
     * 过滤未使用的二进制镜像信息
     * 通过分析堆栈信息,仅保留实际用到的库的二进制镜像信息
     * 优化步骤:
     * 1. 从堆栈中提取使用到的库名称
     * 2. 仅保留这些库对应的二进制镜像信息
     * 3. 保持原始格式不变
     *
     * @param trace 原始的崩溃日志
     * @return 过滤后的崩溃日志
     */
    public static String filterUnusedImages(String trace) {
        // Extract libraries from stack trace
        Set<String> usedLibraries = extractLibrariesFromStack(trace);

        // Process binary images section
        StringBuilder result = new StringBuilder();
        String[] sections = trace.split("Binary Images:");

        if (sections.length != 2) {
            return trace;
        }

        // Add everything before Binary Images section
        result.append(sections[0]);
        result.append("Binary Images:\n");

        // Optimize by using Pattern matching for binary image lines

        // 用于匹配iOS二进制镜像行的正则表达式
        // 示例匹配行:
        // 0x1ca9c4000 - 0x1ca9fefe3 libsystem_kernel.dylib arm64-unknown <UUID> /path
        // 0x18f229000 - 0x190a14fff UIKitCore arm64 <UUID> /path
        // 0x10438c000 - 0x104d33fff +万岳直播带货 arm64 <UUID> /path
        Pattern imagePattern = Pattern.compile(
                ".*?"           // 非贪婪匹配任意字符
                        // 匹配地址部分 "0x1ca9c4000 - 0x1ca9fefe3 "
                        // .*? 表示尽可能少的匹配
                        + "\\s+"        // 匹配一个或多个空白字符
                        + "([^\\s]+)"   // 第一个捕获组：匹配库名称
                        // [^\\s]+ 匹配一个或多个非空白字符
                        // 可以匹配 "libsystem_kernel.dylib" 或 "+万岳直播带货"
                        + "\\s+"        // 匹配库名称后的空白字符
                        + "arm64"       // 精确匹配架构标识 "arm64"
                        + ".*"          // 匹配行末尾任意字符
                // 包括 "-unknown <UUID> /path" 等后续内容
        );

        String[] lines = sections[1].split("\n");

        int matchedCount = 0;

        for (String line : lines) {
            Matcher matcher = imagePattern.matcher(line);
            if (matcher.find()) {
                String libName = matcher.group(1);
                // Remove leading '+' if present
                if (libName.startsWith("+")) {
                    libName = libName.substring(1);
                }
                if (usedLibraries.contains(libName)) {
                    result.append(line).append("\n");
                    matchedCount++;
                }
            }
        }

        if (matchedCount != usedLibraries.size()) {
            log.warn("Binary images validation failed: found {} images but {} libraries in stack trace",
                    matchedCount, usedLibraries.size());
        }

        return result.toString();
    }


    /**
     * 从堆栈中提取使用到的库名称
     * 解析格式:
     * - libsystem_kernel.dylib 0x1ca9c8f68
     * - UIKitCore 0x1900a2530
     * - 应用名称 0x1044231dc
     *
     * 特殊处理:
     * - 去除库名前的'+'号
     * - 仅处理Call Backtrace部分的内容
     * - 使用正则表达式精确匹配库名和地址格式
     *
     * @param trace 原始崩溃日志
     * @return 使用到的库名称集合
     */
    private static Set<String> extractLibrariesFromStack(String trace) {
        Set<String> libraries = new HashSet<>();

        // 用于匹配iOS堆栈中的库名称和地址的正则表达式
        // 示例匹配:
        // libsystem_kernel.dylib          0x1ca9c8f68
        // UIKitCore                       0x1900a2530
        // 万岳直播带货                    0x1044231dc
        Pattern pattern = Pattern.compile(
                "([^\\s]+)"      // 第一个捕获组：匹配一个或多个非空白字符(库名称)
                        // [^\\s] 表示非空白字符,可以匹配"libsystem_kernel.dylib"或"UIKitCore"
                        // + 表示一个或多个字符
                        + "\\s+"         // 匹配中间的空白字符(空格、制表符等)
                        // 示例中库名和地址之间的空白部分
                        + "0x"           // 匹配十六进制地址的前缀"0x"
                        + "[\\da-f]+"    // 匹配十六进制数字部分
                // \\d 匹配数字0-9
                // a-f 匹配小写字母a到f
                // + 表示匹配一个或多个字符
                // 例如匹配"1ca9c8f68"这样的地址
        );


        int stackStart = trace.indexOf("Call Backtrace of");
        int binaryImagesIndex = trace.indexOf("Binary Images:");

        if (stackStart < 0 || binaryImagesIndex < 0) {
            return libraries;
        }

        String stackSection = trace.substring(stackStart, binaryImagesIndex);
        String[] lines = stackSection.split("\n");

        for (String line : lines) {
            Matcher matcher = pattern.matcher(line.trim());
            if (matcher.find()) {
                String lib = matcher.group(1);
                if (lib.startsWith("+")) {
                    lib = lib.substring(1);
                }
                libraries.add(lib);
            }
        }

        return libraries;
    }
}
