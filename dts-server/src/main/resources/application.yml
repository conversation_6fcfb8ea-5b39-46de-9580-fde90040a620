server:
  port: 18111

# actuator配置
management:
  endpoints:
    web:
      exposure:
        include: "*"
        exclude: "env,beans" # 开启所有端点，但是排除env和beans端点

spring:
  main:
    allow-circular-references: true
  cloud:
    compatibility-verifier:
      enabled: false
  # 上传最大文件限制
  servlet:
    multipart:
      max-file-size: 10GB
      max-request-size: 10GB
  jackson:
    date-format: yyyy-MM-dd HH:mm:ss
    time-zone: GMT+8
  datasource:
    name: Databuff
    url: ********************************************************************************************************************************************
    username: root
    password: 234*(sdlj12
    # 使用Druid数据源
    type: com.alibaba.druid.pool.DruidDataSource
    driver-class-name: com.mysql.jdbc.Driver
    druid:
      filters: stat
      maxActive: 20
      initialSize: 1
      maxWait: 60000
      minIdle: 1
      timeBetweenEvictionRunsMillis: 60000
      minEvictableIdleTimeMillis: 300000
      validationQuery: select 'x'
      testWhileIdle: true
      testOnBorrow: false
      testOnReturn: false
      poolPreparedStatements: true
      maxOpenPreparedStatements: 20
      stat-view-servlet:
        # 是否启用StatViewServlet(监控页面),默认true-启动，false-不启动
        enabled: false
        url-pattern: '/druid/*'
        # 禁用HTML页面上的"Reset All"功能
        reset-enable: false
  olap:
    driver-class-name: com.mysql.jdbc.Driver
    url: ************************************
    feIpPorts: starrocks:8040
    username: root
    password: Databuff@123
    openMonitor: true
    maxFilterRatio: 1
    # redis
  redis:
    host: redis
    port: 6379
    timeout: 10000
    password: Databuff@123+
    jedis:
      pool:
        max-idle: 8
        min-idle: 0
        max-wait: -1
        max-active: 200
  resources:
    add-mappings: false
  mvc:
    throw-exception-if-no-handler-found: true

  # 使用aop操作日志
  aop:
    auto: true
    proxy-target-class: true

# mysql config
mybatis-plus:
  mapper-locations: classpath*:mappers/*.xml
  configuration:
    map-underscore-to-camel-case: true
    cache-enabled: false
    #log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
    log-impl: org.apache.ibatis.logging.nologging.NoLoggingImpl
  global-config:
    refresh: true
    db-config:
      id-type: auto
      field-strategy: not_empty
      db-column-underline: true
      logic-delete-value: 1
      logic-not-delete-value: 0
      db-type: mysql

pagehelper:
  params: count=countSql
  # 指定分页插件使用哪种方言
  helper-dialect: mysql
  # 分页合理化参数 pageNum<=0时会查询第一页 pageNum>pages(超过总数时) 会查询最后一页
  reasonable: 'true'
  support-methods-arguments: 'false'

mapper:
  # 通用Mapper的insertSelective和updateByPrimaryKeySelective中是否判断字符串类型!=''
  not-empty: true

#日志级别从低到高分为TRACE < DEBUG < INFO < WARN < ERROR < FATAL，如果设置为WARN，则低于WARN的信息都不会输出
logging:
  config: classpath:logback-spring.xml
  path: /usr/local/log
  level:
    root: info
  level.com.databuff.dts: info

jasypt:
  encryptor:
    password: 123456   #开发环境密码

kafka:
  bootstrap-servers: kafka:9092
  consumer:
    auto-commit-interval: 100
    auto-offset-reset: earliest
    enable-auto-commit: true
    group-id: dc-consumer-group-databuff-receive
    key-deserializer: org.apache.kafka.common.serialization.StringDeserializer
    value-deserializer: org.apache.kafka.common.serialization.StringDeserializer
  listener:
     concurrency: 1
  producer:
     batch-size: 64000
     buffer-memory: 33554432
     linger-ms: 100
     key-serializer: org.apache.kafka.common.serialization.StringSerializer
     retries: 0
     value-serializer: org.apache.kafka.common.serialization.StringSerializer
     max-request-size: 20000000

tsdb:
  # 默认数据库配置，支持集群，适合读写,优先读写
  db: moredb
  # 连接配置（同时用于读写，支持逗号分隔的集群配置）
  url: moredb:2890
  api: http://moredb:8080/api
  user: databuff
  password: databuff666
  duration: 30d
  # 生产环境连接池优化配置
  max-total: 100
  max-idle: 50
  min-idle: 10
  max-wait-millis: 3000
  soft-min-evictable-idle-time-millis: 600000
  test-on-borrow: false
  test-while-idle: true
  time-between-eviction-runs-millis: 120000
  queryTimeout: 60000
  shard: 2
  replication: 1
  interval: 60

  # moredb数据库，支持集群，只写不读
  #  moredb:
  #    # 连接配置（支持逗号分隔的集群配置）
  #    url: moredb:2890
  #    api: http://moredb:8080/api
  #    user: databuff
  #    password: databuff666

  # opengemini数据库，支持集群，只写不读
  opengemini:
    # 连接配置（支持逗号分隔的集群配置）
    url: opengemini:8086
    api: http://opengemini:8086
    user: admin
    password: password123

#版本
version: "DataBuff|v2.9.1"
#渠道: 官方000
channel: "000"
# logo路径
logoPath:

# AccessToken过期时间-5分钟-5*60(秒为单位)
accessTokenExpireTime: 30240000
# RefreshToken过期时间-30分钟-30*60(秒为单位)
refreshTokenExpireTime: 3600
# dczw缓存过期时间-5分钟-5*60(秒为单位)(一般设置与AccessToken过期时间一致)
shiroCacheExpireTime: 3600
# 服务器与agent可容忍误差范围10s(10000ms)
timeDiffRange: 10000

# 日志数据接收，拒绝接收类型 alert，critical，error，warn，notice，info，debug
logReceive:
  refused:

#  采样率 1为100% 0.1为10%
samplingRate:
  trace: 1
  metric: 1


# profiling过滤器配置
profiling:
  method:
    patterns:
      contains:
        - $$
        - $Proxy
        - $ByteBuddy$
      startsWith:
        - /usr
        - JVM_
        - Java_java
        - __

#定时任务
scheduler:
  enabled:
    #管理域
    domainManager: false
    #业务观测
    bizInfo: false
